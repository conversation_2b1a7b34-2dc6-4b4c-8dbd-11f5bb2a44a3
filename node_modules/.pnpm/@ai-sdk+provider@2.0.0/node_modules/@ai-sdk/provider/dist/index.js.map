{"version": 3, "sources": ["../src/index.ts", "../src/errors/ai-sdk-error.ts", "../src/errors/api-call-error.ts", "../src/errors/empty-response-body-error.ts", "../src/errors/get-error-message.ts", "../src/errors/invalid-argument-error.ts", "../src/errors/invalid-prompt-error.ts", "../src/errors/invalid-response-data-error.ts", "../src/errors/json-parse-error.ts", "../src/errors/load-api-key-error.ts", "../src/errors/load-setting-error.ts", "../src/errors/no-content-generated-error.ts", "../src/errors/no-such-model-error.ts", "../src/errors/too-many-embedding-values-for-call-error.ts", "../src/errors/type-validation-error.ts", "../src/errors/unsupported-functionality-error.ts", "../src/json-value/is-json.ts"], "sourcesContent": ["export * from './embedding-model/index';\nexport * from './errors/index';\nexport * from './image-model/index';\nexport * from './json-value/index';\nexport * from './language-model-middleware/index';\nexport * from './language-model/index';\nexport * from './provider/index';\nexport * from './shared/index';\nexport * from './speech-model/index';\nexport * from './transcription-model/index';\n\nexport type { JSONSchema7, JSONSchema7Definition } from 'json-schema';\n", "/**\n * Symbol used for identifying AI SDK Error instances.\n * Enables checking if an error is an instance of AISDKError across package versions.\n */\nconst marker = 'vercel.ai.error';\nconst symbol = Symbol.for(marker);\n\n/**\n * Custom error class for AI SDK related errors.\n * @extends Error\n */\nexport class AISDKError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  /**\n   * The underlying cause of the error, if any.\n   */\n  readonly cause?: unknown;\n\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name,\n    message,\n    cause,\n  }: {\n    name: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = name;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is AISDKError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  protected static hasMarker(error: unknown, marker: string): boolean {\n    const markerSymbol = Symbol.for(marker);\n    return (\n      error != null &&\n      typeof error === 'object' &&\n      markerSymbol in error &&\n      typeof error[markerSymbol] === 'boolean' &&\n      error[markerSymbol] === true\n    );\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_APICallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class APICallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is APICallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_EmptyResponseBodyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class EmptyResponseBodyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is EmptyResponseBodyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A function argument is invalid.\n */\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly argument: string;\n\n  constructor({\n    message,\n    cause,\n    argument,\n  }: {\n    argument: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.argument = argument;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidPromptError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A prompt is invalid. This error should be thrown by providers when they cannot\n * process a prompt.\n */\nexport class InvalidPromptError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly prompt: unknown;\n\n  constructor({\n    prompt,\n    message,\n    cause,\n  }: {\n    prompt: unknown;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message: `Invalid prompt: ${message}`, cause });\n\n    this.prompt = prompt;\n  }\n\n  static isInstance(error: unknown): error is InvalidPromptError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidResponseDataError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Server returned a response with invalid data content.\n * This should be thrown by providers when they cannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is InvalidResponseDataError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_JSONParseError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n// TODO v5: rename to ParseError\nexport class JSONParseError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly text: string;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super({\n      name,\n      message:\n        `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.text = text;\n  }\n\n  static isInstance(error: unknown): error is JSONParseError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadAPIKeyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadAPIKeyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadAPIKeyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadSettingError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadSettingError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadSettingError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoContentGeneratedError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is NoContentGeneratedError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoSuchModelError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchModelError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly modelId: string;\n  readonly modelType:\n    | 'languageModel'\n    | 'textEmbeddingModel'\n    | 'imageModel'\n    | 'transcriptionModel'\n    | 'speechModel';\n\n  constructor({\n    errorName = name,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    errorName?: string;\n    modelId: string;\n    modelType:\n      | 'languageModel'\n      | 'textEmbeddingModel'\n      | 'imageModel'\n      | 'transcriptionModel'\n      | 'speechModel';\n    message?: string;\n  }) {\n    super({ name: errorName, message });\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isInstance(error: unknown): error is NoSuchModelError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_TooManyEmbeddingValuesForCallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TooManyEmbeddingValuesForCallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super({\n      name,\n      message:\n        `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    });\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInstance(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_TypeValidationError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TypeValidationError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly value: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super({\n      name,\n      message:\n        `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is TypeValidationError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause,\n  }: {\n    value: unknown;\n    cause: unknown;\n  }): TypeValidationError {\n    return TypeValidationError.isInstance(cause) && cause.value === value\n      ? cause\n      : new TypeValidationError({ value, cause });\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_UnsupportedFunctionalityError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class UnsupportedFunctionalityError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly functionality: string;\n\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`,\n  }: {\n    functionality: string;\n    message?: string;\n  }) {\n    super({ name, message });\n    this.functionality = functionality;\n  }\n\n  static isInstance(error: unknown): error is UnsupportedFunctionalityError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { JSONArray, JSONObject, JSONValue } from './json-value';\n\nexport function isJSONValue(value: unknown): value is JSONValue {\n  if (\n    value === null ||\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean'\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n\n  if (typeof value === 'object') {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is JSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is JSONObject {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    )\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACIA,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,IAAI,MAAM;AALhC;AAWO,IAAM,cAAN,MAAM,oBAAmB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBpC,YAAY;AAAA,IACV,MAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,OAAO;AAxBf,SAAkB,MAAU;AA0B1B,SAAK,OAAOA;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,WAAW,OAAqC;AACrD,WAAO,YAAW,UAAU,OAAO,MAAM;AAAA,EAC3C;AAAA,EAEA,OAAiB,UAAU,OAAgBC,UAAyB;AAClE,UAAM,eAAe,OAAO,IAAIA,QAAM;AACtC,WACE,SAAS,QACT,OAAO,UAAU,YACjB,gBAAgB,SAChB,OAAO,MAAM,YAAY,MAAM,aAC/B,MAAM,YAAY,MAAM;AAAA,EAE5B;AACF;AAjDoB;AADb,IAAM,aAAN;;;ACTP,IAAM,OAAO;AACb,IAAMC,UAAS,mBAAmB,IAAI;AACtC,IAAMC,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,eAAN,cAA2B,WAAW;AAAA,EAa3C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,cAAc,SACzB,eAAe;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,cAAc;AAAA;AAAA,IAClB;AAAA,EACF,GAUG;AACD,UAAM,EAAE,MAAM,SAAS,MAAM,CAAC;AArChC,SAAkBA,OAAU;AAuC1B,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,WAAW,OAAuC;AACvD,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AAnDoBE,MAAAD;;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,yBAAN,cAAqC,WAAW;AAAA;AAAA,EAGrD,YAAY,EAAE,UAAU,sBAAsB,IAA0B,CAAC,GAAG;AAC1E,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAHzB,SAAkBG,OAAU;AAAA,EAI5B;AAAA,EAEA,OAAO,WAAW,OAAiD;AACjE,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AAToBE,MAAAD;;;ACPb,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;AAAA,EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;;;ACZA,IAAME,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AASO,IAAM,uBAAN,cAAmC,WAAW;AAAA,EAKnD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,EAAE,MAAAH,OAAM,SAAS,MAAM,CAAC;AAbhC,SAAkBG,OAAU;AAe1B,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,OAAO,WAAW,OAA+C;AAC/D,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AArBoBE,MAAAD;;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,qBAAN,cAAiC,WAAW;AAAA,EAKjD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,EAAE,MAAAH,OAAM,SAAS,mBAAmB,OAAO,IAAI,MAAM,CAAC;AAb9D,SAAkBG,OAAU;AAe1B,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,OAAO,WAAW,OAA6C;AAC7D,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AArBoBE,MAAAD;;;ACTpB,IAAME,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,2BAAN,cAAuC,WAAW;AAAA,EAKvD,YAAY;AAAA,IACV;AAAA,IACA,UAAU,0BAA0B,KAAK,UAAU,IAAI,CAAC;AAAA,EAC1D,GAGG;AACD,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAXzB,SAAkBG,OAAU;AAa1B,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,WAAW,OAAmD;AACnE,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AAnBoBE,MAAAD;;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AALhC,IAAAE;AAQO,IAAM,iBAAN,cAA6B,WAAW;AAAA,EAK7C,YAAY,EAAE,MAAM,MAAM,GAAqC;AAC7D,UAAM;AAAA,MACJ,MAAAH;AAAA,MACA,SACE,8BACS,IAAI;AAAA,iBACK,gBAAgB,KAAK,CAAC;AAAA,MAC1C;AAAA,IACF,CAAC;AAZH,SAAkBG,OAAU;AAc1B,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,WAAW,OAAyC;AACzD,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AApBoBE,MAAAD;;;ACPpB,IAAME,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,kBAAN,cAA8B,WAAW;AAAA;AAAA,EAG9C,YAAY,EAAE,QAAQ,GAAwB;AAC5C,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAHzB,SAAkBG,OAAU;AAAA,EAI5B;AAAA,EAEA,OAAO,WAAW,OAA0C;AAC1D,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AAToBE,MAAAD;;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;AAAA;AAAA,EAG/C,YAAY,EAAE,QAAQ,GAAwB;AAC5C,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAHzB,SAAkBG,OAAU;AAAA,EAI5B;AAAA,EAEA,OAAO,WAAW,OAA2C;AAC3D,WAAO,WAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AAToBE,MAAAD;;;ACLpB,IAAME,QAAO;AACb,IAAMC,WAAS,mBAAmBD,KAAI;AACtC,IAAME,WAAS,OAAO,IAAID,QAAM;AAJhC,IAAAE;AASO,IAAM,0BAAN,cAAsC,WAAW;AAAA;AAAA,EAGtD,YAAY;AAAA,IACV,UAAU;AAAA,EACZ,IAA0B,CAAC,GAAG;AAC5B,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AALzB,SAAkBG,QAAU;AAAA,EAM5B;AAAA,EAEA,OAAO,WAAW,OAAkD;AAClE,WAAO,WAAW,UAAU,OAAOF,QAAM;AAAA,EAC3C;AACF;AAXoBE,OAAAD;;;ACRpB,IAAME,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAME,WAAS,OAAO,IAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;AAAA,EAW/C,YAAY;AAAA,IACV,YAAYH;AAAA,IACZ;AAAA,IACA;AAAA,IACA,UAAU,WAAW,SAAS,KAAK,OAAO;AAAA,EAC5C,GAUG;AACD,UAAM,EAAE,MAAM,WAAW,QAAQ,CAAC;AA1BpC,SAAkBG,QAAU;AA4B1B,SAAK,UAAU;AACf,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,OAAO,WAAW,OAA2C;AAC3D,WAAO,WAAW,UAAU,OAAOF,QAAM;AAAA,EAC3C;AACF;AAnCoBE,OAAAD;;;ACLpB,IAAME,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAME,WAAS,OAAO,IAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,qCAAN,cAAiD,WAAW;AAAA,EAQjE,YAAY,SAKT;AACD,UAAM;AAAA,MACJ,MAAAH;AAAA,MACA,SACE,oDACO,QAAQ,QAAQ,WAAW,QAAQ,OAAO,0BAC9C,QAAQ,oBAAoB,yBAAyB,QAAQ,OAAO,MAAM;AAAA,IACjF,CAAC;AAnBH,SAAkBG,QAAU;AAqB1B,SAAK,WAAW,QAAQ;AACxB,SAAK,UAAU,QAAQ;AACvB,SAAK,uBAAuB,QAAQ;AACpC,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EAEA,OAAO,WACL,OAC6C;AAC7C,WAAO,WAAW,UAAU,OAAOF,QAAM;AAAA,EAC3C;AACF;AAhCoBE,OAAAD;;;ACJpB,IAAME,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAME,WAAS,OAAO,IAAID,QAAM;AALhC,IAAAE;AAOO,IAAM,uBAAN,MAAM,6BAA4B,WAAW;AAAA,EAKlD,YAAY,EAAE,OAAO,MAAM,GAAuC;AAChE,UAAM;AAAA,MACJ,MAAAH;AAAA,MACA,SACE,kCACU,KAAK,UAAU,KAAK,CAAC;AAAA,iBACb,gBAAgB,KAAK,CAAC;AAAA,MAC1C;AAAA,IACF,CAAC;AAZH,SAAkBG,QAAU;AAc1B,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,WAAW,OAA8C;AAC9D,WAAO,WAAW,UAAU,OAAOF,QAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,KAAK;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAGwB;AACtB,WAAO,qBAAoB,WAAW,KAAK,KAAK,MAAM,UAAU,QAC5D,QACA,IAAI,qBAAoB,EAAE,OAAO,MAAM,CAAC;AAAA,EAC9C;AACF;AA1CoBE,OAAAD;AADb,IAAM,sBAAN;;;ACLP,IAAME,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAME,WAAS,OAAO,IAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,gCAAN,cAA4C,WAAW;AAAA,EAK5D,YAAY;AAAA,IACV;AAAA,IACA,UAAU,IAAI,aAAa;AAAA,EAC7B,GAGG;AACD,UAAM,EAAE,MAAAH,QAAM,QAAQ,CAAC;AAXzB,SAAkBG,QAAU;AAY1B,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEA,OAAO,WAAW,OAAwD;AACxE,WAAO,WAAW,UAAU,OAAOF,QAAM;AAAA,EAC3C;AACF;AAlBoBE,OAAAD;;;ACLb,SAAS,YAAY,OAAoC;AAC9D,MACE,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,UAAU,YACjB,OAAO,UAAU,WACjB;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,MAAM,WAAW;AAAA,EAChC;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,OAAO,QAAQ,KAAK,EAAE;AAAA,MAC3B,CAAC,CAAC,KAAK,GAAG,MAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;AAAA,IAC5D;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,YAAY,OAAoC;AAC9D,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,WAAW;AACxD;AAEO,SAAS,aAAa,OAAqC;AAChE,SACE,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,QAAQ,KAAK,EAAE;AAAA,IACpB,CAAC,CAAC,KAAK,GAAG,MAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;AAAA,EAC5D;AAEJ;", "names": ["name", "marker", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a"]}