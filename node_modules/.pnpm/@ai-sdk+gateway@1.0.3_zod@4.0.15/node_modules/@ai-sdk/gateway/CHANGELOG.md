# @ai-sdk/gateway

## 1.0.3

### Patch Changes

- 893aed6: feat (provider/gateway): add anthropic claude 4.1 opus model id

## 1.0.2

### Patch Changes

- 444df49: feat (provider/gateway): update model ids

## 1.0.1

### Patch Changes

- 028fb9c: refactor(provider/gateway): Cleanup old gateway-embedding-options file
- 6331826: feat(provider/gateway): Hide Cohere embedding models with no pricing info
- Updated dependencies [90d212f]
  - @ai-sdk/provider-utils@3.0.1

## 1.0.0

### Patch Changes

- 9e16bfd: feat (provider/gateway): update model ids
- 0477a13: feat (provider/gateway): Add OpenAI embedding support
- 26b6dd0: feat (providers/gateway): include deployment and request id
- 30ab1de: feat (provider/gateway): add grok-4 model id
- e2aceaf: feat: add raw chunk support
- 97fedf9: feat (providers/gateway): include description and pricing info in model list
- c91586a: chore (providers/gateway): update language model ids
- 3cbcbb7: feat (providers/gateway): share common gateway error transform logic
- fedb55e: feat (provider/gateway): add z.ai and glm-4.5 models
- 6c2c708: feat (providers/gateway): initial gateway provider
- 721775e: feat(provider/gateway): Generate new Gateway embedding model settings file
- 70ebead: feat (provider/gateway): add qwen3 coder model id
- f3639fa: feat (providers/gateway): improve oidc api key client auth flow
- 8bd3624: feat (provider/gateway): update model ids to include vercel
- c145d62: feat (providers/gateway): add createGateway shorthand alias for createGatewayProvider
- f77bc38: chore (providers/gateway): update language model ids
- 989ac75: chore (providers/gateway): update chat model ids
- 7742ba3: feat (providers/gateway): add gateway error types with error detail
- c190907: fix (provider/gateway): use zod v4
- d1a034f: feature: using Zod 4 for internal stuff
- d454e4b: fix (providers/gateway): fix timestamp error when streaming objects
- cf1e00e: feat (provider/gateway): add devstral model id
- cc21603: feat (provider/gateway): Add AI Gateway provider options (ordering)
- 205077b: fix: improve Zod compatibility
- e001ea1: fix (provider/gateway): remove unnecessary 'x-' prefix on auth method header
- 27deb4d: feat (provider/gateway): Add providerMetadata to embeddings response
- Updated dependencies [a571d6e]
- Updated dependencies [742b7be]
- Updated dependencies [e7fcc86]
- Updated dependencies [7cddb72]
- Updated dependencies [ccce59b]
- Updated dependencies [e2b9e4b]
- Updated dependencies [95857aa]
- Updated dependencies [45c1ea2]
- Updated dependencies [6f6bb89]
- Updated dependencies [060370c]
- Updated dependencies [dc714f3]
- Updated dependencies [b5da06a]
- Updated dependencies [d1a1aa1]
- Updated dependencies [63f9e9b]
- Updated dependencies [5d142ab]
- Updated dependencies [d5f588f]
- Updated dependencies [e025824]
- Updated dependencies [0571b98]
- Updated dependencies [b6b43c7]
- Updated dependencies [4fef487]
- Updated dependencies [48d257a]
- Updated dependencies [0c0c0b3]
- Updated dependencies [0d2c085]
- Updated dependencies [40acf9b]
- Updated dependencies [9222aeb]
- Updated dependencies [e2aceaf]
- Updated dependencies [411e483]
- Updated dependencies [8ba77a7]
- Updated dependencies [7b3ae3f]
- Updated dependencies [a166433]
- Updated dependencies [26735b5]
- Updated dependencies [443d8ec]
- Updated dependencies [a8c8bd5]
- Updated dependencies [abf9a79]
- Updated dependencies [14c9410]
- Updated dependencies [e86be6f]
- Updated dependencies [9bf7291]
- Updated dependencies [2e13791]
- Updated dependencies [9f95b35]
- Updated dependencies [66962ed]
- Updated dependencies [0d06df6]
- Updated dependencies [472524a]
- Updated dependencies [dd3ff01]
- Updated dependencies [d9c98f4]
- Updated dependencies [05d2819]
- Updated dependencies [9301f86]
- Updated dependencies [0a87932]
- Updated dependencies [c4a2fec]
- Updated dependencies [957b739]
- Updated dependencies [79457bd]
- Updated dependencies [a3f768e]
- Updated dependencies [7435eb5]
- Updated dependencies [8aa9e20]
- Updated dependencies [4617fab]
- Updated dependencies [ac34802]
- Updated dependencies [0054544]
- Updated dependencies [cb68df0]
- Updated dependencies [ad80501]
- Updated dependencies [68ecf2f]
- Updated dependencies [9e9c809]
- Updated dependencies [32831c6]
- Updated dependencies [6dc848c]
- Updated dependencies [6b98118]
- Updated dependencies [d0f9495]
- Updated dependencies [63d791d]
- Updated dependencies [87b828f]
- Updated dependencies [3f2f00c]
- Updated dependencies [bfdca8d]
- Updated dependencies [0ff02bb]
- Updated dependencies [7979f7f]
- Updated dependencies [39a4fab]
- Updated dependencies [44f4aba]
- Updated dependencies [9bd5ab5]
- Updated dependencies [57edfcb]
- Updated dependencies [faf8446]
- Updated dependencies [7ea4132]
- Updated dependencies [d1a034f]
- Updated dependencies [5c56081]
- Updated dependencies [fd65bc6]
- Updated dependencies [023ba40]
- Updated dependencies [ea7a7c9]
- Updated dependencies [26535e0]
- Updated dependencies [e030615]
- Updated dependencies [5e57fae]
- Updated dependencies [393138b]
- Updated dependencies [c57e248]
- Updated dependencies [88a8ee5]
- Updated dependencies [41fa418]
- Updated dependencies [205077b]
- Updated dependencies [71f938d]
- Updated dependencies [3795467]
- Updated dependencies [28a5ed5]
- Updated dependencies [7182d14]
- Updated dependencies [c1e6647]
- Updated dependencies [1766ede]
- Updated dependencies [811dff3]
- Updated dependencies [f10304b]
- Updated dependencies [dd5fd43]
- Updated dependencies [33f4a6a]
- Updated dependencies [383cbfa]
- Updated dependencies [27deb4d]
- Updated dependencies [c4df419]
  - @ai-sdk/provider-utils@3.0.0
  - @ai-sdk/provider@2.0.0

## 1.0.0-beta.19

### Patch Changes

- 721775e: feat(provider/gateway): Generate new Gateway embedding model settings file
- Updated dependencies [88a8ee5]
  - @ai-sdk/provider-utils@3.0.0-beta.10

## 1.0.0-beta.18

### Patch Changes

- 27deb4d: feat (provider/gateway): Add providerMetadata to embeddings response
- Updated dependencies [27deb4d]
  - @ai-sdk/provider@2.0.0-beta.2
  - @ai-sdk/provider-utils@3.0.0-beta.9

## 1.0.0-beta.17

### Patch Changes

- Updated dependencies [dd5fd43]
  - @ai-sdk/provider-utils@3.0.0-beta.8

## 1.0.0-beta.16

### Patch Changes

- fedb55e: feat (provider/gateway): add z.ai and glm-4.5 models

## 1.0.0-beta.15

### Patch Changes

- Updated dependencies [e7fcc86]
  - @ai-sdk/provider-utils@3.0.0-beta.7

## 1.0.0-beta.14

### Patch Changes

- Updated dependencies [ac34802]
  - @ai-sdk/provider-utils@3.0.0-beta.6

## 1.0.0-beta.13

### Patch Changes

- 0477a13: feat (provider/gateway): Add OpenAI embedding support
- cf1e00e: feat (provider/gateway): add devstral model id
- cc21603: feat (provider/gateway): Add AI Gateway provider options (ordering)

## 1.0.0-beta.12

### Patch Changes

- 70ebead: feat (provider/gateway): add qwen3 coder model id

## 1.0.0-beta.11

### Patch Changes

- 8bd3624: feat (provider/gateway): update model ids to include vercel
- e001ea1: fix (provider/gateway): remove unnecessary 'x-' prefix on auth method header

## 1.0.0-beta.10

### Patch Changes

- Updated dependencies [57edfcb]
- Updated dependencies [383cbfa]
  - @ai-sdk/provider-utils@3.0.0-beta.5

## 1.0.0-beta.9

### Patch Changes

- 205077b: fix: improve Zod compatibility
- Updated dependencies [205077b]
  - @ai-sdk/provider-utils@3.0.0-beta.4

## 1.0.0-beta.8

### Patch Changes

- Updated dependencies [05d2819]
  - @ai-sdk/provider-utils@3.0.0-beta.3

## 1.0.0-beta.7

### Patch Changes

- c190907: fix (provider/gateway): use zod v4

## 1.0.0-beta.6

### Patch Changes

- 9e16bfd: feat (provider/gateway): update model ids

## 1.0.0-beta.5

### Patch Changes

- 30ab1de: feat (provider/gateway): add grok-4 model id

## 1.0.0-beta.4

### Patch Changes

- 97fedf9: feat (providers/gateway): include description and pricing info in model list

## 1.0.0-beta.3

### Patch Changes

- f3639fa: feat (providers/gateway): improve oidc api key client auth flow
- d454e4b: fix (providers/gateway): fix timestamp error when streaming objects

## 1.0.0-beta.2

### Patch Changes

- c91586a: chore (providers/gateway): update language model ids
- d1a034f: feature: using Zod 4 for internal stuff
- Updated dependencies [0571b98]
- Updated dependencies [39a4fab]
- Updated dependencies [d1a034f]
  - @ai-sdk/provider-utils@3.0.0-beta.2

## 1.0.0-beta.1

### Patch Changes

- f77bc38: chore (providers/gateway): update language model ids
- Updated dependencies [742b7be]
- Updated dependencies [7cddb72]
- Updated dependencies [ccce59b]
- Updated dependencies [e2b9e4b]
- Updated dependencies [45c1ea2]
- Updated dependencies [e025824]
- Updated dependencies [0d06df6]
- Updated dependencies [472524a]
- Updated dependencies [dd3ff01]
- Updated dependencies [7435eb5]
- Updated dependencies [cb68df0]
- Updated dependencies [bfdca8d]
- Updated dependencies [44f4aba]
- Updated dependencies [023ba40]
- Updated dependencies [5e57fae]
- Updated dependencies [71f938d]
- Updated dependencies [28a5ed5]
  - @ai-sdk/provider@2.0.0-beta.1
  - @ai-sdk/provider-utils@3.0.0-beta.1

## 1.0.0-alpha.15

### Patch Changes

- c145d62: feat (providers/gateway): add createGateway shorthand alias for createGatewayProvider
- Updated dependencies [48d257a]
- Updated dependencies [8ba77a7]
  - @ai-sdk/provider@2.0.0-alpha.15
  - @ai-sdk/provider-utils@3.0.0-alpha.15

## 1.0.0-alpha.14

### Patch Changes

- Updated dependencies [b5da06a]
- Updated dependencies [63f9e9b]
- Updated dependencies [2e13791]
  - @ai-sdk/provider@2.0.0-alpha.14
  - @ai-sdk/provider-utils@3.0.0-alpha.14

## 1.0.0-alpha.13

### Patch Changes

- Updated dependencies [68ecf2f]
  - @ai-sdk/provider@2.0.0-alpha.13
  - @ai-sdk/provider-utils@3.0.0-alpha.13

## 1.0.0-alpha.12

### Patch Changes

- e2aceaf: feat: add raw chunk support
- Updated dependencies [e2aceaf]
  - @ai-sdk/provider@2.0.0-alpha.12
  - @ai-sdk/provider-utils@3.0.0-alpha.12

## 1.0.0-alpha.11

### Patch Changes

- Updated dependencies [c1e6647]
  - @ai-sdk/provider@2.0.0-alpha.11
  - @ai-sdk/provider-utils@3.0.0-alpha.11

## 1.0.0-alpha.10

### Patch Changes

- Updated dependencies [c4df419]
  - @ai-sdk/provider@2.0.0-alpha.10
  - @ai-sdk/provider-utils@3.0.0-alpha.10

## 1.0.0-alpha.9

### Patch Changes

- 26b6dd0: feat (providers/gateway): include deployment and request id
- Updated dependencies [811dff3]
  - @ai-sdk/provider@2.0.0-alpha.9
  - @ai-sdk/provider-utils@3.0.0-alpha.9

## 1.0.0-alpha.8

### Patch Changes

- 3cbcbb7: feat (providers/gateway): share common gateway error transform logic
- 989ac75: chore (providers/gateway): update chat model ids
- 7742ba3: feat (providers/gateway): add gateway error types with error detail
- Updated dependencies [4fef487]
- Updated dependencies [9222aeb]
  - @ai-sdk/provider-utils@3.0.0-alpha.8
  - @ai-sdk/provider@2.0.0-alpha.8

## 1.0.0-alpha.7

### Patch Changes

- Updated dependencies [5c56081]
  - @ai-sdk/provider@2.0.0-alpha.7
  - @ai-sdk/provider-utils@3.0.0-alpha.7

## 1.0.0-alpha.6

### Patch Changes

- 6c2c708: feat (providers/gateway): initial gateway provider
- Updated dependencies [0d2c085]
  - @ai-sdk/provider@2.0.0-alpha.6
  - @ai-sdk/provider-utils@3.0.0-alpha.6
