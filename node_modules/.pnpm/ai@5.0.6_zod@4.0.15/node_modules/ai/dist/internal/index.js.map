{"version": 3, "sources": ["../../internal/index.ts", "../../src/prompt/convert-to-language-model-prompt.ts", "../../src/util/detect-media-type.ts", "../../src/util/download-error.ts", "../../src/util/download.ts", "../../src/prompt/data-content.ts", "../../src/prompt/split-data-url.ts", "../../src/prompt/invalid-message-role-error.ts", "../../src/prompt/prepare-tools-and-tool-choice.ts", "../../src/util/is-non-empty-object.ts", "../../src/prompt/standardize-prompt.ts", "../../src/prompt/message.ts", "../../src/types/provider-metadata.ts", "../../src/types/json-value.ts", "../../src/prompt/content-part.ts", "../../src/error/invalid-argument-error.ts", "../../src/prompt/prepare-call-settings.ts", "../../src/util/retry-with-exponential-backoff.ts", "../../src/util/retry-error.ts", "../../src/util/prepare-retries.ts"], "sourcesContent": ["// internal re-exports\nexport { convertAsyncIteratorToReadableStream } from '@ai-sdk/provider-utils';\n\n// internal\nexport { convertToLanguageModelPrompt } from '../src/prompt/convert-to-language-model-prompt';\nexport { prepareToolsAndToolChoice } from '../src/prompt/prepare-tools-and-tool-choice';\nexport { standardizePrompt } from '../src/prompt/standardize-prompt';\nexport { prepareCallSettings } from '../src/prompt/prepare-call-settings';\nexport { prepareRetries } from '../src/util/prepare-retries';\n", "import {\n  LanguageModelV2FilePart,\n  LanguageModelV2Message,\n  LanguageModelV2Prompt,\n  LanguageModelV2TextPart,\n} from '@ai-sdk/provider';\nimport {\n  DataContent,\n  FilePart,\n  ImagePart,\n  isUrlSupported,\n  ModelMessage,\n  TextPart,\n} from '@ai-sdk/provider-utils';\nimport {\n  detectMediaType,\n  imageMediaTypeSignatures,\n} from '../util/detect-media-type';\nimport { download } from '../util/download';\nimport { convertToLanguageModelV2DataContent } from './data-content';\nimport { InvalidMessageRoleError } from './invalid-message-role-error';\nimport { StandardizedPrompt } from './standardize-prompt';\n\nexport async function convertToLanguageModelPrompt({\n  prompt,\n  supportedUrls,\n  downloadImplementation = download,\n}: {\n  prompt: StandardizedPrompt;\n  supportedUrls: Record<string, RegExp[]>;\n  downloadImplementation?: typeof download;\n}): Promise<LanguageModelV2Prompt> {\n  const downloadedAssets = await downloadAssets(\n    prompt.messages,\n    downloadImplementation,\n    supportedUrls,\n  );\n\n  return [\n    ...(prompt.system != null\n      ? [{ role: 'system' as const, content: prompt.system }]\n      : []),\n    ...prompt.messages.map(message =>\n      convertToLanguageModelMessage({ message, downloadedAssets }),\n    ),\n  ];\n}\n\n/**\n * Convert a ModelMessage to a LanguageModelV2Message.\n *\n * @param message The ModelMessage to convert.\n * @param downloadedAssets A map of URLs to their downloaded data. Only\n *   available if the model does not support URLs, null otherwise.\n */\nexport function convertToLanguageModelMessage({\n  message,\n  downloadedAssets,\n}: {\n  message: ModelMessage;\n  downloadedAssets: Record<\n    string,\n    { mediaType: string | undefined; data: Uint8Array }\n  >;\n}): LanguageModelV2Message {\n  const role = message.role;\n  switch (role) {\n    case 'system': {\n      return {\n        role: 'system',\n        content: message.content,\n        providerOptions: message.providerOptions,\n      };\n    }\n\n    case 'user': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'user',\n          content: [{ type: 'text', text: message.content }],\n          providerOptions: message.providerOptions,\n        };\n      }\n\n      return {\n        role: 'user',\n        content: message.content\n          .map(part => convertPartToLanguageModelPart(part, downloadedAssets))\n          // remove empty text parts:\n          .filter(part => part.type !== 'text' || part.text !== ''),\n        providerOptions: message.providerOptions,\n      };\n    }\n\n    case 'assistant': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'assistant',\n          content: [{ type: 'text', text: message.content }],\n          providerOptions: message.providerOptions,\n        };\n      }\n\n      return {\n        role: 'assistant',\n        content: message.content\n          .filter(\n            // remove empty text parts:\n            part => part.type !== 'text' || part.text !== '',\n          )\n          .map(part => {\n            const providerOptions = part.providerOptions;\n\n            switch (part.type) {\n              case 'file': {\n                const { data, mediaType } = convertToLanguageModelV2DataContent(\n                  part.data,\n                );\n                return {\n                  type: 'file',\n                  data,\n                  filename: part.filename,\n                  mediaType: mediaType ?? part.mediaType,\n                  providerOptions,\n                };\n              }\n              case 'reasoning': {\n                return {\n                  type: 'reasoning',\n                  text: part.text,\n                  providerOptions,\n                };\n              }\n              case 'text': {\n                return {\n                  type: 'text' as const,\n                  text: part.text,\n                  providerOptions,\n                };\n              }\n              case 'tool-call': {\n                return {\n                  type: 'tool-call' as const,\n                  toolCallId: part.toolCallId,\n                  toolName: part.toolName,\n                  input: part.input,\n                  providerExecuted: part.providerExecuted,\n                  providerOptions,\n                };\n              }\n              case 'tool-result': {\n                return {\n                  type: 'tool-result' as const,\n                  toolCallId: part.toolCallId,\n                  toolName: part.toolName,\n                  output: part.output,\n                  providerOptions,\n                };\n              }\n            }\n          }),\n        providerOptions: message.providerOptions,\n      };\n    }\n\n    case 'tool': {\n      return {\n        role: 'tool',\n        content: message.content.map(part => ({\n          type: 'tool-result' as const,\n          toolCallId: part.toolCallId,\n          toolName: part.toolName,\n          output: part.output,\n          providerOptions: part.providerOptions,\n        })),\n        providerOptions: message.providerOptions,\n      };\n    }\n\n    default: {\n      const _exhaustiveCheck: never = role;\n      throw new InvalidMessageRoleError({ role: _exhaustiveCheck });\n    }\n  }\n}\n\n/**\n * Downloads images and files from URLs in the messages.\n */\nasync function downloadAssets(\n  messages: ModelMessage[],\n  downloadImplementation: typeof download,\n  supportedUrls: Record<string, RegExp[]>,\n): Promise<\n  Record<string, { mediaType: string | undefined; data: Uint8Array }>\n> {\n  const urls = messages\n    .filter(message => message.role === 'user')\n    .map(message => message.content)\n    .filter((content): content is Array<TextPart | ImagePart | FilePart> =>\n      Array.isArray(content),\n    )\n    .flat()\n    .filter(\n      (part): part is ImagePart | FilePart =>\n        part.type === 'image' || part.type === 'file',\n    )\n    .map(part => {\n      const mediaType =\n        part.mediaType ?? (part.type === 'image' ? 'image/*' : undefined);\n\n      let data = part.type === 'image' ? part.image : part.data;\n      if (typeof data === 'string') {\n        try {\n          data = new URL(data);\n        } catch (ignored) {}\n      }\n\n      return { mediaType, data };\n    })\n    /**\n     * Filter out URLs that the model supports natively, so we don't download them.\n     */\n    .filter(\n      (part): part is { mediaType: string; data: URL } =>\n        part.data instanceof URL &&\n        part.mediaType != null &&\n        !isUrlSupported({\n          url: part.data.toString(),\n          mediaType: part.mediaType,\n          supportedUrls,\n        }),\n    )\n    .map(part => part.data);\n\n  // download in parallel:\n  const downloadedImages = await Promise.all(\n    urls.map(async url => ({\n      url,\n      data: await downloadImplementation({ url }),\n    })),\n  );\n\n  return Object.fromEntries(\n    downloadedImages.map(({ url, data }) => [url.toString(), data]),\n  );\n}\n\n/**\n * Convert part of a message to a LanguageModelV2Part.\n * @param part The part to convert.\n * @param downloadedAssets A map of URLs to their downloaded data. Only\n *  available if the model does not support URLs, null otherwise.\n *\n * @returns The converted part.\n */\nfunction convertPartToLanguageModelPart(\n  part: TextPart | ImagePart | FilePart,\n  downloadedAssets: Record<\n    string,\n    { mediaType: string | undefined; data: Uint8Array }\n  >,\n): LanguageModelV2TextPart | LanguageModelV2FilePart {\n  if (part.type === 'text') {\n    return {\n      type: 'text',\n      text: part.text,\n      providerOptions: part.providerOptions,\n    };\n  }\n\n  let originalData: DataContent | URL;\n  const type = part.type;\n  switch (type) {\n    case 'image':\n      originalData = part.image;\n      break;\n    case 'file':\n      originalData = part.data;\n\n      break;\n    default:\n      throw new Error(`Unsupported part type: ${type}`);\n  }\n\n  const { data: convertedData, mediaType: convertedMediaType } =\n    convertToLanguageModelV2DataContent(originalData);\n\n  let mediaType: string | undefined = convertedMediaType ?? part.mediaType;\n  let data: Uint8Array | string | URL = convertedData; // binary | base64 | url\n\n  // If the content is a URL, we check if it was downloaded:\n  if (data instanceof URL) {\n    const downloadedFile = downloadedAssets[data.toString()];\n    if (downloadedFile) {\n      data = downloadedFile.data;\n      mediaType ??= downloadedFile.mediaType;\n    }\n  }\n\n  // Now that we have the normalized data either as a URL or a Uint8Array,\n  // we can create the LanguageModelV2Part.\n  switch (type) {\n    case 'image': {\n      // When possible, try to detect the media type automatically\n      // to deal with incorrect media type inputs.\n      // When detection fails, use provided media type.\n      if (data instanceof Uint8Array || typeof data === 'string') {\n        mediaType =\n          detectMediaType({ data, signatures: imageMediaTypeSignatures }) ??\n          mediaType;\n      }\n\n      return {\n        type: 'file',\n        mediaType: mediaType ?? 'image/*', // any image\n        filename: undefined,\n        data,\n        providerOptions: part.providerOptions,\n      };\n    }\n\n    case 'file': {\n      // We must have a mediaType for files, if not, throw an error.\n      if (mediaType == null) {\n        throw new Error(`Media type is missing for file part`);\n      }\n\n      return {\n        type: 'file',\n        mediaType,\n        filename: part.filename,\n        data,\n        providerOptions: part.providerOptions,\n      };\n    }\n  }\n}\n", "import { convertBase64ToUint8Array } from '@ai-sdk/provider-utils';\n\nexport const imageMediaTypeSignatures = [\n  {\n    mediaType: 'image/gif' as const,\n    bytesPrefix: [0x47, 0x49, 0x46],\n    base64Prefix: 'R0lG',\n  },\n  {\n    mediaType: 'image/png' as const,\n    bytesPrefix: [0x89, 0x50, 0x4e, 0x47],\n    base64Prefix: 'iVBORw',\n  },\n  {\n    mediaType: 'image/jpeg' as const,\n    bytesPrefix: [0xff, 0xd8],\n    base64Prefix: '/9j/',\n  },\n  {\n    mediaType: 'image/webp' as const,\n    bytesPrefix: [0x52, 0x49, 0x46, 0x46],\n    base64Prefix: 'UklGRg',\n  },\n  {\n    mediaType: 'image/bmp' as const,\n    bytesPrefix: [0x42, 0x4d],\n    base64Prefix: 'Qk',\n  },\n  {\n    mediaType: 'image/tiff' as const,\n    bytesPrefix: [0x49, 0x49, 0x2a, 0x00],\n    base64Prefix: 'SUkqAA',\n  },\n  {\n    mediaType: 'image/tiff' as const,\n    bytesPrefix: [0x4d, 0x4d, 0x00, 0x2a],\n    base64Prefix: 'TU0AKg',\n  },\n  {\n    mediaType: 'image/avif' as const,\n    bytesPrefix: [\n      0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66,\n    ],\n    base64Prefix: 'AAAAIGZ0eXBhdmlm',\n  },\n  {\n    mediaType: 'image/heic' as const,\n    bytesPrefix: [\n      0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63,\n    ],\n    base64Prefix: 'AAAAIGZ0eXBoZWlj',\n  },\n] as const;\n\nexport const audioMediaTypeSignatures = [\n  {\n    mediaType: 'audio/mpeg' as const,\n    bytesPrefix: [0xff, 0xfb],\n    base64Prefix: '//s=',\n  },\n  {\n    mediaType: 'audio/wav' as const,\n    bytesPrefix: [0x52, 0x49, 0x46, 0x46],\n    base64Prefix: 'UklGR',\n  },\n  {\n    mediaType: 'audio/ogg' as const,\n    bytesPrefix: [0x4f, 0x67, 0x67, 0x53],\n    base64Prefix: 'T2dnUw',\n  },\n  {\n    mediaType: 'audio/flac' as const,\n    bytesPrefix: [0x66, 0x4c, 0x61, 0x43],\n    base64Prefix: 'ZkxhQw',\n  },\n  {\n    mediaType: 'audio/aac' as const,\n    bytesPrefix: [0x40, 0x15, 0x00, 0x00],\n    base64Prefix: 'QBUA',\n  },\n  {\n    mediaType: 'audio/mp4' as const,\n    bytesPrefix: [0x66, 0x74, 0x79, 0x70],\n    base64Prefix: 'ZnR5cA',\n  },\n  {\n    mediaType: 'audio/webm',\n    bytesPrefix: [0x1a, 0x45, 0xdf, 0xa3],\n    base64Prefix: 'GkXf',\n  },\n] as const;\n\nconst stripID3 = (data: Uint8Array | string) => {\n  const bytes =\n    typeof data === 'string' ? convertBase64ToUint8Array(data) : data;\n  const id3Size =\n    ((bytes[6] & 0x7f) << 21) |\n    ((bytes[7] & 0x7f) << 14) |\n    ((bytes[8] & 0x7f) << 7) |\n    (bytes[9] & 0x7f);\n\n  // The raw MP3 starts here\n  return bytes.slice(id3Size + 10);\n};\n\nfunction stripID3TagsIfPresent(data: Uint8Array | string): Uint8Array | string {\n  const hasId3 =\n    (typeof data === 'string' && data.startsWith('SUQz')) ||\n    (typeof data !== 'string' &&\n      data.length > 10 &&\n      data[0] === 0x49 && // 'I'\n      data[1] === 0x44 && // 'D'\n      data[2] === 0x33); // '3'\n\n  return hasId3 ? stripID3(data) : data;\n}\n\n/**\n * Detect the media IANA media type of a file using a list of signatures.\n *\n * @param data - The file data.\n * @param signatures - The signatures to use for detection.\n * @returns The media type of the file.\n */\nexport function detectMediaType({\n  data,\n  signatures,\n}: {\n  data: Uint8Array | string;\n  signatures: typeof audioMediaTypeSignatures | typeof imageMediaTypeSignatures;\n}): (typeof signatures)[number]['mediaType'] | undefined {\n  const processedData = stripID3TagsIfPresent(data);\n\n  for (const signature of signatures) {\n    if (\n      typeof processedData === 'string'\n        ? processedData.startsWith(signature.base64Prefix)\n        : processedData.length >= signature.bytesPrefix.length &&\n          signature.bytesPrefix.every(\n            (byte, index) => processedData[index] === byte,\n          )\n    ) {\n      return signature.mediaType;\n    }\n  }\n\n  return undefined;\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_DownloadError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class DownloadError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly statusCode?: number;\n  readonly statusText?: string;\n\n  constructor({\n    url,\n    statusCode,\n    statusText,\n    cause,\n    message = cause == null\n      ? `Failed to download ${url}: ${statusCode} ${statusText}`\n      : `Failed to download ${url}: ${cause}`,\n  }: {\n    url: string;\n    statusCode?: number;\n    statusText?: string;\n    message?: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n  }\n\n  static isInstance(error: unknown): error is DownloadError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { DownloadError } from './download-error';\n\nexport async function download({ url }: { url: URL }): Promise<{\n  data: Uint8Array;\n  mediaType: string | undefined;\n}> {\n  const urlText = url.toString();\n  try {\n    const response = await fetch(urlText);\n\n    if (!response.ok) {\n      throw new DownloadError({\n        url: urlText,\n        statusCode: response.status,\n        statusText: response.statusText,\n      });\n    }\n\n    return {\n      data: new Uint8Array(await response.arrayBuffer()),\n      mediaType: response.headers.get('content-type') ?? undefined,\n    };\n  } catch (error) {\n    if (DownloadError.isInstance(error)) {\n      throw error;\n    }\n\n    throw new DownloadError({ url: urlText, cause: error });\n  }\n}\n", "import { AI<PERSON>KError, LanguageModelV2DataContent } from '@ai-sdk/provider';\nimport {\n  convertBase64ToUint8Array,\n  convertUint8ArrayToBase64,\n  DataContent,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod/v4';\nimport { InvalidDataContentError } from './invalid-data-content-error';\nimport { splitDataUrl } from './split-data-url';\n\n/**\n@internal\n */\nexport const dataContentSchema: z.ZodType<DataContent> = z.union([\n  z.string(),\n  z.instanceof(Uint8Array),\n  z.instanceof(ArrayBuffer),\n  z.custom<Buffer>(\n    // Buffer might not be available in some environments such as CloudFlare:\n    (value: unknown): value is Buffer =>\n      globalThis.Buffer?.isBuffer(value) ?? false,\n    { message: 'Must be a Buffer' },\n  ),\n]);\n\nexport function convertToLanguageModelV2DataContent(\n  content: DataContent | URL,\n): {\n  data: LanguageModelV2DataContent;\n  mediaType: string | undefined;\n} {\n  // Buffer & Uint8Array:\n  if (content instanceof Uint8Array) {\n    return { data: content, mediaType: undefined };\n  }\n\n  // ArrayBuffer needs conversion to Uint8Array (lightweight):\n  if (content instanceof ArrayBuffer) {\n    return { data: new Uint8Array(content), mediaType: undefined };\n  }\n\n  // Attempt to create a URL from the data. If it fails, we can assume the data\n  // is not a URL and likely some other sort of data.\n  if (typeof content === 'string') {\n    try {\n      content = new URL(content);\n    } catch (error) {\n      // ignored\n    }\n  }\n\n  // Extract data from data URL:\n  if (content instanceof URL && content.protocol === 'data:') {\n    const { mediaType: dataUrlMediaType, base64Content } = splitDataUrl(\n      content.toString(),\n    );\n\n    if (dataUrlMediaType == null || base64Content == null) {\n      throw new AISDKError({\n        name: 'InvalidDataContentError',\n        message: `Invalid data URL format in content ${content.toString()}`,\n      });\n    }\n\n    return { data: base64Content, mediaType: dataUrlMediaType };\n  }\n\n  return { data: content, mediaType: undefined };\n}\n\n/**\nConverts data content to a base64-encoded string.\n\n@param content - Data content to convert.\n@returns Base64-encoded string.\n*/\nexport function convertDataContentToBase64String(content: DataContent): string {\n  if (typeof content === 'string') {\n    return content;\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return convertUint8ArrayToBase64(new Uint8Array(content));\n  }\n\n  return convertUint8ArrayToBase64(content);\n}\n\n/**\nConverts data content to a Uint8Array.\n\n@param content - Data content to convert.\n@returns Uint8Array.\n */\nexport function convertDataContentToUint8Array(\n  content: DataContent,\n): Uint8Array {\n  if (content instanceof Uint8Array) {\n    return content;\n  }\n\n  if (typeof content === 'string') {\n    try {\n      return convertBase64ToUint8Array(content);\n    } catch (error) {\n      throw new InvalidDataContentError({\n        message:\n          'Invalid data content. Content string is not a base64-encoded media.',\n        content,\n        cause: error,\n      });\n    }\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return new Uint8Array(content);\n  }\n\n  throw new InvalidDataContentError({ content });\n}\n\n/**\n * Converts a Uint8Array to a string of text.\n *\n * @param uint8Array - The Uint8Array to convert.\n * @returns The converted string.\n */\nexport function convertUint8ArrayToText(uint8Array: Uint8Array): string {\n  try {\n    return new TextDecoder().decode(uint8Array);\n  } catch (error) {\n    throw new Error('Error decoding Uint8Array to text');\n  }\n}\n", "export function splitDataUrl(dataUrl: string): {\n  mediaType: string | undefined;\n  base64Content: string | undefined;\n} {\n  try {\n    const [header, base64Content] = dataUrl.split(',');\n    return {\n      mediaType: header.split(';')[0].split(':')[1],\n      base64Content,\n    };\n  } catch (error) {\n    return {\n      mediaType: undefined,\n      base64Content: undefined,\n    };\n  }\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_InvalidMessageRoleError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class InvalidMessageRoleError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly role: string;\n\n  constructor({\n    role,\n    message = `Invalid message role: '${role}'. Must be one of: \"system\", \"user\", \"assistant\", \"tool\".`,\n  }: {\n    role: string;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.role = role;\n  }\n\n  static isInstance(error: unknown): error is InvalidMessageRoleError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import {\n  LanguageModelV2FunctionTool,\n  LanguageModelV2ProviderDefinedTool,\n  LanguageModelV2ToolChoice,\n} from '@ai-sdk/provider';\nimport { asSchema } from '@ai-sdk/provider-utils';\nimport { isNonEmptyObject } from '../util/is-non-empty-object';\nimport { ToolSet } from '../generate-text';\nimport { ToolChoice } from '../types/language-model';\n\nexport function prepareToolsAndToolChoice<TOOLS extends ToolSet>({\n  tools,\n  toolChoice,\n  activeTools,\n}: {\n  tools: TOOLS | undefined;\n  toolChoice: ToolChoice<TOOLS> | undefined;\n  activeTools: Array<keyof TOOLS> | undefined;\n}): {\n  tools:\n    | Array<LanguageModelV2FunctionTool | LanguageModelV2ProviderDefinedTool>\n    | undefined;\n  toolChoice: LanguageModelV2ToolChoice | undefined;\n} {\n  if (!isNonEmptyObject(tools)) {\n    return {\n      tools: undefined,\n      toolChoice: undefined,\n    };\n  }\n\n  // when activeTools is provided, we only include the tools that are in the list:\n  const filteredTools =\n    activeTools != null\n      ? Object.entries(tools).filter(([name]) =>\n          activeTools.includes(name as keyof TOOLS),\n        )\n      : Object.entries(tools);\n\n  return {\n    tools: filteredTools.map(([name, tool]) => {\n      const toolType = tool.type;\n      switch (toolType) {\n        case undefined:\n        case 'dynamic':\n        case 'function':\n          return {\n            type: 'function' as const,\n            name,\n            description: tool.description,\n            inputSchema: asSchema(tool.inputSchema).jsonSchema,\n            providerOptions: tool.providerOptions,\n          };\n        case 'provider-defined':\n          return {\n            type: 'provider-defined' as const,\n            name,\n            id: tool.id,\n            args: tool.args,\n          };\n        default: {\n          const exhaustiveCheck: never = toolType;\n          throw new Error(`Unsupported tool type: ${exhaustiveCheck}`);\n        }\n      }\n    }),\n    toolChoice:\n      toolChoice == null\n        ? { type: 'auto' }\n        : typeof toolChoice === 'string'\n          ? { type: toolChoice }\n          : { type: 'tool' as const, toolName: toolChoice.toolName as string },\n  };\n}\n", "export function isNonEmptyObject(\n  object: Record<string, unknown> | undefined | null,\n): object is Record<string, unknown> {\n  return object != null && Object.keys(object).length > 0;\n}\n", "import { InvalidPromptError } from '@ai-sdk/provider';\nimport { ModelMessage, safeValidateTypes } from '@ai-sdk/provider-utils';\nimport { z } from 'zod/v4';\nimport { modelMessageSchema } from './message';\nimport { Prompt } from './prompt';\n\nexport type StandardizedPrompt = {\n  /**\n   * System message.\n   */\n  system?: string;\n\n  /**\n   * Messages.\n   */\n  messages: ModelMessage[];\n};\n\nexport async function standardizePrompt(\n  prompt: Prompt,\n): Promise<StandardizedPrompt> {\n  if (prompt.prompt == null && prompt.messages == null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt or messages must be defined',\n    });\n  }\n\n  if (prompt.prompt != null && prompt.messages != null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt and messages cannot be defined at the same time',\n    });\n  }\n\n  // validate that system is a string\n  if (prompt.system != null && typeof prompt.system !== 'string') {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'system must be a string',\n    });\n  }\n\n  let messages: ModelMessage[];\n\n  if (prompt.prompt != null && typeof prompt.prompt === 'string') {\n    messages = [{ role: 'user', content: prompt.prompt }];\n  } else if (prompt.prompt != null && Array.isArray(prompt.prompt)) {\n    messages = prompt.prompt;\n  } else if (prompt.messages != null) {\n    messages = prompt.messages;\n  } else {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt or messages must be defined',\n    });\n  }\n\n  if (messages.length === 0) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'messages must not be empty',\n    });\n  }\n\n  const validationResult = await safeValidateTypes({\n    value: messages,\n    schema: z.array(modelMessageSchema),\n  });\n\n  if (!validationResult.success) {\n    throw new InvalidPromptError({\n      prompt,\n      message:\n        'The messages must be a ModelMessage[]. ' +\n        'If you have passed a UIMessage[], you can use convertToModelMessages to convert them.',\n      cause: validationResult.error,\n    });\n  }\n\n  return {\n    messages,\n    system: prompt.system,\n  };\n}\n", "import {\n  AssistantModelMessage,\n  ModelMessage,\n  SystemModelMessage,\n  ToolModelMessage,\n  UserModelMessage,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod/v4';\nimport { providerMetadataSchema } from '../types/provider-metadata';\nimport {\n  filePartSchema,\n  imagePartSchema,\n  reasoningPartSchema,\n  textPartSchema,\n  toolCallPartSchema,\n  toolResultPartSchema,\n} from './content-part';\n\n/**\n@deprecated Use `SystemModelMessage` instead.\n */\n// TODO remove in AI SDK 6\nexport type CoreSystemMessage = SystemModelMessage;\n\nexport const systemModelMessageSchema: z.ZodType<SystemModelMessage> = z.object(\n  {\n    role: z.literal('system'),\n    content: z.string(),\n    providerOptions: providerMetadataSchema.optional(),\n  },\n);\n\n/**\n@deprecated Use `systemModelMessageSchema` instead.\n */\n// TODO remove in AI SDK 6\nexport const coreSystemMessageSchema = systemModelMessageSchema;\n\n/**\n@deprecated Use `UserModelMessage` instead.\n */\n// TODO remove in AI SDK 6\nexport type CoreUserMessage = UserModelMessage;\n\nexport const userModelMessageSchema: z.ZodType<UserModelMessage> = z.object({\n  role: z.literal('user'),\n  content: z.union([\n    z.string(),\n    z.array(z.union([textPartSchema, imagePartSchema, filePartSchema])),\n  ]),\n  providerOptions: providerMetadataSchema.optional(),\n});\n\n/**\n@deprecated Use `userModelMessageSchema` instead.\n */\n// TODO remove in AI SDK 6\nexport const coreUserMessageSchema = userModelMessageSchema;\n\n/**\n@deprecated Use `AssistantModelMessage` instead.\n */\n// TODO remove in AI SDK 6\nexport type CoreAssistantMessage = AssistantModelMessage;\n\nexport const assistantModelMessageSchema: z.ZodType<AssistantModelMessage> =\n  z.object({\n    role: z.literal('assistant'),\n    content: z.union([\n      z.string(),\n      z.array(\n        z.union([\n          textPartSchema,\n          filePartSchema,\n          reasoningPartSchema,\n          toolCallPartSchema,\n          toolResultPartSchema,\n        ]),\n      ),\n    ]),\n    providerOptions: providerMetadataSchema.optional(),\n  });\n\n/**\n@deprecated Use `assistantModelMessageSchema` instead.\n */\n// TODO remove in AI SDK 6\nexport const coreAssistantMessageSchema = assistantModelMessageSchema;\n\n/**\n@deprecated Use `ToolModelMessage` instead.\n */\n// TODO remove in AI SDK 6\nexport type CoreToolMessage = ToolModelMessage;\n\nexport const toolModelMessageSchema: z.ZodType<ToolModelMessage> = z.object({\n  role: z.literal('tool'),\n  content: z.array(toolResultPartSchema),\n  providerOptions: providerMetadataSchema.optional(),\n});\n\n/**\n@deprecated Use `toolModelMessageSchema` instead.\n */\n// TODO remove in AI SDK 6\nexport const coreToolMessageSchema = toolModelMessageSchema;\n\n/**\n@deprecated Use `ModelMessage` instead.\n   */\n// TODO remove in AI SDK 6\nexport type CoreMessage = ModelMessage;\n\nexport const modelMessageSchema: z.ZodType<ModelMessage> = z.union([\n  systemModelMessageSchema,\n  userModelMessageSchema,\n  assistantModelMessageSchema,\n  toolModelMessageSchema,\n]);\n\n/**\n@deprecated Use `modelMessageSchema` instead.\n */\n// TODO remove in AI SDK 6\nexport const coreMessageSchema: z.ZodType<CoreMessage> = modelMessageSchema;\n", "import { SharedV2ProviderMetadata } from '@ai-sdk/provider';\nimport { z } from 'zod/v4';\nimport { jsonValueSchema } from './json-value';\n\n/**\nAdditional provider-specific metadata that is returned from the provider.\n\nThis is needed to enable provider-specific functionality that can be\nfully encapsulated in the provider.\n */\nexport type ProviderMetadata = SharedV2ProviderMetadata;\n\nexport const providerMetadataSchema: z.ZodType<ProviderMetadata> = z.record(\n  z.string(),\n  z.record(z.string(), jsonValueSchema),\n);\n", "import { JSONValue as OriginalJSONValue } from '@ai-sdk/provider';\nimport { z } from 'zod/v4';\n\nexport const jsonValueSchema: z.ZodType<JSONValue> = z.lazy(() =>\n  z.union([\n    z.null(),\n    z.string(),\n    z.number(),\n    z.boolean(),\n    z.record(z.string(), jsonValueSchema),\n    z.array(jsonValueSchema),\n  ]),\n);\n\nexport type JSONValue = OriginalJSONValue;\n", "import { LanguageModelV2ToolResultOutput } from '@ai-sdk/provider';\nimport {\n  FilePart,\n  ImagePart,\n  ProviderOptions,\n  ReasoningPart,\n  TextPart,\n  ToolResultPart,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod/v4';\nimport { jsonValueSchema } from '../types/json-value';\nimport { providerMetadataSchema } from '../types/provider-metadata';\nimport { dataContentSchema } from './data-content';\n\n/**\n@internal\n */\nexport const textPartSchema: z.ZodType<TextPart> = z.object({\n  type: z.literal('text'),\n  text: z.string(),\n  providerOptions: providerMetadataSchema.optional(),\n});\n\n/**\n@internal\n */\nexport const imagePartSchema: z.ZodType<ImagePart> = z.object({\n  type: z.literal('image'),\n  image: z.union([dataContentSchema, z.instanceof(URL)]),\n  mediaType: z.string().optional(),\n  providerOptions: providerMetadataSchema.optional(),\n});\n\n/**\n@internal\n */\nexport const filePartSchema: z.ZodType<FilePart> = z.object({\n  type: z.literal('file'),\n  data: z.union([dataContentSchema, z.instanceof(URL)]),\n  filename: z.string().optional(),\n  mediaType: z.string(),\n  providerOptions: providerMetadataSchema.optional(),\n});\n\n/**\n@internal\n */\nexport const reasoningPartSchema: z.ZodType<ReasoningPart> = z.object({\n  type: z.literal('reasoning'),\n  text: z.string(),\n  providerOptions: providerMetadataSchema.optional(),\n});\n\n/**\nTool call content part of a prompt. It contains a tool call (usually generated by the AI model).\n */\nexport interface ToolCallPart {\n  type: 'tool-call';\n\n  /**\nID of the tool call. This ID is used to match the tool call with the tool result.\n */\n  toolCallId: string;\n\n  /**\nName of the tool that is being called.\n */\n  toolName: string;\n\n  /**\nArguments of the tool call. This is a JSON-serializable object that matches the tool's input schema.\n   */\n  input: unknown;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n}\n\n/**\n@internal\n */\nexport const toolCallPartSchema: z.ZodType<ToolCallPart> = z.object({\n  type: z.literal('tool-call'),\n  toolCallId: z.string(),\n  toolName: z.string(),\n  input: z.unknown(),\n  providerOptions: providerMetadataSchema.optional(),\n  providerExecuted: z.boolean().optional(),\n}) as z.ZodType<ToolCallPart>; // necessary bc input is optional on Zod type\n\n/**\n@internal\n */\nexport const outputSchema: z.ZodType<LanguageModelV2ToolResultOutput> =\n  z.discriminatedUnion('type', [\n    z.object({\n      type: z.literal('text'),\n      value: z.string(),\n    }),\n    z.object({\n      type: z.literal('json'),\n      value: jsonValueSchema,\n    }),\n    z.object({\n      type: z.literal('error-text'),\n      value: z.string(),\n    }),\n    z.object({\n      type: z.literal('error-json'),\n      value: jsonValueSchema,\n    }),\n    z.object({\n      type: z.literal('content'),\n      value: z.array(\n        z.union([\n          z.object({\n            type: z.literal('text'),\n            text: z.string(),\n          }),\n          z.object({\n            type: z.literal('media'),\n            data: z.string(),\n            mediaType: z.string(),\n          }),\n        ]),\n      ),\n    }),\n  ]);\n\n/**\n@internal\n */\nexport const toolResultPartSchema: z.ZodType<ToolResultPart> = z.object({\n  type: z.literal('tool-result'),\n  toolCallId: z.string(),\n  toolName: z.string(),\n  output: outputSchema,\n  providerOptions: providerMetadataSchema.optional(),\n}) as z.ZodType<ToolResultPart>; // necessary bc result is optional on Zod type\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly parameter: string;\n  readonly value: unknown;\n\n  constructor({\n    parameter,\n    value,\n    message,\n  }: {\n    parameter: string;\n    value: unknown;\n    message: string;\n  }) {\n    super({\n      name,\n      message: `Invalid argument for parameter ${parameter}: ${message}`,\n    });\n\n    this.parameter = parameter;\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { InvalidArgumentError } from '../error/invalid-argument-error';\nimport { CallSettings } from './call-settings';\n\n/**\n * Validates call settings and returns a new object with limited values.\n */\nexport function prepareCallSettings({\n  maxOutputTokens,\n  temperature,\n  topP,\n  topK,\n  presencePenalty,\n  frequencyPenalty,\n  seed,\n  stopSequences,\n}: Omit<CallSettings, 'abortSignal' | 'headers' | 'maxRetries'>): Omit<\n  CallSettings,\n  'abortSignal' | 'headers' | 'maxRetries'\n> {\n  if (maxOutputTokens != null) {\n    if (!Number.isInteger(maxOutputTokens)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxOutputTokens',\n        value: maxOutputTokens,\n        message: 'maxOutputTokens must be an integer',\n      });\n    }\n\n    if (maxOutputTokens < 1) {\n      throw new InvalidArgumentError({\n        parameter: 'maxOutputTokens',\n        value: maxOutputTokens,\n        message: 'maxOutputTokens must be >= 1',\n      });\n    }\n  }\n\n  if (temperature != null) {\n    if (typeof temperature !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'temperature',\n        value: temperature,\n        message: 'temperature must be a number',\n      });\n    }\n  }\n\n  if (topP != null) {\n    if (typeof topP !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'topP',\n        value: topP,\n        message: 'topP must be a number',\n      });\n    }\n  }\n\n  if (topK != null) {\n    if (typeof topK !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'topK',\n        value: topK,\n        message: 'topK must be a number',\n      });\n    }\n  }\n\n  if (presencePenalty != null) {\n    if (typeof presencePenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'presencePenalty',\n        value: presencePenalty,\n        message: 'presencePenalty must be a number',\n      });\n    }\n  }\n\n  if (frequencyPenalty != null) {\n    if (typeof frequencyPenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'frequencyPenalty',\n        value: frequencyPenalty,\n        message: 'frequencyPenalty must be a number',\n      });\n    }\n  }\n\n  if (seed != null) {\n    if (!Number.isInteger(seed)) {\n      throw new InvalidArgumentError({\n        parameter: 'seed',\n        value: seed,\n        message: 'seed must be an integer',\n      });\n    }\n  }\n\n  return {\n    maxOutputTokens,\n    temperature,\n    topP,\n    topK,\n    presencePenalty,\n    frequencyPenalty,\n    stopSequences,\n    seed,\n  };\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { delay, getErrorMessage, isAbortError } from '@ai-sdk/provider-utils';\nimport { RetryError } from './retry-error';\n\nexport type RetryFunction = <OUTPUT>(\n  fn: () => PromiseLike<OUTPUT>,\n) => PromiseLike<OUTPUT>;\n\nfunction getRetryDelayInMs({\n  error,\n  exponentialBackoffDelay,\n}: {\n  error: APICallError;\n  exponentialBackoffDelay: number;\n}): number {\n  const headers = error.responseHeaders;\n\n  if (!headers) return exponentialBackoffDelay;\n\n  let ms: number | undefined;\n\n  // retry-ms is more precise than retry-after and used by e.g. OpenAI\n  const retryAfterMs = headers['retry-after-ms'];\n  if (retryAfterMs) {\n    const timeoutMs = parseFloat(retryAfterMs);\n    if (!Number.isNaN(timeoutMs)) {\n      ms = timeoutMs;\n    }\n  }\n\n  // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n  const retryAfter = headers['retry-after'];\n  if (retryAfter && ms === undefined) {\n    const timeoutSeconds = parseFloat(retryAfter);\n    if (!Number.isNaN(timeoutSeconds)) {\n      ms = timeoutSeconds * 1000;\n    } else {\n      ms = Date.parse(retryAfter) - Date.now();\n    }\n  }\n\n  // check that the delay is reasonable:\n  if (\n    ms != null &&\n    !Number.isNaN(ms) &&\n    0 <= ms &&\n    (ms < 60 * 1000 || ms < exponentialBackoffDelay)\n  ) {\n    return ms;\n  }\n\n  return exponentialBackoffDelay;\n}\n\n/**\nThe `retryWithExponentialBackoffRespectingRetryHeaders` strategy retries a failed API call with an exponential backoff,\nwhile respecting rate limit headers (retry-after-ms and retry-after) if they are provided and reasonable (0-60 seconds).\nYou can configure the maximum number of retries, the initial delay, and the backoff factor.\n */\nexport const retryWithExponentialBackoffRespectingRetryHeaders =\n  ({\n    maxRetries = 2,\n    initialDelayInMs = 2000,\n    backoffFactor = 2,\n    abortSignal,\n  }: {\n    maxRetries?: number;\n    initialDelayInMs?: number;\n    backoffFactor?: number;\n    abortSignal?: AbortSignal;\n  } = {}): RetryFunction =>\n  async <OUTPUT>(f: () => PromiseLike<OUTPUT>) =>\n    _retryWithExponentialBackoff(f, {\n      maxRetries,\n      delayInMs: initialDelayInMs,\n      backoffFactor,\n      abortSignal,\n    });\n\nasync function _retryWithExponentialBackoff<OUTPUT>(\n  f: () => PromiseLike<OUTPUT>,\n  {\n    maxRetries,\n    delayInMs,\n    backoffFactor,\n    abortSignal,\n  }: {\n    maxRetries: number;\n    delayInMs: number;\n    backoffFactor: number;\n    abortSignal: AbortSignal | undefined;\n  },\n  errors: unknown[] = [],\n): Promise<OUTPUT> {\n  try {\n    return await f();\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error; // don't retry when the request was aborted\n    }\n\n    if (maxRetries === 0) {\n      throw error; // don't wrap the error when retries are disabled\n    }\n\n    const errorMessage = getErrorMessage(error);\n    const newErrors = [...errors, error];\n    const tryNumber = newErrors.length;\n\n    if (tryNumber > maxRetries) {\n      throw new RetryError({\n        message: `Failed after ${tryNumber} attempts. Last error: ${errorMessage}`,\n        reason: 'maxRetriesExceeded',\n        errors: newErrors,\n      });\n    }\n\n    if (\n      error instanceof Error &&\n      APICallError.isInstance(error) &&\n      error.isRetryable === true &&\n      tryNumber <= maxRetries\n    ) {\n      await delay(\n        getRetryDelayInMs({\n          error,\n          exponentialBackoffDelay: delayInMs,\n        }),\n        { abortSignal },\n      );\n\n      return _retryWithExponentialBackoff(\n        f,\n        {\n          maxRetries,\n          delayInMs: backoffFactor * delayInMs,\n          backoffFactor,\n          abortSignal,\n        },\n        newErrors,\n      );\n    }\n\n    if (tryNumber === 1) {\n      throw error; // don't wrap the error when a non-retryable error occurs on the first try\n    }\n\n    throw new RetryError({\n      message: `Failed after ${tryNumber} attempts with non-retryable error: '${errorMessage}'`,\n      reason: 'errorNotRetryable',\n      errors: newErrors,\n    });\n  }\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_RetryError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport type RetryErrorReason =\n  | 'maxRetriesExceeded'\n  | 'errorNotRetryable'\n  | 'abort';\n\nexport class RetryError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  // note: property order determines debugging output\n  readonly reason: RetryErrorReason;\n  readonly lastError: unknown;\n  readonly errors: Array<unknown>;\n\n  constructor({\n    message,\n    reason,\n    errors,\n  }: {\n    message: string;\n    reason: RetryErrorReason;\n    errors: Array<unknown>;\n  }) {\n    super({ name, message });\n\n    this.reason = reason;\n    this.errors = errors;\n\n    // separate our last error to make debugging via log easier:\n    this.lastError = errors[errors.length - 1];\n  }\n\n  static isInstance(error: unknown): error is RetryError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { InvalidArgumentError } from '../error/invalid-argument-error';\nimport {\n  RetryFunction,\n  retryWithExponentialBackoffRespectingRetryHeaders,\n} from '../util/retry-with-exponential-backoff';\n\n/**\n * Validate and prepare retries.\n */\nexport function prepareRetries({\n  maxRetries,\n  abortSignal,\n}: {\n  maxRetries: number | undefined;\n  abortSignal: AbortSignal | undefined;\n}): {\n  maxRetries: number;\n  retry: RetryFunction;\n} {\n  if (maxRetries != null) {\n    if (!Number.isInteger(maxRetries)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be an integer',\n      });\n    }\n\n    if (maxRetries < 0) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be >= 0',\n      });\n    }\n  }\n\n  const maxRetriesResult = maxRetries ?? 2;\n\n  return {\n    maxRetries: maxRetriesResult,\n    retry: retryWithExponentialBackoffRespectingRetryHeaders({\n      maxRetries: maxRetriesResult,\n      abortSignal,\n    }),\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,IAAAA,yBAAqD;;;ACKrD,IAAAC,yBAOO;;;ACbP,4BAA0C;AAEnC,IAAM,2BAA2B;AAAA,EACtC;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,IAAM,IAAM,EAAI;AAAA,IAC9B,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,KAAM,IAAM,IAAM,EAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,KAAM,GAAI;AAAA,IACxB,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,IAAM,IAAM,IAAM,EAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,IAAM,EAAI;AAAA,IACxB,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,IAAM,IAAM,IAAM,CAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,IAAM,IAAM,GAAM,EAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa;AAAA,MACX;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACpE;AAAA,IACA,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa;AAAA,MACX;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACpE;AAAA,IACA,cAAc;AAAA,EAChB;AACF;AAwCA,IAAM,WAAW,CAAC,SAA8B;AAC9C,QAAM,QACJ,OAAO,SAAS,eAAW,iDAA0B,IAAI,IAAI;AAC/D,QAAM,WACF,MAAM,CAAC,IAAI,QAAS,MACpB,MAAM,CAAC,IAAI,QAAS,MACpB,MAAM,CAAC,IAAI,QAAS,IACrB,MAAM,CAAC,IAAI;AAGd,SAAO,MAAM,MAAM,UAAU,EAAE;AACjC;AAEA,SAAS,sBAAsB,MAAgD;AAC7E,QAAM,SACH,OAAO,SAAS,YAAY,KAAK,WAAW,MAAM,KAClD,OAAO,SAAS,YACf,KAAK,SAAS,MACd,KAAK,CAAC,MAAM;AAAA,EACZ,KAAK,CAAC,MAAM;AAAA,EACZ,KAAK,CAAC,MAAM;AAEhB,SAAO,SAAS,SAAS,IAAI,IAAI;AACnC;AASO,SAAS,gBAAgB;AAAA,EAC9B;AAAA,EACA;AACF,GAGyD;AACvD,QAAM,gBAAgB,sBAAsB,IAAI;AAEhD,aAAW,aAAa,YAAY;AAClC,QACE,OAAO,kBAAkB,WACrB,cAAc,WAAW,UAAU,YAAY,IAC/C,cAAc,UAAU,UAAU,YAAY,UAC9C,UAAU,YAAY;AAAA,MACpB,CAAC,MAAM,UAAU,cAAc,KAAK,MAAM;AAAA,IAC5C,GACJ;AACA,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;;;ACnJA,sBAA2B;AAE3B,IAAM,OAAO;AACb,IAAM,SAAS,mBAAmB,IAAI;AACtC,IAAM,SAAS,OAAO,IAAI,MAAM;AAJhC;AAMO,IAAM,gBAAN,cAA4B,2BAAW;AAAA,EAO5C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,SAAS,OACf,sBAAsB,GAAG,KAAK,UAAU,IAAI,UAAU,KACtD,sBAAsB,GAAG,KAAK,KAAK;AAAA,EACzC,GAMG;AACD,UAAM,EAAE,MAAM,SAAS,MAAM,CAAC;AArBhC,SAAkB,MAAU;AAuB1B,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,OAAO,WAAW,OAAwC;AACxD,WAAO,2BAAW,UAAU,OAAO,MAAM;AAAA,EAC3C;AACF;AA/BoB;;;ACLpB,eAAsB,SAAS,EAAE,IAAI,GAGlC;AALH,MAAAC;AAME,QAAM,UAAU,IAAI,SAAS;AAC7B,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,OAAO;AAEpC,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,cAAc;AAAA,QACtB,KAAK;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,SAAS;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,MAAM,IAAI,WAAW,MAAM,SAAS,YAAY,CAAC;AAAA,MACjD,YAAWA,MAAA,SAAS,QAAQ,IAAI,cAAc,MAAnC,OAAAA,MAAwC;AAAA,IACrD;AAAA,EACF,SAAS,OAAO;AACd,QAAI,cAAc,WAAW,KAAK,GAAG;AACnC,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,cAAc,EAAE,KAAK,SAAS,OAAO,MAAM,CAAC;AAAA,EACxD;AACF;;;AC7BA,IAAAC,mBAAuD;AACvD,IAAAC,yBAIO;AACP,gBAAkB;;;ACNX,SAAS,aAAa,SAG3B;AACA,MAAI;AACF,UAAM,CAAC,QAAQ,aAAa,IAAI,QAAQ,MAAM,GAAG;AACjD,WAAO;AAAA,MACL,WAAW,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,WAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,IACjB;AAAA,EACF;AACF;;;ADHO,IAAM,oBAA4C,YAAE,MAAM;AAAA,EAC/D,YAAE,OAAO;AAAA,EACT,YAAE,WAAW,UAAU;AAAA,EACvB,YAAE,WAAW,WAAW;AAAA,EACxB,YAAE;AAAA;AAAA,IAEA,CAAC,UAAiC;AAnBtC,UAAAC,KAAA;AAoBM,oBAAAA,MAAA,WAAW,WAAX,gBAAAA,IAAmB,SAAS,WAA5B,YAAsC;AAAA;AAAA,IACxC,EAAE,SAAS,mBAAmB;AAAA,EAChC;AACF,CAAC;AAEM,SAAS,oCACd,SAIA;AAEA,MAAI,mBAAmB,YAAY;AACjC,WAAO,EAAE,MAAM,SAAS,WAAW,OAAU;AAAA,EAC/C;AAGA,MAAI,mBAAmB,aAAa;AAClC,WAAO,EAAE,MAAM,IAAI,WAAW,OAAO,GAAG,WAAW,OAAU;AAAA,EAC/D;AAIA,MAAI,OAAO,YAAY,UAAU;AAC/B,QAAI;AACF,gBAAU,IAAI,IAAI,OAAO;AAAA,IAC3B,SAAS,OAAO;AAAA,IAEhB;AAAA,EACF;AAGA,MAAI,mBAAmB,OAAO,QAAQ,aAAa,SAAS;AAC1D,UAAM,EAAE,WAAW,kBAAkB,cAAc,IAAI;AAAA,MACrD,QAAQ,SAAS;AAAA,IACnB;AAEA,QAAI,oBAAoB,QAAQ,iBAAiB,MAAM;AACrD,YAAM,IAAI,4BAAW;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,sCAAsC,QAAQ,SAAS,CAAC;AAAA,MACnE,CAAC;AAAA,IACH;AAEA,WAAO,EAAE,MAAM,eAAe,WAAW,iBAAiB;AAAA,EAC5D;AAEA,SAAO,EAAE,MAAM,SAAS,WAAW,OAAU;AAC/C;;;AEpEA,IAAAC,mBAA2B;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,0BAAN,cAAsC,4BAAW;AAAA,EAKtD,YAAY;AAAA,IACV;AAAA,IACA,UAAU,0BAA0B,IAAI;AAAA,EAC1C,GAGG;AACD,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAXzB,SAAkBG,OAAU;AAa1B,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,WAAW,OAAkD;AAClE,WAAO,4BAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AAnBoBE,MAAAD;;;ANgBpB,eAAsB,6BAA6B;AAAA,EACjD;AAAA,EACA;AAAA,EACA,yBAAyB;AAC3B,GAImC;AACjC,QAAM,mBAAmB,MAAM;AAAA,IAC7B,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAI,OAAO,UAAU,OACjB,CAAC,EAAE,MAAM,UAAmB,SAAS,OAAO,OAAO,CAAC,IACpD,CAAC;AAAA,IACL,GAAG,OAAO,SAAS;AAAA,MAAI,aACrB,8BAA8B,EAAE,SAAS,iBAAiB,CAAC;AAAA,IAC7D;AAAA,EACF;AACF;AASO,SAAS,8BAA8B;AAAA,EAC5C;AAAA,EACA;AACF,GAM2B;AACzB,QAAM,OAAO,QAAQ;AACrB,UAAQ,MAAM;AAAA,IACZ,KAAK,UAAU;AACb,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ;AAAA,QACjB,iBAAiB,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,UACjD,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QACd,IAAI,UAAQ,+BAA+B,MAAM,gBAAgB,CAAC,EAElE,OAAO,UAAQ,KAAK,SAAS,UAAU,KAAK,SAAS,EAAE;AAAA,QAC1D,iBAAiB,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,KAAK,aAAa;AAChB,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,UACjD,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QACd;AAAA;AAAA,UAEC,UAAQ,KAAK,SAAS,UAAU,KAAK,SAAS;AAAA,QAChD,EACC,IAAI,UAAQ;AACX,gBAAM,kBAAkB,KAAK;AAE7B,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,oBAAM,EAAE,MAAM,UAAU,IAAI;AAAA,gBAC1B,KAAK;AAAA,cACP;AACA,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN;AAAA,gBACA,UAAU,KAAK;AAAA,gBACf,WAAW,gCAAa,KAAK;AAAA,gBAC7B;AAAA,cACF;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,YAAY,KAAK;AAAA,gBACjB,UAAU,KAAK;AAAA,gBACf,OAAO,KAAK;AAAA,gBACZ,kBAAkB,KAAK;AAAA,gBACvB;AAAA,cACF;AAAA,YACF;AAAA,YACA,KAAK,eAAe;AAClB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,YAAY,KAAK;AAAA,gBACjB,UAAU,KAAK;AAAA,gBACf,QAAQ,KAAK;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,QACH,iBAAiB,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QAAQ,IAAI,WAAS;AAAA,UACpC,MAAM;AAAA,UACN,YAAY,KAAK;AAAA,UACjB,UAAU,KAAK;AAAA,UACf,QAAQ,KAAK;AAAA,UACb,iBAAiB,KAAK;AAAA,QACxB,EAAE;AAAA,QACF,iBAAiB,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,IAEA,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,wBAAwB,EAAE,MAAM,iBAAiB,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAKA,eAAe,eACb,UACA,wBACA,eAGA;AACA,QAAM,OAAO,SACV,OAAO,aAAW,QAAQ,SAAS,MAAM,EACzC,IAAI,aAAW,QAAQ,OAAO,EAC9B;AAAA,IAAO,CAAC,YACP,MAAM,QAAQ,OAAO;AAAA,EACvB,EACC,KAAK,EACL;AAAA,IACC,CAAC,SACC,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,EAC3C,EACC,IAAI,UAAQ;AA/MjB,QAAAE;AAgNM,UAAM,aACJA,MAAA,KAAK,cAAL,OAAAA,MAAmB,KAAK,SAAS,UAAU,YAAY;AAEzD,QAAI,OAAO,KAAK,SAAS,UAAU,KAAK,QAAQ,KAAK;AACrD,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI;AACF,eAAO,IAAI,IAAI,IAAI;AAAA,MACrB,SAAS,SAAS;AAAA,MAAC;AAAA,IACrB;AAEA,WAAO,EAAE,WAAW,KAAK;AAAA,EAC3B,CAAC,EAIA;AAAA,IACC,CAAC,SACC,KAAK,gBAAgB,OACrB,KAAK,aAAa,QAClB,KAAC,uCAAe;AAAA,MACd,KAAK,KAAK,KAAK,SAAS;AAAA,MACxB,WAAW,KAAK;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACL,EACC,IAAI,UAAQ,KAAK,IAAI;AAGxB,QAAM,mBAAmB,MAAM,QAAQ;AAAA,IACrC,KAAK,IAAI,OAAM,SAAQ;AAAA,MACrB;AAAA,MACA,MAAM,MAAM,uBAAuB,EAAE,IAAI,CAAC;AAAA,IAC5C,EAAE;AAAA,EACJ;AAEA,SAAO,OAAO;AAAA,IACZ,iBAAiB,IAAI,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC;AAAA,EAChE;AACF;AAUA,SAAS,+BACP,MACA,kBAImD;AAtQrD,MAAAA;AAuQE,MAAI,KAAK,SAAS,QAAQ;AACxB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,MACX,iBAAiB,KAAK;AAAA,IACxB;AAAA,EACF;AAEA,MAAI;AACJ,QAAM,OAAO,KAAK;AAClB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,qBAAe,KAAK;AACpB;AAAA,IACF,KAAK;AACH,qBAAe,KAAK;AAEpB;AAAA,IACF;AACE,YAAM,IAAI,MAAM,0BAA0B,IAAI,EAAE;AAAA,EACpD;AAEA,QAAM,EAAE,MAAM,eAAe,WAAW,mBAAmB,IACzD,oCAAoC,YAAY;AAElD,MAAI,YAAgC,kDAAsB,KAAK;AAC/D,MAAI,OAAkC;AAGtC,MAAI,gBAAgB,KAAK;AACvB,UAAM,iBAAiB,iBAAiB,KAAK,SAAS,CAAC;AACvD,QAAI,gBAAgB;AAClB,aAAO,eAAe;AACtB,kDAAc,eAAe;AAAA,IAC/B;AAAA,EACF;AAIA,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AAIZ,UAAI,gBAAgB,cAAc,OAAO,SAAS,UAAU;AAC1D,qBACEA,MAAA,gBAAgB,EAAE,MAAM,YAAY,yBAAyB,CAAC,MAA9D,OAAAA,MACA;AAAA,MACJ;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,WAAW,gCAAa;AAAA;AAAA,QACxB,UAAU;AAAA,QACV;AAAA,QACA,iBAAiB,KAAK;AAAA,MACxB;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AAEX,UAAI,aAAa,MAAM;AACrB,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACvD;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,UAAU,KAAK;AAAA,QACf;AAAA,QACA,iBAAiB,KAAK;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF;;;AO5UA,IAAAC,yBAAyB;;;ACLlB,SAAS,iBACd,QACmC;AACnC,SAAO,UAAU,QAAQ,OAAO,KAAK,MAAM,EAAE,SAAS;AACxD;;;ADMO,SAAS,0BAAiD;AAAA,EAC/D;AAAA,EACA;AAAA,EACA;AACF,GASE;AACA,MAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,WAAO;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAGA,QAAM,gBACJ,eAAe,OACX,OAAO,QAAQ,KAAK,EAAE;AAAA,IAAO,CAAC,CAACC,KAAI,MACjC,YAAY,SAASA,KAAmB;AAAA,EAC1C,IACA,OAAO,QAAQ,KAAK;AAE1B,SAAO;AAAA,IACL,OAAO,cAAc,IAAI,CAAC,CAACA,OAAM,IAAI,MAAM;AACzC,YAAM,WAAW,KAAK;AACtB,cAAQ,UAAU;AAAA,QAChB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAAA;AAAA,YACA,aAAa,KAAK;AAAA,YAClB,iBAAa,iCAAS,KAAK,WAAW,EAAE;AAAA,YACxC,iBAAiB,KAAK;AAAA,UACxB;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAAA;AAAA,YACA,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,UACb;AAAA,QACF,SAAS;AACP,gBAAM,kBAAyB;AAC/B,gBAAM,IAAI,MAAM,0BAA0B,eAAe,EAAE;AAAA,QAC7D;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD,YACE,cAAc,OACV,EAAE,MAAM,OAAO,IACf,OAAO,eAAe,WACpB,EAAE,MAAM,WAAW,IACnB,EAAE,MAAM,QAAiB,UAAU,WAAW,SAAmB;AAAA,EAC3E;AACF;;;AEzEA,IAAAC,mBAAmC;AACnC,IAAAC,yBAAgD;AAChD,IAAAC,aAAkB;;;ACKlB,IAAAC,aAAkB;;;ACNlB,IAAAC,aAAkB;;;ACAlB,IAAAC,aAAkB;AAEX,IAAM,kBAAwC,aAAE;AAAA,EAAK,MAC1D,aAAE,MAAM;AAAA,IACN,aAAE,KAAK;AAAA,IACP,aAAE,OAAO;AAAA,IACT,aAAE,OAAO;AAAA,IACT,aAAE,QAAQ;AAAA,IACV,aAAE,OAAO,aAAE,OAAO,GAAG,eAAe;AAAA,IACpC,aAAE,MAAM,eAAe;AAAA,EACzB,CAAC;AACH;;;ADAO,IAAM,yBAAsD,aAAE;AAAA,EACnE,aAAE,OAAO;AAAA,EACT,aAAE,OAAO,aAAE,OAAO,GAAG,eAAe;AACtC;;;AENA,IAAAC,aAAkB;AAQX,IAAM,iBAAsC,aAAE,OAAO;AAAA,EAC1D,MAAM,aAAE,QAAQ,MAAM;AAAA,EACtB,MAAM,aAAE,OAAO;AAAA,EACf,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;AAKM,IAAM,kBAAwC,aAAE,OAAO;AAAA,EAC5D,MAAM,aAAE,QAAQ,OAAO;AAAA,EACvB,OAAO,aAAE,MAAM,CAAC,mBAAmB,aAAE,WAAW,GAAG,CAAC,CAAC;AAAA,EACrD,WAAW,aAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;AAKM,IAAM,iBAAsC,aAAE,OAAO;AAAA,EAC1D,MAAM,aAAE,QAAQ,MAAM;AAAA,EACtB,MAAM,aAAE,MAAM,CAAC,mBAAmB,aAAE,WAAW,GAAG,CAAC,CAAC;AAAA,EACpD,UAAU,aAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,WAAW,aAAE,OAAO;AAAA,EACpB,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;AAKM,IAAM,sBAAgD,aAAE,OAAO;AAAA,EACpE,MAAM,aAAE,QAAQ,WAAW;AAAA,EAC3B,MAAM,aAAE,OAAO;AAAA,EACf,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;AAkCM,IAAM,qBAA8C,aAAE,OAAO;AAAA,EAClE,MAAM,aAAE,QAAQ,WAAW;AAAA,EAC3B,YAAY,aAAE,OAAO;AAAA,EACrB,UAAU,aAAE,OAAO;AAAA,EACnB,OAAO,aAAE,QAAQ;AAAA,EACjB,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,kBAAkB,aAAE,QAAQ,EAAE,SAAS;AACzC,CAAC;AAKM,IAAM,eACX,aAAE,mBAAmB,QAAQ;AAAA,EAC3B,aAAE,OAAO;AAAA,IACP,MAAM,aAAE,QAAQ,MAAM;AAAA,IACtB,OAAO,aAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,aAAE,OAAO;AAAA,IACP,MAAM,aAAE,QAAQ,MAAM;AAAA,IACtB,OAAO;AAAA,EACT,CAAC;AAAA,EACD,aAAE,OAAO;AAAA,IACP,MAAM,aAAE,QAAQ,YAAY;AAAA,IAC5B,OAAO,aAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,aAAE,OAAO;AAAA,IACP,MAAM,aAAE,QAAQ,YAAY;AAAA,IAC5B,OAAO;AAAA,EACT,CAAC;AAAA,EACD,aAAE,OAAO;AAAA,IACP,MAAM,aAAE,QAAQ,SAAS;AAAA,IACzB,OAAO,aAAE;AAAA,MACP,aAAE,MAAM;AAAA,QACN,aAAE,OAAO;AAAA,UACP,MAAM,aAAE,QAAQ,MAAM;AAAA,UACtB,MAAM,aAAE,OAAO;AAAA,QACjB,CAAC;AAAA,QACD,aAAE,OAAO;AAAA,UACP,MAAM,aAAE,QAAQ,OAAO;AAAA,UACvB,MAAM,aAAE,OAAO;AAAA,UACf,WAAW,aAAE,OAAO;AAAA,QACtB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH,CAAC;AAKI,IAAM,uBAAkD,aAAE,OAAO;AAAA,EACtE,MAAM,aAAE,QAAQ,aAAa;AAAA,EAC7B,YAAY,aAAE,OAAO;AAAA,EACrB,UAAU,aAAE,OAAO;AAAA,EACnB,QAAQ;AAAA,EACR,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;;;AHtHM,IAAM,2BAA0D,aAAE;AAAA,EACvE;AAAA,IACE,MAAM,aAAE,QAAQ,QAAQ;AAAA,IACxB,SAAS,aAAE,OAAO;AAAA,IAClB,iBAAiB,uBAAuB,SAAS;AAAA,EACnD;AACF;AAcO,IAAM,yBAAsD,aAAE,OAAO;AAAA,EAC1E,MAAM,aAAE,QAAQ,MAAM;AAAA,EACtB,SAAS,aAAE,MAAM;AAAA,IACf,aAAE,OAAO;AAAA,IACT,aAAE,MAAM,aAAE,MAAM,CAAC,gBAAgB,iBAAiB,cAAc,CAAC,CAAC;AAAA,EACpE,CAAC;AAAA,EACD,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;AAcM,IAAM,8BACX,aAAE,OAAO;AAAA,EACP,MAAM,aAAE,QAAQ,WAAW;AAAA,EAC3B,SAAS,aAAE,MAAM;AAAA,IACf,aAAE,OAAO;AAAA,IACT,aAAE;AAAA,MACA,aAAE,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAAA,EACD,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;AAcI,IAAM,yBAAsD,aAAE,OAAO;AAAA,EAC1E,MAAM,aAAE,QAAQ,MAAM;AAAA,EACtB,SAAS,aAAE,MAAM,oBAAoB;AAAA,EACrC,iBAAiB,uBAAuB,SAAS;AACnD,CAAC;AAcM,IAAM,qBAA8C,aAAE,MAAM;AAAA,EACjE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ADpGD,eAAsB,kBACpB,QAC6B;AAC7B,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,oCAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,oCAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAGA,MAAI,OAAO,UAAU,QAAQ,OAAO,OAAO,WAAW,UAAU;AAC9D,UAAM,IAAI,oCAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI;AAEJ,MAAI,OAAO,UAAU,QAAQ,OAAO,OAAO,WAAW,UAAU;AAC9D,eAAW,CAAC,EAAE,MAAM,QAAQ,SAAS,OAAO,OAAO,CAAC;AAAA,EACtD,WAAW,OAAO,UAAU,QAAQ,MAAM,QAAQ,OAAO,MAAM,GAAG;AAChE,eAAW,OAAO;AAAA,EACpB,WAAW,OAAO,YAAY,MAAM;AAClC,eAAW,OAAO;AAAA,EACpB,OAAO;AACL,UAAM,IAAI,oCAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,WAAW,GAAG;AACzB,UAAM,IAAI,oCAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,QAAM,mBAAmB,UAAM,0CAAkB;AAAA,IAC/C,OAAO;AAAA,IACP,QAAQ,aAAE,MAAM,kBAAkB;AAAA,EACpC,CAAC;AAED,MAAI,CAAC,iBAAiB,SAAS;AAC7B,UAAM,IAAI,oCAAmB;AAAA,MAC3B;AAAA,MACA,SACE;AAAA,MAEF,OAAO,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,OAAO;AAAA,EACjB;AACF;;;AKpFA,IAAAC,mBAA2B;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,uBAAN,cAAmC,4BAAW;AAAA,EAMnD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM;AAAA,MACJ,MAAAH;AAAA,MACA,SAAS,kCAAkC,SAAS,KAAK,OAAO;AAAA,IAClE,CAAC;AAjBH,SAAkBG,OAAU;AAmB1B,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,WAAW,OAA+C;AAC/D,WAAO,4BAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AA1BoBE,MAAAD;;;ACDb,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAGE;AACA,MAAI,mBAAmB,MAAM;AAC3B,QAAI,CAAC,OAAO,UAAU,eAAe,GAAG;AACtC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,kBAAkB,GAAG;AACvB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,eAAe,MAAM;AACvB,QAAI,OAAO,gBAAgB,UAAU;AACnC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,mBAAmB,MAAM;AAC3B,QAAI,OAAO,oBAAoB,UAAU;AACvC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,oBAAoB,MAAM;AAC5B,QAAI,OAAO,qBAAqB,UAAU;AACxC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,CAAC,OAAO,UAAU,IAAI,GAAG;AAC3B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC3GA,IAAAE,mBAA6B;AAC7B,IAAAC,yBAAqD;;;ACDrD,IAAAC,mBAA2B;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAWO,IAAM,aAAN,cAAyB,4BAAW;AAAA,EAQzC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAhBzB,SAAkBG,OAAU;AAkB1B,SAAK,SAAS;AACd,SAAK,SAAS;AAGd,SAAK,YAAY,OAAO,OAAO,SAAS,CAAC;AAAA,EAC3C;AAAA,EAEA,OAAO,WAAW,OAAqC;AACrD,WAAO,4BAAW,UAAU,OAAOF,OAAM;AAAA,EAC3C;AACF;AA5BoBE,MAAAD;;;ADJpB,SAAS,kBAAkB;AAAA,EACzB;AAAA,EACA;AACF,GAGW;AACT,QAAM,UAAU,MAAM;AAEtB,MAAI,CAAC;AAAS,WAAO;AAErB,MAAI;AAGJ,QAAM,eAAe,QAAQ,gBAAgB;AAC7C,MAAI,cAAc;AAChB,UAAM,YAAY,WAAW,YAAY;AACzC,QAAI,CAAC,OAAO,MAAM,SAAS,GAAG;AAC5B,WAAK;AAAA,IACP;AAAA,EACF;AAGA,QAAM,aAAa,QAAQ,aAAa;AACxC,MAAI,cAAc,OAAO,QAAW;AAClC,UAAM,iBAAiB,WAAW,UAAU;AAC5C,QAAI,CAAC,OAAO,MAAM,cAAc,GAAG;AACjC,WAAK,iBAAiB;AAAA,IACxB,OAAO;AACL,WAAK,KAAK,MAAM,UAAU,IAAI,KAAK,IAAI;AAAA,IACzC;AAAA,EACF;AAGA,MACE,MAAM,QACN,CAAC,OAAO,MAAM,EAAE,KAChB,KAAK,OACJ,KAAK,KAAK,OAAQ,KAAK,0BACxB;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAOO,IAAM,oDACX,CAAC;AAAA,EACC,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB;AACF,IAKI,CAAC,MACL,OAAe,MACb,6BAA6B,GAAG;AAAA,EAC9B;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AACF,CAAC;AAEL,eAAe,6BACb,GACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAMA,SAAoB,CAAC,GACJ;AACjB,MAAI;AACF,WAAO,MAAM,EAAE;AAAA,EACjB,SAAS,OAAO;AACd,YAAI,qCAAa,KAAK,GAAG;AACvB,YAAM;AAAA,IACR;AAEA,QAAI,eAAe,GAAG;AACpB,YAAM;AAAA,IACR;AAEA,UAAM,mBAAe,wCAAgB,KAAK;AAC1C,UAAM,YAAY,CAAC,GAAG,QAAQ,KAAK;AACnC,UAAM,YAAY,UAAU;AAE5B,QAAI,YAAY,YAAY;AAC1B,YAAM,IAAI,WAAW;AAAA,QACnB,SAAS,gBAAgB,SAAS,0BAA0B,YAAY;AAAA,QACxE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,QACE,iBAAiB,SACjB,8BAAa,WAAW,KAAK,KAC7B,MAAM,gBAAgB,QACtB,aAAa,YACb;AACA,gBAAM;AAAA,QACJ,kBAAkB;AAAA,UAChB;AAAA,UACA,yBAAyB;AAAA,QAC3B,CAAC;AAAA,QACD,EAAE,YAAY;AAAA,MAChB;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,UACE;AAAA,UACA,WAAW,gBAAgB;AAAA,UAC3B;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI,cAAc,GAAG;AACnB,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,WAAW;AAAA,MACnB,SAAS,gBAAgB,SAAS,wCAAwC,YAAY;AAAA,MACtF,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;;;AEhJO,SAAS,eAAe;AAAA,EAC7B;AAAA,EACA;AACF,GAME;AACA,MAAI,cAAc,MAAM;AACtB,QAAI,CAAC,OAAO,UAAU,UAAU,GAAG;AACjC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,aAAa,GAAG;AAClB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,mBAAmB,kCAAc;AAEvC,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,OAAO,kDAAkD;AAAA,MACvD,YAAY;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AACF;", "names": ["import_provider_utils", "import_provider_utils", "_a", "import_provider", "import_provider_utils", "_a", "import_provider", "name", "marker", "symbol", "_a", "_a", "import_provider_utils", "name", "import_provider", "import_provider_utils", "import_v4", "import_v4", "import_v4", "import_v4", "import_v4", "import_provider", "name", "marker", "symbol", "_a", "import_provider", "import_provider_utils", "import_provider", "name", "marker", "symbol", "_a"]}