{"version": 3, "sources": ["../../test/index.ts", "../../src/test/not-implemented.ts", "../../src/test/mock-embedding-model-v2.ts", "../../src/test/mock-image-model-v2.ts", "../../src/test/mock-language-model-v2.ts", "../../src/test/mock-provider-v2.ts", "../../src/test/mock-speech-model-v2.ts", "../../src/test/mock-transcription-model-v2.ts", "../../src/test/mock-values.ts", "../../src/util/simulate-readable-stream.ts"], "sourcesContent": ["export {\n  convertArrayToAsyncIterable,\n  convertArrayToReadableStream,\n  convertReadableStreamToArray,\n  mockId,\n} from '@ai-sdk/provider-utils/test';\nexport { MockEmbeddingModelV2 } from '../src/test/mock-embedding-model-v2';\nexport { MockImageModelV2 } from '../src/test/mock-image-model-v2';\nexport { MockLanguageModelV2 } from '../src/test/mock-language-model-v2';\nexport { MockProviderV2 } from '../src/test/mock-provider-v2';\nexport { MockSpeechModelV2 } from '../src/test/mock-speech-model-v2';\nexport { MockTranscriptionModelV2 } from '../src/test/mock-transcription-model-v2';\nexport { mockValues } from '../src/test/mock-values';\n\nimport { simulateReadableStream as originalSimulateReadableStream } from '../src/util/simulate-readable-stream';\n\n/**\n * @deprecated Use `simulateReadableStream` from `ai` instead.\n */\nexport const simulateReadableStream = originalSimulateReadableStream;\n", "export function notImplemented(): never {\n  throw new Error('Not implemented');\n}\n", "import { EmbeddingModelV2 } from '@ai-sdk/provider';\nimport { Embedding } from '../types';\nimport { EmbeddingModelUsage } from '../types/usage';\nimport { notImplemented } from './not-implemented';\n\nexport class MockEmbeddingModelV2<VALUE> implements EmbeddingModelV2<VALUE> {\n  readonly specificationVersion = 'v2';\n\n  readonly provider: EmbeddingModelV2<VALUE>['provider'];\n  readonly modelId: EmbeddingModelV2<VALUE>['modelId'];\n  readonly maxEmbeddingsPerCall: EmbeddingModelV2<VALUE>['maxEmbeddingsPerCall'];\n  readonly supportsParallelCalls: EmbeddingModelV2<VALUE>['supportsParallelCalls'];\n\n  doEmbed: EmbeddingModelV2<VALUE>['doEmbed'];\n\n  constructor({\n    provider = 'mock-provider',\n    modelId = 'mock-model-id',\n    maxEmbeddingsPerCall = 1,\n    supportsParallelCalls = false,\n    doEmbed = notImplemented,\n  }: {\n    provider?: EmbeddingModelV2<VALUE>['provider'];\n    modelId?: EmbeddingModelV2<VALUE>['modelId'];\n    maxEmbeddingsPerCall?:\n      | EmbeddingModelV2<VALUE>['maxEmbeddingsPerCall']\n      | null;\n    supportsParallelCalls?: EmbeddingModelV2<VALUE>['supportsParallelCalls'];\n    doEmbed?: EmbeddingModelV2<VALUE>['doEmbed'];\n  } = {}) {\n    this.provider = provider;\n    this.modelId = modelId;\n    this.maxEmbeddingsPerCall = maxEmbeddingsPerCall ?? undefined;\n    this.supportsParallelCalls = supportsParallelCalls;\n    this.doEmbed = doEmbed;\n  }\n}\n\nexport function mockEmbed<VALUE>(\n  expectedValues: Array<VALUE>,\n  embeddings: Array<Embedding>,\n  usage?: EmbeddingModelUsage,\n  response: Awaited<\n    ReturnType<EmbeddingModelV2<VALUE>['doEmbed']>\n  >['response'] = { headers: {}, body: {} },\n  providerMetadata?: Awaited<\n    ReturnType<EmbeddingModelV2<VALUE>['doEmbed']>\n  >['providerMetadata'],\n): EmbeddingModelV2<VALUE>['doEmbed'] {\n  return async ({ values }) => {\n    assert.deepStrictEqual(expectedValues, values);\n    return { embeddings, usage, response, providerMetadata };\n  };\n}\n", "import { ImageModelV2 } from '@ai-sdk/provider';\nimport { notImplemented } from './not-implemented';\n\nexport class MockImageModelV2 implements ImageModelV2 {\n  readonly specificationVersion = 'v2';\n  readonly provider: ImageModelV2['provider'];\n  readonly modelId: ImageModelV2['modelId'];\n  readonly maxImagesPerCall: ImageModelV2['maxImagesPerCall'];\n\n  doGenerate: ImageModelV2['doGenerate'];\n\n  constructor({\n    provider = 'mock-provider',\n    modelId = 'mock-model-id',\n    maxImagesPerCall = 1,\n    doGenerate = notImplemented,\n  }: {\n    provider?: ImageModelV2['provider'];\n    modelId?: ImageModelV2['modelId'];\n    maxImagesPerCall?: ImageModelV2['maxImagesPerCall'];\n    doGenerate?: ImageModelV2['doGenerate'];\n  } = {}) {\n    this.provider = provider;\n    this.modelId = modelId;\n    this.maxImagesPerCall = maxImagesPerCall;\n    this.doGenerate = doGenerate;\n  }\n}\n", "import { LanguageModelV2 } from '@ai-sdk/provider';\nimport { notImplemented } from './not-implemented';\n\nexport class MockLanguageModelV2 implements LanguageModelV2 {\n  readonly specificationVersion = 'v2';\n\n  private _supportedUrls: () => LanguageModelV2['supportedUrls'];\n\n  readonly provider: LanguageModelV2['provider'];\n  readonly modelId: LanguageModelV2['modelId'];\n\n  doGenerate: LanguageModelV2['doGenerate'];\n  doStream: LanguageModelV2['doStream'];\n\n  doGenerateCalls: Parameters<LanguageModelV2['doGenerate']>[0][] = [];\n  doStreamCalls: Parameters<LanguageModelV2['doStream']>[0][] = [];\n\n  constructor({\n    provider = 'mock-provider',\n    modelId = 'mock-model-id',\n    supportedUrls = {},\n    doGenerate = notImplemented,\n    doStream = notImplemented,\n  }: {\n    provider?: LanguageModelV2['provider'];\n    modelId?: LanguageModelV2['modelId'];\n    supportedUrls?:\n      | LanguageModelV2['supportedUrls']\n      | (() => LanguageModelV2['supportedUrls']);\n    doGenerate?:\n      | LanguageModelV2['doGenerate']\n      | Awaited<ReturnType<LanguageModelV2['doGenerate']>>\n      | Awaited<ReturnType<LanguageModelV2['doGenerate']>>[];\n    doStream?:\n      | LanguageModelV2['doStream']\n      | Awaited<ReturnType<LanguageModelV2['doStream']>>\n      | Awaited<ReturnType<LanguageModelV2['doStream']>>[];\n  } = {}) {\n    this.provider = provider;\n    this.modelId = modelId;\n    this.doGenerate = async options => {\n      this.doGenerateCalls.push(options);\n\n      if (typeof doGenerate === 'function') {\n        return doGenerate(options);\n      } else if (Array.isArray(doGenerate)) {\n        return doGenerate[this.doGenerateCalls.length];\n      } else {\n        return doGenerate;\n      }\n    };\n    this.doStream = async options => {\n      this.doStreamCalls.push(options);\n\n      if (typeof doStream === 'function') {\n        return doStream(options);\n      } else if (Array.isArray(doStream)) {\n        return doStream[this.doStreamCalls.length];\n      } else {\n        return doStream;\n      }\n    };\n    this._supportedUrls =\n      typeof supportedUrls === 'function'\n        ? supportedUrls\n        : async () => supportedUrls;\n  }\n\n  get supportedUrls() {\n    return this._supportedUrls();\n  }\n}\n", "import {\n  Embedding<PERSON>odelV2,\n  ImageModelV2,\n  LanguageModelV2,\n  NoSuchModelError,\n  ProviderV2,\n  SpeechModelV2,\n  TranscriptionModelV2,\n} from '@ai-sdk/provider';\n\nexport class MockProviderV2 implements ProviderV2 {\n  languageModel: ProviderV2['languageModel'];\n  textEmbeddingModel: ProviderV2['textEmbeddingModel'];\n  imageModel: ProviderV2['imageModel'];\n  transcriptionModel: ProviderV2['transcriptionModel'];\n  speechModel: ProviderV2['speechModel'];\n\n  constructor({\n    languageModels,\n    embeddingModels,\n    imageModels,\n    transcriptionModels,\n    speechModels,\n  }: {\n    languageModels?: Record<string, LanguageModelV2>;\n    embeddingModels?: Record<string, EmbeddingModelV2<string>>;\n    imageModels?: Record<string, ImageModelV2>;\n    transcriptionModels?: Record<string, TranscriptionModelV2>;\n    speechModels?: Record<string, SpeechModelV2>;\n  } = {}) {\n    this.languageModel = (modelId: string) => {\n      if (!languageModels?.[modelId]) {\n        throw new NoSuchModelError({ modelId, modelType: 'languageModel' });\n      }\n      return languageModels[modelId];\n    };\n    this.textEmbeddingModel = (modelId: string) => {\n      if (!embeddingModels?.[modelId]) {\n        throw new NoSuchModelError({\n          modelId,\n          modelType: 'textEmbeddingModel',\n        });\n      }\n      return embeddingModels[modelId];\n    };\n    this.imageModel = (modelId: string) => {\n      if (!imageModels?.[modelId]) {\n        throw new NoSuchModelError({ modelId, modelType: 'imageModel' });\n      }\n      return imageModels[modelId];\n    };\n    this.transcriptionModel = (modelId: string) => {\n      if (!transcriptionModels?.[modelId]) {\n        throw new NoSuchModelError({\n          modelId,\n          modelType: 'transcriptionModel',\n        });\n      }\n      return transcriptionModels[modelId];\n    };\n    this.speechModel = (modelId: string) => {\n      if (!speechModels?.[modelId]) {\n        throw new NoSuchModelError({ modelId, modelType: 'speechModel' });\n      }\n      return speechModels[modelId];\n    };\n  }\n}\n", "import { SpeechModelV2 } from '@ai-sdk/provider';\nimport { notImplemented } from './not-implemented';\n\nexport class MockSpeechModelV2 implements SpeechModelV2 {\n  readonly specificationVersion = 'v2';\n  readonly provider: SpeechModelV2['provider'];\n  readonly modelId: SpeechModelV2['modelId'];\n\n  doGenerate: SpeechModelV2['doGenerate'];\n\n  constructor({\n    provider = 'mock-provider',\n    modelId = 'mock-model-id',\n    doGenerate = notImplemented,\n  }: {\n    provider?: SpeechModelV2['provider'];\n    modelId?: SpeechModelV2['modelId'];\n    doGenerate?: SpeechModelV2['doGenerate'];\n  } = {}) {\n    this.provider = provider;\n    this.modelId = modelId;\n    this.doGenerate = doGenerate;\n  }\n}\n", "import { TranscriptionModelV2 } from '@ai-sdk/provider';\nimport { notImplemented } from './not-implemented';\n\nexport class MockTranscriptionModelV2 implements TranscriptionModelV2 {\n  readonly specificationVersion = 'v2';\n  readonly provider: TranscriptionModelV2['provider'];\n  readonly modelId: TranscriptionModelV2['modelId'];\n\n  doGenerate: TranscriptionModelV2['doGenerate'];\n\n  constructor({\n    provider = 'mock-provider',\n    modelId = 'mock-model-id',\n    doGenerate = notImplemented,\n  }: {\n    provider?: TranscriptionModelV2['provider'];\n    modelId?: TranscriptionModelV2['modelId'];\n    doGenerate?: TranscriptionModelV2['doGenerate'];\n  } = {}) {\n    this.provider = provider;\n    this.modelId = modelId;\n    this.doGenerate = doGenerate;\n  }\n}\n", "export function mockValues<T>(...values: T[]): () => T {\n  let counter = 0;\n  return () => values[counter++] ?? values[values.length - 1];\n}\n", "import { delay as delayFunction } from '@ai-sdk/provider-utils';\n\n/**\n * Creates a ReadableStream that emits the provided values with an optional delay between each value.\n *\n * @param options - The configuration options\n * @param options.chunks - Array of values to be emitted by the stream\n * @param options.initialDelayInMs - Optional initial delay in milliseconds before emitting the first value (default: 0). Can be set to `null` to skip the initial delay. The difference between `initialDelayInMs: null` and `initialDelayInMs: 0` is that `initialDelayInMs: null` will emit the values without any delay, while `initialDelayInMs: 0` will emit the values with a delay of 0 milliseconds.\n * @param options.chunkDelayInMs - Optional delay in milliseconds between emitting each value (default: 0). Can be set to `null` to skip the delay. The difference between `chunkDelayInMs: null` and `chunkDelayInMs: 0` is that `chunkDelayInMs: null` will emit the values without any delay, while `chunkDelayInMs: 0` will emit the values with a delay of 0 milliseconds.\n * @returns A ReadableStream that emits the provided values\n */\nexport function simulateReadableStream<T>({\n  chunks,\n  initialDelayInMs = 0,\n  chunkDelayInMs = 0,\n  _internal,\n}: {\n  chunks: T[];\n  initialDelayInMs?: number | null;\n  chunkDelayInMs?: number | null;\n  _internal?: {\n    delay?: (ms: number | null) => Promise<void>;\n  };\n}): ReadableStream<T> {\n  const delay = _internal?.delay ?? delayFunction;\n\n  let index = 0;\n\n  return new ReadableStream({\n    async pull(controller) {\n      if (index < chunks.length) {\n        await delay(index === 0 ? initialDelayInMs : chunkDelayInMs);\n        controller.enqueue(chunks[index++]);\n      } else {\n        controller.close();\n      }\n    },\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAAAA;AAAA;AAAA;AAAA,kBAKO;;;ACLA,SAAS,iBAAwB;AACtC,QAAM,IAAI,MAAM,iBAAiB;AACnC;;;ACGO,IAAM,uBAAN,MAAqE;AAAA,EAU1E,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,UAAU;AAAA,EACZ,IAQI,CAAC,GAAG;AAvBR,SAAS,uBAAuB;AAwB9B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,uBAAuB,sDAAwB;AACpD,SAAK,wBAAwB;AAC7B,SAAK,UAAU;AAAA,EACjB;AACF;;;ACjCO,IAAM,mBAAN,MAA+C;AAAA,EAQpD,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,aAAa;AAAA,EACf,IAKI,CAAC,GAAG;AAjBR,SAAS,uBAAuB;AAkB9B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAAA,EACpB;AACF;;;ACxBO,IAAM,sBAAN,MAAqD;AAAA,EAc1D,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,gBAAgB,CAAC;AAAA,IACjB,aAAa;AAAA,IACb,WAAW;AAAA,EACb,IAcI,CAAC,GAAG;AAjCR,SAAS,uBAAuB;AAUhC,2BAAkE,CAAC;AACnE,yBAA8D,CAAC;AAuB7D,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa,OAAM,YAAW;AACjC,WAAK,gBAAgB,KAAK,OAAO;AAEjC,UAAI,OAAO,eAAe,YAAY;AACpC,eAAO,WAAW,OAAO;AAAA,MAC3B,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,eAAO,WAAW,KAAK,gBAAgB,MAAM;AAAA,MAC/C,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,WAAW,OAAM,YAAW;AAC/B,WAAK,cAAc,KAAK,OAAO;AAE/B,UAAI,OAAO,aAAa,YAAY;AAClC,eAAO,SAAS,OAAO;AAAA,MACzB,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAClC,eAAO,SAAS,KAAK,cAAc,MAAM;AAAA,MAC3C,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,iBACH,OAAO,kBAAkB,aACrB,gBACA,YAAY;AAAA,EACpB;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,eAAe;AAAA,EAC7B;AACF;;;ACvEA,sBAQO;AAEA,IAAM,iBAAN,MAA2C;AAAA,EAOhD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAMI,CAAC,GAAG;AACN,SAAK,gBAAgB,CAAC,YAAoB;AACxC,UAAI,EAAC,iDAAiB,WAAU;AAC9B,cAAM,IAAI,iCAAiB,EAAE,SAAS,WAAW,gBAAgB,CAAC;AAAA,MACpE;AACA,aAAO,eAAe,OAAO;AAAA,IAC/B;AACA,SAAK,qBAAqB,CAAC,YAAoB;AAC7C,UAAI,EAAC,mDAAkB,WAAU;AAC/B,cAAM,IAAI,iCAAiB;AAAA,UACzB;AAAA,UACA,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AACA,aAAO,gBAAgB,OAAO;AAAA,IAChC;AACA,SAAK,aAAa,CAAC,YAAoB;AACrC,UAAI,EAAC,2CAAc,WAAU;AAC3B,cAAM,IAAI,iCAAiB,EAAE,SAAS,WAAW,aAAa,CAAC;AAAA,MACjE;AACA,aAAO,YAAY,OAAO;AAAA,IAC5B;AACA,SAAK,qBAAqB,CAAC,YAAoB;AAC7C,UAAI,EAAC,2DAAsB,WAAU;AACnC,cAAM,IAAI,iCAAiB;AAAA,UACzB;AAAA,UACA,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AACA,aAAO,oBAAoB,OAAO;AAAA,IACpC;AACA,SAAK,cAAc,CAAC,YAAoB;AACtC,UAAI,EAAC,6CAAe,WAAU;AAC5B,cAAM,IAAI,iCAAiB,EAAE,SAAS,WAAW,cAAc,CAAC;AAAA,MAClE;AACA,aAAO,aAAa,OAAO;AAAA,IAC7B;AAAA,EACF;AACF;;;AChEO,IAAM,oBAAN,MAAiD;AAAA,EAOtD,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,aAAa;AAAA,EACf,IAII,CAAC,GAAG;AAdR,SAAS,uBAAuB;AAe9B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAAA,EACpB;AACF;;;ACpBO,IAAM,2BAAN,MAA+D;AAAA,EAOpE,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,aAAa;AAAA,EACf,IAII,CAAC,GAAG;AAdR,SAAS,uBAAuB;AAe9B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAAA,EACpB;AACF;;;ACvBO,SAAS,cAAiB,QAAsB;AACrD,MAAI,UAAU;AACd,SAAO,MAAG;AAFZ;AAEe,wBAAO,SAAS,MAAhB,YAAqB,OAAO,OAAO,SAAS,CAAC;AAAA;AAC5D;;;ACHA,4BAAuC;AAWhC,SAAS,uBAA0B;AAAA,EACxC;AAAA,EACA,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB;AACF,GAOsB;AAvBtB;AAwBE,QAAM,SAAQ,4CAAW,UAAX,YAAoB,sBAAAC;AAElC,MAAI,QAAQ;AAEZ,SAAO,IAAI,eAAe;AAAA,IACxB,MAAM,KAAK,YAAY;AACrB,UAAI,QAAQ,OAAO,QAAQ;AACzB,cAAM,MAAM,UAAU,IAAI,mBAAmB,cAAc;AAC3D,mBAAW,QAAQ,OAAO,OAAO,CAAC;AAAA,MACpC,OAAO;AACL,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ATnBO,IAAMC,0BAAyB;", "names": ["simulateReadableStream", "delayFunction", "simulateReadableStream"]}