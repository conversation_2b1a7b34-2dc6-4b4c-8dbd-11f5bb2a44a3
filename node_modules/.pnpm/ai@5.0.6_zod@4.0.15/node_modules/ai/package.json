{"name": "ai", "version": "5.0.6", "description": "AI SDK by Vercel - The AI Toolkit for TypeScript and JavaScript", "license": "Apache-2.0", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**/*", "CHANGELOG.md", "internal.d.ts", "mcp-stdio.d.ts", "test.d.ts"], "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./internal": {"types": "./dist/internal/index.d.ts", "import": "./dist/internal/index.mjs", "module": "./dist/internal/index.mjs", "require": "./dist/internal/index.js"}, "./test": {"types": "./dist/test/index.d.ts", "import": "./dist/test/index.mjs", "module": "./dist/test/index.mjs", "require": "./dist/test/index.js"}, "./mcp-stdio": {"types": "./dist/mcp-stdio/index.d.ts", "import": "./dist/mcp-stdio/index.mjs", "module": "./dist/mcp-stdio/index.mjs", "require": "./dist/mcp-stdio/index.js"}}, "dependencies": {"@opentelemetry/api": "1.9.0", "@ai-sdk/gateway": "1.0.3", "@ai-sdk/provider": "2.0.0", "@ai-sdk/provider-utils": "3.0.1"}, "devDependencies": {"@edge-runtime/vm": "^5.0.0", "@types/json-schema": "7.0.15", "@types/node": "20.17.24", "eslint": "8.57.1", "tsup": "^7.2.0", "typescript": "5.8.3", "zod": "3.25.76", "@vercel/ai-tsconfig": "0.0.0", "eslint-config-vercel-ai": "0.0.0"}, "peerDependencies": {"zod": "^3.25.76 || ^4"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "homepage": "https://ai-sdk.dev/docs", "repository": {"type": "git", "url": "git+https://github.com/vercel/ai.git"}, "bugs": {"url": "https://github.com/vercel/ai/issues"}, "keywords": ["ai", "vercel", "sdk", "mcp", "tool-calling", "tools", "structured-output", "agent", "agentic", "generative", "chatbot", "prompt", "inference", "llm", "language-model", "streaming"], "scripts": {"build": "pnpm clean && tsup --tsconfig tsconfig.build.json", "build:watch": "pnpm clean && tsup --watch --tsconfig tsconfig.build.json", "clean": "rm -rf dist *.tsbuildinfo", "lint": "eslint \"./**/*.ts*\"", "type-check": "tsc --build", "prettier-check": "prettier --check \"./**/*.ts*\"", "test": "pnpm test:node && pnpm test:edge", "test:update": "pnpm test:node -u", "test:watch": "vitest --config vitest.node.config.js", "test:edge": "vitest --config vitest.edge.config.js --run", "test:node": "vitest --config vitest.node.config.js --run"}}