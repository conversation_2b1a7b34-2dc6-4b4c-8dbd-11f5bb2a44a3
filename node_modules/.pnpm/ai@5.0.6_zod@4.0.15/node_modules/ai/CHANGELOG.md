# ai

## 5.0.6

### Patch Changes

- d983eee: feat(ai): allow passing model string for embeddings

## 5.0.5

### Patch Changes

- Updated dependencies [893aed6]
  - @ai-sdk/gateway@1.0.3

## 5.0.4

### Patch Changes

- Updated dependencies [444df49]
  - @ai-sdk/gateway@1.0.2

## 5.0.3

### Patch Changes

- 90d212f: feat (ai): add experimental tool call context
- Updated dependencies [028fb9c]
- Updated dependencies [90d212f]
- Updated dependencies [6331826]
  - @ai-sdk/gateway@1.0.1
  - @ai-sdk/provider-utils@3.0.1

## 5.0.2

### Patch Changes

- 401d73e: fix (ai): support dynamic tool calls in lastAssistantMessageIsCompleteWithToolCalls
- 69fde99: Set status to ready when reconnect stream is null

## 5.0.1

### Patch Changes

- 4d0c108: fix(ai/ui): convert provider metadata for system messages to model messages

## 5.0.0

### Major Changes

- e1cbf8a: chore(@ai-sdk/rsc): extract to separate package
- a847c3e: chore: rename reasoning to reasoningText etc
- 13fef90: chore (ai): remove automatic conversion of UI messages to model messages
- d964901: - remove setting temperature to `0` by default
  - remove `null` option from `DefaultSettingsMiddleware`
  - remove setting defaults for `temperature` and `stopSequences` in `ai` to enable middleware changes
- 0a710d8: feat (ui): typed tool parts in ui messages
- 9ad0484: feat (ai): automatic tool execution error handling
- 63f9e9b: chore (provider,ai): tools have input/output instead of args,result
- ab7ccef: chore (ai): change source ui message parts to source-url
- d5f588f: AI SDK 5
- ec78cdc: chore (ai): remove "data" UIMessage role
- 6a83f7d: refactoring (ai): restructure message metadata transfer
- db345da: chore (ai): remove exports of internal ui functions
- 496bbc1: chore (ui): inline/remove ChatRequest type
- 72d7d72: chore (ai): stable activeTools
- 40acf9b: feat (ui): introduce ChatStore and ChatTransport
- 98f25e5: chore (ui): remove managed chat inputs
- 2d03e19: chore (ai): remove StreamCallbacks.onCompletion
- da70d79: chore (ai): remove getUIText helper
- c60f895: chore (ai): remove useChat keepLastMessageOnError
- 0560977: chore (ai): improve consistency of generate text result, stream text result, and step result
- 9477ebb: chore (ui): remove useAssistant hook (**breaking change**)
- 1f55c21: chore (ai): send reasoning to the client by default
- e7dc6c7: chore (ai): remove onResponse callback
- 8b86e99: chore (ai): replace `Message` with `UIMessage`
- 04d5063: chore (ai): rename default provider global to AI_SDK_DEFAULT_PROVIDER
- 319b989: chore (ai): remove content from ui messages
- 14c9410: chore: refactor file towards source pattern (spec)
- a34eb39: chore (ai): remove `data` and `allowEmptySubmit` from `ChatRequestOptions`
- f04fb4a: chore (ai): replace useChat attachments with file ui parts
- f7e8bf4: chore (ai): flatten ui message stream parts
- 257224b: chore (ai): separate TextStreamChatTransport
- fd1924b: chore (ai): remove redundant `mimeType` property
- 2524fc7: chore (ai): remove ui message toolInvocations property
- 6fba4c7: chore (ai): remove deprecated experimental_providerMetadata
- b4b4bb2: chore (ui): rename experimental_resume to resumeStream
- 441d042: chore (ui): data stream protocol v2 with SSEs
- ef256ed: chore (ai): refactor and use chatstore in svelte
- 516be5b: ### Move Image Model Settings into generate options

  Image Models no longer have settings. Instead, `maxImagesPerCall` can be passed directly to `generateImage()`. All other image settings can be passed to `providerOptions[provider]`.

  Before

  ```js
  await generateImage({
    model: luma.image('photon-flash-1', {
      maxImagesPerCall: 5,
      pollIntervalMillis: 500,
    }),
    prompt,
    n: 10,
  });
  ```

  After

  ```js
  await generateImage({
    model: luma.image('photon-flash-1'),
    prompt,
    n: 10,
    maxImagesPerCall: 5,
    providerOptions: {
      luma: { pollIntervalMillis: 5 },
    },
  });
  ```

  Pull Request: https://github.com/vercel/ai/pull/6180

- a662dea: chore (ai): remove sendExtraMessageFields
- d884051: feat (ai): simplify default provider setup
- e8324c5: feat (ai): add args callbacks to tools
- fafc3f2: chore (ai): change file to parts to use urls instead of data
- 1ed0287: chore (ai): stable sendStart/sendFinish options
- c7710a9: chore (ai): rename DataStreamToSSETransformStream to JsonToSseTransformStream
- bfbfc4c: feat (ai): streamText/generateText: totalUsage contains usage for all steps. usage is for a single step.
- 9ae327d: chore (ui): replace chat store concept with chat instances
- 9315076: chore (ai): rename continueUntil to stopWhen. Rename maxSteps stop condition to stepCountIs.
- 247ee0c: chore (ai): remove steps from tool invocation ui parts
- 109c0ac: chore (ai): rename id to chatId (in post request, resume request, and useChat)
- 954aa73: feat (ui): extended regenerate support
- 33eb499: feat (ai): inject message id in createUIMessageStream
- 901df02: feat (ui): use UI_MESSAGE generic
- 4892798: chore (ai): always stream tool calls
- c25cbce: feat (ai): use console.error as default error handler for streamText and streamObject
- b33ed7a: chore (ai): rename DataStream* to UIMessage*
- ed675de: feat (ai): add ui data parts
- 7bb58d4: chore (ai): restructure prepareRequest
- ea7a7c9: feat (ui): UI message metadata
- 0463011: fix (ai): update source url stream part
- dcc549b: remove StreamTextResult.mergeIntoDataStream method
  rename DataStreamOptions.getErrorMessage to onError
  add pipeTextStreamToResponse function
  add createTextStreamResponse function
  change createDataStreamResponse function to accept a DataStream and not a DataStreamWriter
  change pipeDataStreamToResponse function to accept a DataStream and not a DataStreamWriter
  change pipeDataStreamToResponse function to have a single parameter
- 35fc02c: chore (ui): rename RequestOptions to CompletionRequestOptions
- 64f6d64: feat (ai): replace maxSteps with continueUntil (generateText)
- 175b868: chore (ai): rename reasoning UI parts 'reasoning' property to 'text'
- 60e2c56: feat (ai): restructure chat transports
- 765f1cd: chore (ai): remove deprecated useChat isLoading helper
- cb2b53a: chore (ai): refactor header preparation
- e244a78: chore (ai): remove StreamData and mergeStreams
- d306260: feat (ai): replace maxSteps with continueUntil (streamText)
- 4bfe9ec: chore (ai): remove ui message reasoning property
- 1766ede: chore: rename maxTokens to maxOutputTokens
- 2877a74: chore (ai): remove ui message data property
- 1409e13: chore (ai): remove experimental continueSteps
- b32e192: chore (ai): rename reasoning to reasoningText, rename reasoningDetails to reasoning (streamText, generateText)
- 92cb0a2: chore (ai): rename CoreMessage to ModelMessage
- 2b637d6: chore (ai): rename UIMessageStreamPart to UIMessageChunk

### Minor Changes

- b7eae2d: feat (core): Add finishReason field to NoObjectGeneratedError
- bcea599: feat (ai): add content to generateText result
- 48d675a: feat (ai): add content to streamText result
- c9ad635: feat (ai): add filename to file ui parts

### Patch Changes

- a571d6e: chore(provider-utils): move ToolResultContent to provider-utils
- de2d2ab: feat(ai): add provider and provider registry middleware functionality
- c22ad54: feat(smooth-stream): chunking callbacks
- d88455d: feat (ai): expose http chat transport type
- e7fcc86: feat (ai): introduce dynamic tools
- da1e6f0: feat (ui): add generics to ui message stream parts
- 48378b9: fix (ai): send null as tool output when tools return undefined
- 5d1e3ba: chore (ai): remove provider re-exports
- 93d53a1: chore (ai): remove cli
- e90d45d: chore (rsc): move HANGING_STREAM_WARNING_TIME constant into @ai-sdk/rsc package
- b32c141: feat (ai): add array support to stopWhen
- bc3109f: chore (ai): push stream-callbacks into langchain/llamaindex adapters
- 0d9583c: fix (ai): use user-provided media type when available
- 38ae5cc: feat (ai): export InferUIMessageChunk type
- 10b21eb: feat(cli): add ai command line interface
- 9e40cbe: Allow destructuring output and errorText on `ToolUIPart` type
- 6909543: feat (ai): support system parameter in Agent constructor
- 86cfc72: feat (ai): add ignoreIncompleteToolCalls option to convertToModelMessages
- 377bbcf: fix (ui): tool input can be undefined during input-streaming
- d8aeaef: feat(providers/fal): add transcribe
- ae77a99: chore (ai): rename text and reasoning chunks in streamText fullstream
- 4fef487: feat: support for zod v4 for schema validation

  All these methods now accept both a zod v4 and zod v3 schemas for validation:

  - `generateObject()`
  - `streamObject()`
  - `generateText()`
  - `experimental_useObject()` from `@ai-sdk/react`
  - `streamUI()` from `@ai-sdk/rsc`

- b1e3abd: feat (ai): expose ui message stream headers
- 4f3e637: fix (ui): avoid caching globalThis.fetch in case it is patched by other libraries
- 14cb3be: chore(providers/llamaindex): extract to separate package
- 1f6ce57: feat (ai): infer tool call types in the `onToolCall` callback
- 16ccfb2: feat (ai): add readUIMessageStream helper
- 225f087: fix (ai/mcp): prevent mutation of customEnv
- ce1d1f3: feat (ai): export mock image, speech, and transcription models
- fc0380b: feat (ui): resolvable header, body, credentials in http chat transport
- 6622441: feat (ai): add static/dynamic toolCalls/toolResults helpers
- 4048ce3: fix (ai): add tests and examples for openai responses
- 6c42e56: feat (ai): validate ui stream data chunks
- bedb239: chore (ai): make ui stream parts value optional when it's not required
- 9b4d074: feat(streamObject): add enum support
- c8fce91: feat (ai): add experimental Agent abstraction
- 655cf3c: feat (ui): add onFinish to createUIMessageStream
- 3e10408: fix(utils/detect-mimetype): add support for detecting id3 tags
- d5ae088: feat (ui): add sendAutomaticallyWhen to Chat
- ced8eee: feat(ai): re-export zodSchema from main package
- c040e2f: fix (ui): inject generated response message id
- d3960e3: selectTelemetryAttributes more robustness
- faea29f: fix (provider/openai): multi-step reasoning with text
- 66af894: fix (ai): respect content order in toResponseMessages
- 332167b: chore (ai): move maxSteps into UseChatOptions
- 6b1c55c: feat (ai): introduce GLOBAL_DEFAULT_PROVIDER
- 5a975a4: feat (ui): update Chat tool result submission
- 507ac1d: fix (ui/react): update messages immediately with the submitted user message
- a166433: feat: add transcription with experimental_transcribe
- 26735b5: chore(embedding-model): add v2 interface
- c93a8bc: chore(ai): export AsyncIterableStream type from async-iterable-stream module
- 0d2c085: feat (ai): support string model ids through gateway
- 2b9bbcd: feat (ai): improve prompt validation error message
- a8c8bd5: feat(embed-many): respect supportsParallelCalls & concurrency
- 75c3396: fix (ai): handle errors in 2nd streamText doStream call
- cb9c9e4: remove deprecated `experimental_wrapLanguageModel`
- 9bf7291: chore(providers/openai): enable structuredOutputs by default & switch to provider option
- 9b0da33: fix (ai): do not send id with start unless specified
- 28ad69e: fix(react-native): support experimental_attachments without FileList global
- 0b78e17: chore(ai/generateObject): simplify function signature
- 20398f2: feat: ai sdk cli documentation + adjusted default model
- 66962ed: fix(packages): export node10 compatible types
- b71fe8d: fix(ai): remove jsondiffpatch dependency
- 7827a49: fix (ai/core): refactor `toResponseMessages` to filter out empty string/content
- bd8a36c: feat(embedding-model-v2/embedMany): add response body field
- d9209ca: fix (image-model): `specificationVersion: v1` -> `v2`
- b346545: feat (ai): add data ui part schemas
- 05d2819: feat: allow zod 4.x as peer dependency
- f2b041e: Fix custom `fetch` in HttpChatTransport
- 2a62513: Fix error thrown when emptying messages in onError or onFinish
- 143c55b: feat (ai): export Chat callback types
- 9301f86: refactor (image-model): rename `ImageModelV1` to `ImageModelV2`
- 904fa5e: feat (ai/core): add terminateOnError option to readUIMessageStream
- 0a87932: core (ai): change transcription model mimeType to mediaType
- 1675396: fix: avoid job executor deadlock when adding tool result
- 51f497d: feat (ai): step input message modification in prepareStep
- cee64b2: fix(otel): change back toolCall attributes of input/output back to args/result for compatibility
- f04ffe4: feat (ui): add onData callback to Chat
- bc24722: feat (ai): Add finishReason as a promise on StreamObjectResult to match StreamTextResult
- b6f9f3c: remove deprecated `CoreTool*` types
- 8aa9e20: feat: add speech with experimental_generateSpeech
- 4617fab: chore(embedding-models): remove remaining settings
- 8255639: ### Fix use with Google APIs + zod v4's `.literal()` schema

  Before [zod@3.25.49](https://github.com/colinhacks/zod/releases/tag/v3.25.49), requests to Google's APIs failed due to a missing `type` in the provided schema. The problem has been resolved for the `ai` SDK by bumping our `zod` peer dependencies to `^3.25.49`.

  pull request: https://github.com/vercel/ai/pull/6609

- f81c720: chore(ai): bundle dependencies in CLI binary
- cf9af6e: feat (ai): allow sync prepareStep
- ee38081: Add support for audio/webm to detect-media-type
- 2e4f9e4: feat (ai): improved error messages when using gateway
- 3e3b9df: fix (ai/mcp): better support for zero-argument MCP tools
- cda32ba: fix (ai): send `start` part in correct position in stream (streamText)
- 48a7606: feat (ai): support changing the system prompt in prepareSteps
- cb68df0: feat: add transcription and speech model support to provider registry
- db64cbe: fix (provider/openai): multi-step reasoning with tool calls
- 97c35c0: feat (ui): transient data parts
- 26695a3: feat (ui): add state for text and reasoning ui message parts
- 90ac328: fix (ui): tool part metadata support in ui messages
- 60132dd: fixed date formatting for updated mcp protocol version
- 4a1e0c8: fix(ai-cli): fix bundling and improve authentication error handling
- c6b64a7: feat (ai): allow async prepareRequest on HttpChatTransport
- fccf75c: update mcp protocol version
- 9121250: Expose provider metadata as an attribute on exported OTEL spans
- ea27cc6: chore (ai): use JSONValue definition from provider
- 90ca2b9: feat(ai): Record tool call errors on tool call spans recorded in `generateText` and `streamText`.
- 50f0362: fix (ai): fix experimental sendStart/sendFinish options in streamText
- 825e8d7: release alpha.5
- 7d97ab6: release alpha.4
- 0ff02bb: chore(provider-utils): move over jsonSchema
- 4f3776c: feat (ai): add InferUITools helper
- 9338f3e: fix (ai): throw error for v1 models
- 92c8e66: fix(ai/core): properly handle custom separator in provider registry
- 53569b8: feat (ai): add experimental repairText function to streamObject
- 82aa95d: fix (ai): merge data ui stream parts correctly
- e7d2ce3: feat: provider-executed tools
- add5ac1: feat (ai): make streamText toUIMessageStream async iterable
- 37a916d: feat (ai): add prepareSteps to streamText
- 30ac566: fix (ui): text message metadata support in ui messages
- 8026705: fix (core): send buffered text in smooth stream when stream parts change
- 9bd5ab5: feat (provider): add providerMetadata to ImageModelV2 interface (#5977)

  The `experimental_generateImage` method from the `ai` package now returnes revised prompts for OpenAI's image models.

  ```js
  const prompt = 'Santa Claus driving a Cadillac';

  const { providerMetadata } = await experimental_generateImage({
    model: openai.image('dall-e-3'),
    prompt,
  });

  const revisedPrompt = providerMetadata.openai.images[0]?.revisedPrompt;

  console.log({
    prompt,
    revisedPrompt,
  });
  ```

- ec5933d: chore (ai/mcp): add `assertCapability` method to experimental MCP client
- 09f41ac: fix (ui): add message metadata in Chat.sendMessage
- ff1c81a: feat (ai): add streamText onAbort callback
- af1d5a5: fix(ai): Unexpected reasoning-start event in extract reasoning middleware
- cb3b9c9: fix (ai): catch errors in ui message stream
- 86293e5: fix (ai): use correct generateMessageId in streamText toUIMessageStream
- d1a034f: feature: using Zod 4 for internal stuff
- fd65bc6: chore(embedding-model-v2): rename rawResponse to response
- d92b9a8: fix(ai): add support for MCP protocol version 2025-06-18
- 102b066: fix (ai): fix invalid fetch call
- 142576e: feat (ui): support message replacement in chat via messageId param on sendMessage
- 84343eb: fix (ui): call sendAutomaticallyWhen with updated messages
- a76a62b: feat (ai): add experimental prepareStep callback to generateText
- 89ba235: fix (ui): support tool names with dash
- 8e31d46: feat (ai): export SourceDocumentUIPart
- bd398e4: fix (core): improve error handling in streamText's consumeStream method
- 88a8ee5: fix (ai): support abort during retry waits
- 205077b: fix: improve Zod compatibility
- d91b50d: chore(ui-utils): merge into ai package
- e4c8647: feat (ui): allow asynchronous onFinish in createUIMessageStream
- c808e4d: fix (ui): do not send changing assistant message ids when onFinish is provided
- e862b5b: feat (ai): allow sync tool.execute
- 395c85e: feat (ai): add consumeSseStream option to UI message stream responses
- 5bdff05: Removed deprecated `options.throwErrorForEmptyVectors` from `cosineSimilarity()`. Since `throwErrorForEmptyVectors` was the only option the entire `options` argument was removed.

  ```diff
  - cosineSimilarity(vector1, vector2, options)
  +cosineSimilarity(vector1, vector2)
  ```

- 13b4f46: feat (ai): export experimental MCPClient and MCPClientConfig interfaces
- a4f3007: chore: remove ai/react
- 8e64e9c: feat (ai): allow using provider default temperature by specifying null
- b983b51: feat (ai): support model message array in prompt
- 56c232b: fix (ai): remove outdated sendStart jsdoc
- 7324c21: fix (ai/telemetry): Avoid JSON.stringify on Uint8Arrays for telemetry
- f10304b: feat(tool-calling): don't require the user to have to pass parameters
- dd5fd43: feat (ai): support dynamic tools in Chat onToolCall
- a753b3a: feat (provider/anthropic): cache control for tools
- 383cbfa: feat (ai): add isAborted to onFinish callback for ui message streams
- 27deb4d: feat (provider/gateway): Add providerMetadata to embeddings response
- 5f2b3d4: chore (ai): stable prepareStep
- 4c8f834: feat: automatically respect rate limit headers in retry logic

  Added automatic support for respecting rate limit headers (`retry-after-ms` and `retry-after`) in the SDK's retry logic. When these headers are present and contain reasonable values (0-60 seconds), the retry mechanism will use the server-specified delay instead of exponential backoff. This matches the behavior of Anthropic and OpenAI client SDKs and improves rate limit handling without requiring any API changes.

- f2c7f19: feat (ui): add Chat.clearError()
- 7bd025b: fix (ai): fix sync tool execute with streamText
- Updated dependencies [a571d6e]
- Updated dependencies [742b7be]
- Updated dependencies [9e16bfd]
- Updated dependencies [e7fcc86]
- Updated dependencies [0477a13]
- Updated dependencies [7cddb72]
- Updated dependencies [ccce59b]
- Updated dependencies [e2b9e4b]
- Updated dependencies [95857aa]
- Updated dependencies [45c1ea2]
- Updated dependencies [6f6bb89]
- Updated dependencies [26b6dd0]
- Updated dependencies [060370c]
- Updated dependencies [dc714f3]
- Updated dependencies [b5da06a]
- Updated dependencies [d1a1aa1]
- Updated dependencies [63f9e9b]
- Updated dependencies [5d142ab]
- Updated dependencies [d5f588f]
- Updated dependencies [30ab1de]
- Updated dependencies [e025824]
- Updated dependencies [0571b98]
- Updated dependencies [b6b43c7]
- Updated dependencies [4fef487]
- Updated dependencies [48d257a]
- Updated dependencies [0c0c0b3]
- Updated dependencies [0d2c085]
- Updated dependencies [40acf9b]
- Updated dependencies [9222aeb]
- Updated dependencies [e2aceaf]
- Updated dependencies [411e483]
- Updated dependencies [97fedf9]
- Updated dependencies [8ba77a7]
- Updated dependencies [c91586a]
- Updated dependencies [7b3ae3f]
- Updated dependencies [a166433]
- Updated dependencies [3cbcbb7]
- Updated dependencies [26735b5]
- Updated dependencies [443d8ec]
- Updated dependencies [a8c8bd5]
- Updated dependencies [abf9a79]
- Updated dependencies [14c9410]
- Updated dependencies [e86be6f]
- Updated dependencies [9bf7291]
- Updated dependencies [2e13791]
- Updated dependencies [9f95b35]
- Updated dependencies [66962ed]
- Updated dependencies [fedb55e]
- Updated dependencies [0d06df6]
- Updated dependencies [472524a]
- Updated dependencies [dd3ff01]
- Updated dependencies [6c2c708]
- Updated dependencies [d9c98f4]
- Updated dependencies [05d2819]
- Updated dependencies [9301f86]
- Updated dependencies [0a87932]
- Updated dependencies [c4a2fec]
- Updated dependencies [957b739]
- Updated dependencies [721775e]
- Updated dependencies [70ebead]
- Updated dependencies [79457bd]
- Updated dependencies [a3f768e]
- Updated dependencies [f3639fa]
- Updated dependencies [7435eb5]
- Updated dependencies [8aa9e20]
- Updated dependencies [4617fab]
- Updated dependencies [ac34802]
- Updated dependencies [0054544]
- Updated dependencies [cb68df0]
- Updated dependencies [ad80501]
- Updated dependencies [8bd3624]
- Updated dependencies [68ecf2f]
- Updated dependencies [9e9c809]
- Updated dependencies [32831c6]
- Updated dependencies [6dc848c]
- Updated dependencies [6b98118]
- Updated dependencies [d0f9495]
- Updated dependencies [c145d62]
- Updated dependencies [63d791d]
- Updated dependencies [87b828f]
- Updated dependencies [3f2f00c]
- Updated dependencies [bfdca8d]
- Updated dependencies [0ff02bb]
- Updated dependencies [7979f7f]
- Updated dependencies [f77bc38]
- Updated dependencies [989ac75]
- Updated dependencies [39a4fab]
- Updated dependencies [44f4aba]
- Updated dependencies [7742ba3]
- Updated dependencies [9bd5ab5]
- Updated dependencies [57edfcb]
- Updated dependencies [faf8446]
- Updated dependencies [c190907]
- Updated dependencies [7ea4132]
- Updated dependencies [d1a034f]
- Updated dependencies [d454e4b]
- Updated dependencies [5c56081]
- Updated dependencies [fd65bc6]
- Updated dependencies [cf1e00e]
- Updated dependencies [023ba40]
- Updated dependencies [cc21603]
- Updated dependencies [ea7a7c9]
- Updated dependencies [26535e0]
- Updated dependencies [e030615]
- Updated dependencies [5e57fae]
- Updated dependencies [393138b]
- Updated dependencies [c57e248]
- Updated dependencies [88a8ee5]
- Updated dependencies [41fa418]
- Updated dependencies [205077b]
- Updated dependencies [71f938d]
- Updated dependencies [e001ea1]
- Updated dependencies [3795467]
- Updated dependencies [28a5ed5]
- Updated dependencies [7182d14]
- Updated dependencies [c1e6647]
- Updated dependencies [1766ede]
- Updated dependencies [811dff3]
- Updated dependencies [f10304b]
- Updated dependencies [dd5fd43]
- Updated dependencies [33f4a6a]
- Updated dependencies [383cbfa]
- Updated dependencies [27deb4d]
- Updated dependencies [c4df419]
  - @ai-sdk/provider-utils@3.0.0
  - @ai-sdk/provider@2.0.0
  - @ai-sdk/gateway@1.0.0

## 5.0.0-beta.34

### Patch Changes

- 53569b8: feat (ai): add experimental repairText function to streamObject
- 88a8ee5: fix (ai): support abort during retry waits
- f2c7f19: feat (ui): add Chat.clearError()
- Updated dependencies [721775e]
- Updated dependencies [88a8ee5]
  - @ai-sdk/gateway@1.0.0-beta.19
  - @ai-sdk/provider-utils@3.0.0-beta.10

## 5.0.0-beta.33

### Patch Changes

- 48378b9: fix (ai): send null as tool output when tools return undefined
- 93d53a1: chore (ai): remove cli
- 27deb4d: feat (provider/gateway): Add providerMetadata to embeddings response
- Updated dependencies [27deb4d]
  - @ai-sdk/gateway@1.0.0-beta.18
  - @ai-sdk/provider@2.0.0-beta.2
  - @ai-sdk/provider-utils@3.0.0-beta.9

## 5.0.0-beta.32

### Patch Changes

- bc24722: feat (ai): Add finishReason as a promise on StreamObjectResult to match StreamTextResult
- 13b4f46: feat (ai): export experimental MCPClient and MCPClientConfig interfaces
- 56c232b: fix (ai): remove outdated sendStart jsdoc

## 5.0.0-beta.31

### Patch Changes

- 6622441: feat (ai): add static/dynamic toolCalls/toolResults helpers
- ced8eee: feat(ai): re-export zodSchema from main package
- cee64b2: fix(otel): change back toolCall attributes of input/output back to args/result for compatibility
- ee38081: Add support for audio/webm to detect-media-type
- dd5fd43: feat (ai): support dynamic tools in Chat onToolCall
- Updated dependencies [dd5fd43]
  - @ai-sdk/provider-utils@3.0.0-beta.8
  - @ai-sdk/gateway@1.0.0-beta.17

## 5.0.0-beta.30

### Patch Changes

- Updated dependencies [fedb55e]
  - @ai-sdk/gateway@1.0.0-beta.16

## 5.0.0-beta.29

### Patch Changes

- e7fcc86: feat (ai): introduce dynamic tools
- d92b9a8: fix(ai): add support for MCP protocol version 2025-06-18
- Updated dependencies [e7fcc86]
  - @ai-sdk/provider-utils@3.0.0-beta.7
  - @ai-sdk/gateway@1.0.0-beta.15

## 5.0.0-beta.28

### Patch Changes

- 84343eb: fix (ui): call sendAutomaticallyWhen with updated messages
- a753b3a: feat (provider/anthropic): cache control for tools
- Updated dependencies [ac34802]
  - @ai-sdk/provider-utils@3.0.0-beta.6
  - @ai-sdk/gateway@1.0.0-beta.14

## 5.0.0-beta.27

### Patch Changes

- d5ae088: feat (ui): add sendAutomaticallyWhen to Chat
- Updated dependencies [0477a13]
- Updated dependencies [cf1e00e]
- Updated dependencies [cc21603]
  - @ai-sdk/gateway@1.0.0-beta.13

## 5.0.0-beta.26

### Patch Changes

- ae77a99: chore (ai): rename text and reasoning chunks in streamText fullstream
- 1f6ce57: feat (ai): infer tool call types in the `onToolCall` callback
- 5a975a4: feat (ui): update Chat tool result submission
- 2a62513: Fix error thrown when emptying messages in onError or onFinish
- 904fa5e: feat (ai/core): add terminateOnError option to readUIMessageStream
- f81c720: chore(ai): bundle dependencies in CLI binary
- Updated dependencies [70ebead]
  - @ai-sdk/gateway@1.0.0-beta.12

## 5.0.0-beta.25

### Patch Changes

- Updated dependencies [8bd3624]
- Updated dependencies [e001ea1]
  - @ai-sdk/gateway@1.0.0-beta.11

## 5.0.0-beta.24

### Patch Changes

- add5ac1: feat (ai): make streamText toUIMessageStream async iterable
- ff1c81a: feat (ai): add streamText onAbort callback
- e4c8647: feat (ui): allow asynchronous onFinish in createUIMessageStream
- 383cbfa: feat (ai): add isAborted to onFinish callback for ui message streams
- Updated dependencies [57edfcb]
- Updated dependencies [383cbfa]
  - @ai-sdk/provider-utils@3.0.0-beta.5
  - @ai-sdk/gateway@1.0.0-beta.10

## 5.0.0-beta.23

### Patch Changes

- 89ba235: fix (ui): support tool names with dash

## 5.0.0-beta.22

### Patch Changes

- de2d2ab: feat(ai): add provider and provider registry middleware functionality
- 6c42e56: feat (ai): validate ui stream data chunks
- c93a8bc: chore(ai): export AsyncIterableStream type from async-iterable-stream module
- 20398f2: feat: ai sdk cli documentation + adjusted default model
- 86293e5: fix (ai): use correct generateMessageId in streamText toUIMessageStream
- 205077b: fix: improve Zod compatibility
- Updated dependencies [205077b]
  - @ai-sdk/provider-utils@3.0.0-beta.4
  - @ai-sdk/gateway@1.0.0-beta.9

## 5.0.0-beta.21

### Patch Changes

- 38ae5cc: feat (ai): export InferUIMessageChunk type
- faea29f: fix (provider/openai): multi-step reasoning with text
- 90ac328: fix (ui): tool part metadata support in ui messages
- 4a1e0c8: fix(ai-cli): fix bundling and improve authentication error handling
- 30ac566: fix (ui): text message metadata support in ui messages

## 5.0.0-beta.20

### Patch Changes

- 4c8f834: feat: automatically respect rate limit headers in retry logic

  Added automatic support for respecting rate limit headers (`retry-after-ms` and `retry-after`) in the SDK's retry logic. When these headers are present and contain reasonable values (0-60 seconds), the retry mechanism will use the server-specified delay instead of exponential backoff. This matches the behavior of Anthropic and OpenAI client SDKs and improves rate limit handling without requiring any API changes.

## 5.0.0-beta.19

### Patch Changes

- 10b21eb: feat(cli): add ai command line interface
- 75c3396: fix (ai): handle errors in 2nd streamText doStream call
- 05d2819: feat: allow zod 4.x as peer dependency
- db64cbe: fix (provider/openai): multi-step reasoning with tool calls
- Updated dependencies [05d2819]
  - @ai-sdk/provider-utils@3.0.0-beta.3
  - @ai-sdk/gateway@1.0.0-beta.8

## 5.0.0-beta.18

### Patch Changes

- d3960e3: selectTelemetryAttributes more robustness
- 9338f3e: fix (ai): throw error for v1 models

## 5.0.0-beta.17

### Patch Changes

- Updated dependencies [c190907]
  - @ai-sdk/gateway@1.0.0-beta.7

## 5.0.0-beta.16

### Patch Changes

- Updated dependencies [9e16bfd]
  - @ai-sdk/gateway@1.0.0-beta.6

## 5.0.0-beta.15

### Patch Changes

- 8e31d46: feat (ai): export SourceDocumentUIPart

## 5.0.0-beta.14

### Patch Changes

- Updated dependencies [30ab1de]
  - @ai-sdk/gateway@1.0.0-beta.5

## 5.0.0-beta.13

### Patch Changes

- 377bbcf: fix (ui): tool input can be undefined during input-streaming
- ce1d1f3: feat (ai): export mock image, speech, and transcription models
- c040e2f: fix (ui): inject generated response message id
- c808e4d: fix (ui): do not send changing assistant message ids when onFinish is provided

## 5.0.0-beta.12

### Patch Changes

- fc0380b: feat (ui): resolvable header, body, credentials in http chat transport
- 51f497d: feat (ai): step input message modification in prepareStep
- 4f3776c: feat (ai): add InferUITools helper

## 5.0.0-beta.11

### Patch Changes

- 9e40cbe: Allow destructuring output and errorText on `ToolUIPart` type

## 5.0.0-beta.10

### Major Changes

- 2b637d6: chore (ai): rename UIMessageStreamPart to UIMessageChunk

### Patch Changes

- 16ccfb2: feat (ai): add readUIMessageStream helper
- 90ca2b9: feat(ai): Record tool call errors on tool call spans recorded in `generateText` and `streamText`.
- af1d5a5: fix(ai): Unexpected reasoning-start event in extract reasoning middleware

## 5.0.0-beta.9

### Patch Changes

- 86cfc72: feat (ai): add ignoreIncompleteToolCalls option to convertToModelMessages

## 5.0.0-beta.8

### Patch Changes

- 6909543: feat (ai): support system parameter in Agent constructor
- c8fce91: feat (ai): add experimental Agent abstraction
- 9121250: Expose provider metadata as an attribute on exported OTEL spans
- Updated dependencies [97fedf9]
  - @ai-sdk/gateway@1.0.0-beta.4

## 5.0.0-beta.7

### Patch Changes

- 60132dd: fixed date formatting for updated mcp protocol version

## 5.0.0-beta.6

### Patch Changes

- 143c55b: feat (ai): export Chat callback types
- f04ffe4: feat (ui): add onData callback to Chat
- 97c35c0: feat (ui): transient data parts
- fccf75c: update mcp protocol version

## 5.0.0-beta.5

### Patch Changes

- 4f3e637: fix (ui): avoid caching globalThis.fetch in case it is patched by other libraries

## 5.0.0-beta.4

### Patch Changes

- 09f41ac: fix (ui): add message metadata in Chat.sendMessage

## 5.0.0-beta.3

### Patch Changes

- Updated dependencies [f3639fa]
- Updated dependencies [d454e4b]
  - @ai-sdk/gateway@1.0.0-beta.3

## 5.0.0-beta.2

### Patch Changes

- 0d9583c: fix (ai): use user-provided media type when available
- c6b64a7: feat (ai): allow async prepareRequest on HttpChatTransport
- cb3b9c9: fix (ai): catch errors in ui message stream
- d1a034f: feature: using Zod 4 for internal stuff
- Updated dependencies [0571b98]
- Updated dependencies [c91586a]
- Updated dependencies [39a4fab]
- Updated dependencies [d1a034f]
  - @ai-sdk/provider-utils@3.0.0-beta.2
  - @ai-sdk/gateway@1.0.0-beta.2

## 5.0.0-beta.1

### Major Changes

- 9ad0484: feat (ai): automatic tool execution error handling

### Patch Changes

- d88455d: feat (ai): expose http chat transport type
- 4048ce3: fix (ai): add tests and examples for openai responses
- f2b041e: Fix custom `fetch` in HttpChatTransport
- cb68df0: feat: add transcription and speech model support to provider registry
- 26695a3: feat (ui): add state for text and reasoning ui message parts
- e7d2ce3: feat: provider-executed tools
- 102b066: fix (ai): fix invalid fetch call
- e862b5b: feat (ai): allow sync tool.execute
- 7bd025b: fix (ai): fix sync tool execute with streamText
- Updated dependencies [742b7be]
- Updated dependencies [7cddb72]
- Updated dependencies [ccce59b]
- Updated dependencies [e2b9e4b]
- Updated dependencies [45c1ea2]
- Updated dependencies [e025824]
- Updated dependencies [0d06df6]
- Updated dependencies [472524a]
- Updated dependencies [dd3ff01]
- Updated dependencies [7435eb5]
- Updated dependencies [cb68df0]
- Updated dependencies [bfdca8d]
- Updated dependencies [f77bc38]
- Updated dependencies [44f4aba]
- Updated dependencies [023ba40]
- Updated dependencies [5e57fae]
- Updated dependencies [71f938d]
- Updated dependencies [28a5ed5]
  - @ai-sdk/provider@2.0.0-beta.1
  - @ai-sdk/provider-utils@3.0.0-beta.1
  - @ai-sdk/gateway@1.0.0-beta.1

## 5.0.0-alpha.15

### Major Changes

- 04d5063: chore (ai): rename default provider global to AI_SDK_DEFAULT_PROVIDER
- b4b4bb2: chore (ui): rename experimental_resume to resumeStream
- d884051: feat (ai): simplify default provider setup
- 954aa73: feat (ui): extended regenerate support
- 60e2c56: feat (ai): restructure chat transports

### Patch Changes

- b1e3abd: feat (ai): expose ui message stream headers
- 142576e: feat (ui): support message replacement in chat via messageId param on sendMessage
- 395c85e: feat (ai): add consumeSseStream option to UI message stream responses
- Updated dependencies [48d257a]
- Updated dependencies [8ba77a7]
- Updated dependencies [c145d62]
  - @ai-sdk/provider@2.0.0-alpha.15
  - @ai-sdk/provider-utils@3.0.0-alpha.15
  - @ai-sdk/gateway@1.0.0-alpha.15

## 5.0.0-alpha.14

### Major Changes

- 63f9e9b: chore (provider,ai): tools have input/output instead of args,result

### Patch Changes

- Updated dependencies [b5da06a]
- Updated dependencies [63f9e9b]
- Updated dependencies [2e13791]
  - @ai-sdk/provider@2.0.0-alpha.14
  - @ai-sdk/gateway@1.0.0-alpha.14
  - @ai-sdk/provider-utils@3.0.0-alpha.14

## 5.0.0-alpha.13

### Major Changes

- 0a710d8: feat (ui): typed tool parts in ui messages
- 6a83f7d: refactoring (ai): restructure message metadata transfer
- 1f55c21: chore (ai): send reasoning to the client by default
- 33eb499: feat (ai): inject message id in createUIMessageStream
- 901df02: feat (ui): use UI_MESSAGE generic

### Patch Changes

- Updated dependencies [68ecf2f]
  - @ai-sdk/provider@2.0.0-alpha.13
  - @ai-sdk/gateway@1.0.0-alpha.13
  - @ai-sdk/provider-utils@3.0.0-alpha.13

## 5.0.0-alpha.12

### Major Changes

- 4892798: chore (ai): always stream tool calls

### Patch Changes

- da1e6f0: feat (ui): add generics to ui message stream parts
- Updated dependencies [e2aceaf]
  - @ai-sdk/gateway@1.0.0-alpha.12
  - @ai-sdk/provider@2.0.0-alpha.12
  - @ai-sdk/provider-utils@3.0.0-alpha.12

## 5.0.0-alpha.11

### Major Changes

- e8324c5: feat (ai): add args callbacks to tools

### Patch Changes

- Updated dependencies [c1e6647]
  - @ai-sdk/provider@2.0.0-alpha.11
  - @ai-sdk/gateway@1.0.0-alpha.11
  - @ai-sdk/provider-utils@3.0.0-alpha.11

## 5.0.0-alpha.10

### Major Changes

- 98f25e5: chore (ui): remove managed chat inputs
- 7bb58d4: chore (ai): restructure prepareRequest

### Patch Changes

- Updated dependencies [c4df419]
  - @ai-sdk/provider@2.0.0-alpha.10
  - @ai-sdk/gateway@1.0.0-alpha.10
  - @ai-sdk/provider-utils@3.0.0-alpha.10

## 5.0.0-alpha.9

### Major Changes

- 9ae327d: chore (ui): replace chat store concept with chat instances

### Patch Changes

- 8255639: ### Fix use with Google APIs + zod v4's `.literal()` schema

  Before [zod@3.25.49](https://github.com/colinhacks/zod/releases/tag/v3.25.49), requests to Google's APIs failed due to a missing `type` in the provided schema. The problem has been resolved for the `ai` SDK by bumping our `zod` peer dependencies to `^3.25.49`.

  pull request: https://github.com/vercel/ai/pull/6609

- Updated dependencies [26b6dd0]
- Updated dependencies [811dff3]
  - @ai-sdk/gateway@1.0.0-alpha.9
  - @ai-sdk/provider@2.0.0-alpha.9
  - @ai-sdk/provider-utils@3.0.0-alpha.9

## 5.0.0-alpha.8

### Major Changes

- c25cbce: feat (ai): use console.error as default error handler for streamText and streamObject

### Patch Changes

- 4fef487: feat: support for zod v4 for schema validation

  All these methods now accept both a zod v4 and zod v3 schemas for validation:

  - `generateObject()`
  - `streamObject()`
  - `generateText()`
  - `experimental_useObject()` from `@ai-sdk/react`
  - `streamUI()` from `@ai-sdk/rsc`

- 6b1c55c: feat (ai): introduce GLOBAL_DEFAULT_PROVIDER
- 2e4f9e4: feat (ai): improved error messages when using gateway
- Updated dependencies [4fef487]
- Updated dependencies [9222aeb]
- Updated dependencies [3cbcbb7]
- Updated dependencies [989ac75]
- Updated dependencies [7742ba3]
  - @ai-sdk/provider-utils@3.0.0-alpha.8
  - @ai-sdk/provider@2.0.0-alpha.8
  - @ai-sdk/gateway@1.0.0-alpha.8

## 5.0.0-alpha.7

### Major Changes

- db345da: chore (ai): remove exports of internal ui functions
- 247ee0c: chore (ai): remove steps from tool invocation ui parts

### Patch Changes

- 9b0da33: fix (ai): do not send id with start unless specified
- Updated dependencies [5c56081]
  - @ai-sdk/provider@2.0.0-alpha.7
  - @ai-sdk/gateway@1.0.0-alpha.7
  - @ai-sdk/provider-utils@3.0.0-alpha.7

## 5.0.0-alpha.6

### Patch Changes

- 0d2c085: feat (ai): support string model ids through gateway
- 48a7606: feat (ai): support changing the system prompt in prepareSteps
- Updated dependencies [0d2c085]
- Updated dependencies [6c2c708]
  - @ai-sdk/provider@2.0.0-alpha.6
  - @ai-sdk/gateway@1.0.0-alpha.6
  - @ai-sdk/provider-utils@3.0.0-alpha.6

## 5.0.0-alpha.5

### Major Changes

- ef256ed: chore (ai): refactor and use chatstore in svelte
- 1ed0287: chore (ai): stable sendStart/sendFinish options

### Patch Changes

- 655cf3c: feat (ui): add onFinish to createUIMessageStream
- 1675396: fix: avoid job executor deadlock when adding tool result
- cf9af6e: feat (ai): allow sync prepareStep
- 825e8d7: release alpha.5
- 7324c21: fix (ai/telemetry): Avoid JSON.stringify on Uint8Arrays for telemetry

## 5.0.0-alpha.4

### Major Changes

- 72d7d72: chore (ai): stable activeTools
- 9315076: chore (ai): rename continueUntil to stopWhen. Rename maxSteps stop condition to stepCountIs.

### Patch Changes

- b32c141: feat (ai): add array support to stopWhen
- 7d97ab6: release alpha.4
- 37a916d: feat (ai): add prepareSteps to streamText
- 5f2b3d4: chore (ai): stable prepareStep
- Updated dependencies [dc714f3]
  - @ai-sdk/provider@2.0.0-alpha.4
  - @ai-sdk/provider-utils@3.0.0-alpha.4

## 5.0.0-alpha.3

### Major Changes

- ab7ccef: chore (ai): change source ui message parts to source-url
- 257224b: chore (ai): separate TextStreamChatTransport
- 0463011: fix (ai): update source url stream part
- d306260: feat (ai): replace maxSteps with continueUntil (streamText)

### Patch Changes

- Updated dependencies [6b98118]
  - @ai-sdk/provider@2.0.0-alpha.3
  - @ai-sdk/provider-utils@3.0.0-alpha.3

## 5.0.0-alpha.2

### Patch Changes

- 82aa95d: fix (ai): merge data ui stream parts correctly
- Updated dependencies [26535e0]
  - @ai-sdk/provider@2.0.0-alpha.2
  - @ai-sdk/provider-utils@3.0.0-alpha.2

## 5.0.0-alpha.1

### Major Changes

- 109c0ac: chore (ai): rename id to chatId (in post request, resume request, and useChat)

### Patch Changes

- b346545: feat (ai): add data ui part schemas
- Updated dependencies [3f2f00c]
  - @ai-sdk/provider@2.0.0-alpha.1
  - @ai-sdk/provider-utils@3.0.0-alpha.1

## 5.0.0-canary.24

### Major Changes

- f7e8bf4: chore (ai): flatten ui message stream parts
- ed675de: feat (ai): add ui data parts
- 64f6d64: feat (ai): replace maxSteps with continueUntil (generateText)

### Patch Changes

- bedb239: chore (ai): make ui stream parts value optional when it's not required
- 507ac1d: fix (ui/react): update messages immediately with the submitted user message
- 2b9bbcd: feat (ai): improve prompt validation error message
- cda32ba: fix (ai): send `start` part in correct position in stream (streamText)
- 50f0362: fix (ai): fix experimental sendStart/sendFinish options in streamText
- Updated dependencies [faf8446]
  - @ai-sdk/provider-utils@3.0.0-canary.19

## 5.0.0-canary.23

### Major Changes

- 40acf9b: feat (ui): introduce ChatStore and ChatTransport

### Patch Changes

- Updated dependencies [40acf9b]
  - @ai-sdk/provider-utils@3.0.0-canary.18

## 5.0.0-canary.22

### Major Changes

- e7dc6c7: chore (ai): remove onResponse callback
- a34eb39: chore (ai): remove `data` and `allowEmptySubmit` from `ChatRequestOptions`
- b33ed7a: chore (ai): rename DataStream* to UIMessage*
- 765f1cd: chore (ai): remove deprecated useChat isLoading helper

## 5.0.0-canary.21

### Major Changes

- d964901: - remove setting temperature to `0` by default
  - remove `null` option from `DefaultSettingsMiddleware`
  - remove setting defaults for `temperature` and `stopSequences` in `ai` to enable middleware changes
- 0560977: chore (ai): improve consistency of generate text result, stream text result, and step result
- 516be5b: ### Move Image Model Settings into generate options

  Image Models no longer have settings. Instead, `maxImagesPerCall` can be passed directly to `generateImage()`. All other image settings can be passed to `providerOptions[provider]`.

  Before

  ```js
  await generateImage({
    model: luma.image('photon-flash-1', {
      maxImagesPerCall: 5,
      pollIntervalMillis: 500,
    }),
    prompt,
    n: 10,
  });
  ```

  After

  ```js
  await generateImage({
    model: luma.image('photon-flash-1'),
    prompt,
    n: 10,
    maxImagesPerCall: 5,
    providerOptions: {
      luma: { pollIntervalMillis: 5 },
    },
  });
  ```

  Pull Request: https://github.com/vercel/ai/pull/6180

- bfbfc4c: feat (ai): streamText/generateText: totalUsage contains usage for all steps. usage is for a single step.
- ea7a7c9: feat (ui): UI message metadata
- 1409e13: chore (ai): remove experimental continueSteps

### Patch Changes

- 66af894: fix (ai): respect content order in toResponseMessages
- Updated dependencies [ea7a7c9]
  - @ai-sdk/provider-utils@3.0.0-canary.17

## 5.0.0-canary.20

### Major Changes

- 13fef90: chore (ai): remove automatic conversion of UI messages to model messages
- 496bbc1: chore (ui): inline/remove ChatRequest type
- da70d79: chore (ai): remove getUIText helper
- c7710a9: chore (ai): rename DataStreamToSSETransformStream to JsonToSseTransformStream
- 35fc02c: chore (ui): rename RequestOptions to CompletionRequestOptions
- b983b51: feat (ai): support model message array in prompt

### Minor Changes

- bcea599: feat (ai): add content to generateText result
- 48d675a: feat (ai): add content to streamText result

### Patch Changes

- e90d45d: chore (rsc): move HANGING_STREAM_WARNING_TIME constant into @ai-sdk/rsc package
- bc3109f: chore (ai): push stream-callbacks into langchain/llamaindex adapters
- Updated dependencies [87b828f]
  - @ai-sdk/provider-utils@3.0.0-canary.16

## 5.0.0-canary.19

### Major Changes

- 2d03e19: chore (ai): remove StreamCallbacks.onCompletion
- 319b989: chore (ai): remove content from ui messages
- 441d042: chore (ui): data stream protocol v2 with SSEs
- dcc549b: remove StreamTextResult.mergeIntoDataStream method
  rename DataStreamOptions.getErrorMessage to onError
  add pipeTextStreamToResponse function
  add createTextStreamResponse function
  change createDataStreamResponse function to accept a DataStream and not a DataStreamWriter
  change pipeDataStreamToResponse function to accept a DataStream and not a DataStreamWriter
  change pipeDataStreamToResponse function to have a single parameter
- cb2b53a: chore (ai): refactor header preparation
- e244a78: chore (ai): remove StreamData and mergeStreams

## 5.0.0-canary.18

### Major Changes

- c60f895: chore (ai): remove useChat keepLastMessageOnError
- a662dea: chore (ai): remove sendExtraMessageFields

### Patch Changes

- a571d6e: chore(provider-utils): move ToolResultContent to provider-utils
- 332167b: chore (ai): move maxSteps into UseChatOptions
- a8c8bd5: feat(embed-many): respect supportsParallelCalls & concurrency
- Updated dependencies [a571d6e]
- Updated dependencies [a8c8bd5]
- Updated dependencies [7979f7f]
- Updated dependencies [41fa418]
  - @ai-sdk/provider-utils@3.0.0-canary.15
  - @ai-sdk/provider@2.0.0-canary.14

## 5.0.0-canary.17

### Major Changes

- f04fb4a: chore (ai): replace useChat attachments with file ui parts
- fd1924b: chore (ai): remove redundant `mimeType` property
- fafc3f2: chore (ai): change file to parts to use urls instead of data
- 92cb0a2: chore (ai): rename CoreMessage to ModelMessage

### Minor Changes

- c9ad635: feat (ai): add filename to file ui parts

### Patch Changes

- 9bd5ab5: feat (provider): add providerMetadata to ImageModelV2 interface (#5977)

  The `experimental_generateImage` method from the `ai` package now returnes revised prompts for OpenAI's image models.

  ```js
  const prompt = 'Santa Claus driving a Cadillac';

  const { providerMetadata } = await experimental_generateImage({
    model: openai.image('dall-e-3'),
    prompt,
  });

  const revisedPrompt = providerMetadata.openai.images[0]?.revisedPrompt;

  console.log({
    prompt,
    revisedPrompt,
  });
  ```

- Updated dependencies [957b739]
- Updated dependencies [9bd5ab5]
  - @ai-sdk/provider-utils@3.0.0-canary.14
  - @ai-sdk/provider@2.0.0-canary.13

## 5.0.0-canary.16

### Major Changes

- ec78cdc: chore (ai): remove "data" UIMessage role
- 8b86e99: chore (ai): replace `Message` with `UIMessage`
- 2524fc7: chore (ai): remove ui message toolInvocations property
- 175b868: chore (ai): rename reasoning UI parts 'reasoning' property to 'text'

### Patch Changes

- 9b4d074: feat(streamObject): add enum support
- 28ad69e: fix(react-native): support experimental_attachments without FileList global
- ec5933d: chore (ai/mcp): add `assertCapability` method to experimental MCP client

## 5.0.0-canary.15

### Major Changes

- 4bfe9ec: chore (ai): remove ui message reasoning property
- 2877a74: chore (ai): remove ui message data property

### Patch Changes

- d9209ca: fix (image-model): `specificationVersion: v1` -> `v2`
- ea27cc6: chore (ai): use JSONValue definition from provider
- 0ff02bb: chore(provider-utils): move over jsonSchema
- Updated dependencies [7b3ae3f]
- Updated dependencies [0ff02bb]
  - @ai-sdk/provider@2.0.0-canary.12
  - @ai-sdk/provider-utils@3.0.0-canary.13

## 5.0.0-canary.14

### Patch Changes

- 9bf7291: chore(providers/openai): enable structuredOutputs by default & switch to provider option
- 4617fab: chore(embedding-models): remove remaining settings
- a76a62b: feat (ai): add experimental prepareStep callback to generateText
- Updated dependencies [9bf7291]
- Updated dependencies [4617fab]
- Updated dependencies [e030615]
  - @ai-sdk/provider@2.0.0-canary.11
  - @ai-sdk/provider-utils@3.0.0-canary.12

## 5.0.0-canary.13

### Patch Changes

- 14cb3be: chore(providers/llamaindex): extract to separate package
- 66962ed: fix(packages): export node10 compatible types
- 9301f86: refactor (image-model): rename `ImageModelV1` to `ImageModelV2`
- Updated dependencies [66962ed]
- Updated dependencies [9301f86]
- Updated dependencies [a3f768e]
  - @ai-sdk/provider-utils@3.0.0-canary.11
  - @ai-sdk/provider@2.0.0-canary.10

## 5.0.0-canary.12

### Patch Changes

- Updated dependencies [e86be6f]
  - @ai-sdk/provider@2.0.0-canary.9
  - @ai-sdk/provider-utils@3.0.0-canary.10

## 5.0.0-canary.11

### Patch Changes

- 8e64e9c: feat (ai): allow using provider default temperature by specifying null
- Updated dependencies [95857aa]
- Updated dependencies [7ea4132]
  - @ai-sdk/provider@2.0.0-canary.8
  - @ai-sdk/provider-utils@3.0.0-canary.9

## 5.0.0-canary.10

### Patch Changes

- d8aeaef: feat(providers/fal): add transcribe
- 3e10408: fix(utils/detect-mimetype): add support for detecting id3 tags

## 5.0.0-canary.9

### Major Changes

- a847c3e: chore: rename reasoning to reasoningText etc
- b32e192: chore (ai): rename reasoning to reasoningText, rename reasoningDetails to reasoning (streamText, generateText)

### Patch Changes

- cb9c9e4: remove deprecated `experimental_wrapLanguageModel`
- 8aa9e20: feat: add speech with experimental_generateSpeech
- Updated dependencies [5d142ab]
- Updated dependencies [b6b43c7]
- Updated dependencies [8aa9e20]
- Updated dependencies [3795467]
  - @ai-sdk/provider-utils@3.0.0-canary.8
  - @ai-sdk/provider@2.0.0-canary.7

## 5.0.0-canary.8

### Major Changes

- 14c9410: chore: refactor file towards source pattern (spec)

### Patch Changes

- 5d1e3ba: chore (ai): remove provider re-exports
- 26735b5: chore(embedding-model): add v2 interface
- 7827a49: fix (ai/core): refactor `toResponseMessages` to filter out empty string/content
- bd8a36c: feat(embedding-model-v2/embedMany): add response body field
- b6f9f3c: remove deprecated `CoreTool*` types
- 92c8e66: fix(ai/core): properly handle custom separator in provider registry
- fd65bc6: chore(embedding-model-v2): rename rawResponse to response
- 5bdff05: Removed deprecated `options.throwErrorForEmptyVectors` from `cosineSimilarity()`. Since `throwErrorForEmptyVectors` was the only option the entire `options` argument was removed.

  ```diff
  - cosineSimilarity(vector1, vector2, options)
  +cosineSimilarity(vector1, vector2)
  ```

- Updated dependencies [26735b5]
- Updated dependencies [443d8ec]
- Updated dependencies [14c9410]
- Updated dependencies [d9c98f4]
- Updated dependencies [c4a2fec]
- Updated dependencies [0054544]
- Updated dependencies [9e9c809]
- Updated dependencies [32831c6]
- Updated dependencies [d0f9495]
- Updated dependencies [fd65bc6]
- Updated dependencies [393138b]
- Updated dependencies [7182d14]
  - @ai-sdk/provider@2.0.0-canary.6
  - @ai-sdk/provider-utils@3.0.0-canary.7

## 5.0.0-canary.7

### Major Changes

- 6fba4c7: chore (ai): remove deprecated experimental_providerMetadata
- 1766ede: chore: rename maxTokens to maxOutputTokens

### Patch Changes

- 0b78e17: chore(ai/generateObject): simplify function signature
- 3e3b9df: fix (ai/mcp): better support for zero-argument MCP tools
- f10304b: feat(tool-calling): don't require the user to have to pass parameters
- Updated dependencies [411e483]
- Updated dependencies [79457bd]
- Updated dependencies [ad80501]
- Updated dependencies [1766ede]
- Updated dependencies [f10304b]
  - @ai-sdk/provider@2.0.0-canary.5
  - @ai-sdk/provider-utils@3.0.0-canary.6

## 5.0.0-canary.6

### Patch Changes

- Updated dependencies [6f6bb89]
  - @ai-sdk/provider@2.0.0-canary.4
  - @ai-sdk/provider-utils@3.0.0-canary.5

## 5.0.0-canary.5

### Patch Changes

- b71fe8d: fix(ai): remove jsondiffpatch dependency
- d91b50d: chore(ui-utils): merge into ai package
- Updated dependencies [d1a1aa1]
  - @ai-sdk/provider@2.0.0-canary.3
  - @ai-sdk/provider-utils@3.0.0-canary.4

## 5.0.0-canary.4

### Major Changes

- e1cbf8a: chore(@ai-sdk/rsc): extract to separate package

### Patch Changes

- 225f087: fix (ai/mcp): prevent mutation of customEnv
- a166433: feat: add transcription with experimental_transcribe
- 0a87932: core (ai): change transcription model mimeType to mediaType
- Updated dependencies [a166433]
- Updated dependencies [abf9a79]
- Updated dependencies [9f95b35]
- Updated dependencies [0a87932]
- Updated dependencies [6dc848c]
  - @ai-sdk/provider-utils@3.0.0-canary.3
  - @ai-sdk/provider@2.0.0-canary.2
  - @ai-sdk/ui-utils@2.0.0-canary.3

## 5.0.0-canary.3

### Patch Changes

- Updated dependencies [c57e248]
- Updated dependencies [33f4a6a]
  - @ai-sdk/provider@2.0.0-canary.1
  - @ai-sdk/provider-utils@3.0.0-canary.2
  - @ai-sdk/ui-utils@2.0.0-canary.2

## 5.0.0-canary.2

### Patch Changes

- bd398e4: fix (core): improve error handling in streamText's consumeStream method

## 5.0.0-canary.1

### Minor Changes

- b7eae2d: feat (core): Add finishReason field to NoObjectGeneratedError

### Patch Changes

- c22ad54: feat(smooth-stream): chunking callbacks
- a4f3007: chore: remove ai/react
- Updated dependencies [060370c]
- Updated dependencies [0c0c0b3]
- Updated dependencies [63d791d]
  - @ai-sdk/provider-utils@3.0.0-canary.1
  - @ai-sdk/ui-utils@2.0.0-canary.1

## 5.0.0-canary.0

### Major Changes

- d5f588f: AI SDK 5
- 9477ebb: chore (ui): remove useAssistant hook (**breaking change**)

### Patch Changes

- 8026705: fix (core): send buffered text in smooth stream when stream parts change
- Updated dependencies [d5f588f]
- Updated dependencies [9477ebb]
  - @ai-sdk/provider-utils@3.0.0-canary.0
  - @ai-sdk/ui-utils@2.0.0-canary.0
  - @ai-sdk/react@2.0.0-canary.0
  - @ai-sdk/provider@2.0.0-canary.0

## 4.2.10

### Patch Changes

- Updated dependencies [a043b14]
- Updated dependencies [28be004]
  - @ai-sdk/react@1.2.5
  - @ai-sdk/provider-utils@2.2.3
  - @ai-sdk/ui-utils@1.2.4

## 4.2.9

### Patch Changes

- Updated dependencies [b01120e]
  - @ai-sdk/provider-utils@2.2.2
  - @ai-sdk/react@1.2.4
  - @ai-sdk/ui-utils@1.2.3

## 4.2.8

### Patch Changes

- 65243ce: fix (ui): introduce step start parts
- Updated dependencies [65243ce]
  - @ai-sdk/ui-utils@1.2.2
  - @ai-sdk/react@1.2.3

## 4.2.7

### Patch Changes

- e14c066: fix (ai/core): convert user ui messages with only parts (no content) to core messages

## 4.2.6

### Patch Changes

- 625591b: feat (ai/core): auto-complete for provider registry
- 6a1506f: feat (ai/core): custom separator support for provider registry
- ea3d998: chore (ai/core): move provider registry to stable

## 4.2.5

### Patch Changes

- Updated dependencies [d92fa29]
  - @ai-sdk/react@1.2.2

## 4.2.4

### Patch Changes

- 3d6d96d: fix (ai/core): validate that messages are not empty

## 4.2.3

### Patch Changes

- 0b3bf29: fix (ai/core): custom env support for stdio MCP transport

## 4.2.2

### Patch Changes

- f10f0fa: fix (provider-utils): improve event source stream parsing performance
- Updated dependencies [f10f0fa]
  - @ai-sdk/provider-utils@2.2.1
  - @ai-sdk/react@1.2.1
  - @ai-sdk/ui-utils@1.2.1

## 4.2.1

### Patch Changes

- b796152: feat (ai/core): add headers to MCP SSE transport
- 06361d6: feat (ai/core): expose JSON RPC types (MCP)

## 4.2.0

### Minor Changes

- 5bc638d: AI SDK 4.2

### Patch Changes

- Updated dependencies [5bc638d]
  - @ai-sdk/provider@1.1.0
  - @ai-sdk/provider-utils@2.2.0
  - @ai-sdk/react@1.2.0
  - @ai-sdk/ui-utils@1.2.0

## 4.1.66

### Patch Changes

- 5d0fc29: chore (ai): improve cosine similarity calculation

## 4.1.65

### Patch Changes

- 16c444f: fix (ai): expose ai/mcp-stdio

## 4.1.64

### Patch Changes

- Updated dependencies [d0c4659]
  - @ai-sdk/provider-utils@2.1.15
  - @ai-sdk/react@1.1.25
  - @ai-sdk/ui-utils@1.1.21

## 4.1.63

### Patch Changes

- 0bd5bc6: feat (ai): support model-generated files
- Updated dependencies [0bd5bc6]
  - @ai-sdk/provider@1.0.12
  - @ai-sdk/provider-utils@2.1.14
  - @ai-sdk/ui-utils@1.1.20
  - @ai-sdk/react@1.1.24

## 4.1.62

### Patch Changes

- c9ed3c4: feat: enable custom mcp transports
  breaking change: remove internal stdio transport creation

## 4.1.61

### Patch Changes

- 2e1101a: feat (provider/openai): pdf input support
- Updated dependencies [2e1101a]
  - @ai-sdk/provider@1.0.11
  - @ai-sdk/provider-utils@2.1.13
  - @ai-sdk/ui-utils@1.1.19
  - @ai-sdk/react@1.1.23

## 4.1.60

### Patch Changes

- 0b8797f: feat (ai/core): expose response body for each generateText step

## 4.1.59

### Patch Changes

- dd18049: fix (ai/core): suppress next.js warnings for node.js specific code path

## 4.1.58

### Patch Changes

- e9897eb: fix (ai/core): move process access into functions and use globalThis

## 4.1.57

### Patch Changes

- 092fdaa: feat (ai/core): add defaultSettingsMiddleware

## 4.1.56

### Patch Changes

- 80be82b: feat (ai/core): add simulateStreamingMiddleware
- 8109a24: fix (ai/core): limit node imports to types where possible

## 4.1.55

### Patch Changes

- 1531959: feat (ai/core): add MCP client for using MCP tools
- Updated dependencies [1531959]
  - @ai-sdk/provider-utils@2.1.12
  - @ai-sdk/react@1.1.22
  - @ai-sdk/ui-utils@1.1.18

## 4.1.54

### Patch Changes

- ee1c787: fix (ai/core): correct spread apply order to fix extract reasoning middleware with generateText

## 4.1.53

### Patch Changes

- e1d3d42: feat (ai): expose raw response body in generateText and generateObject
- Updated dependencies [e1d3d42]
  - @ai-sdk/provider@1.0.10
  - @ai-sdk/provider-utils@2.1.11
  - @ai-sdk/ui-utils@1.1.17
  - @ai-sdk/react@1.1.21

## 4.1.52

### Patch Changes

- 5329a69: fix (ai/core): fix duplicated reasoning in streamText onFinish and messages

## 4.1.51

### Patch Changes

- 0cb2647: feat (ai/core): add streamText sendStart & sendFinish data stream options

## 4.1.50

### Patch Changes

- ae98f0d: fix (ai/core): forward providerOptions for text, image, and file parts

## 4.1.49

### Patch Changes

- dc027d3: fix (ai/core): add reasoning support to appendResponseMessages

## 4.1.48

### Patch Changes

- Updated dependencies [6255fbc]
  - @ai-sdk/react@1.1.20

## 4.1.47

### Patch Changes

- Updated dependencies [da5c734]
  - @ai-sdk/react@1.1.19

## 4.1.46

### Patch Changes

- ddf9740: feat (ai): add anthropic reasoning
- Updated dependencies [ddf9740]
  - @ai-sdk/provider@1.0.9
  - @ai-sdk/ui-utils@1.1.16
  - @ai-sdk/provider-utils@2.1.10
  - @ai-sdk/react@1.1.18

## 4.1.45

### Patch Changes

- 93bd5a0: feat (ai/ui): add writeSource to createDataStream

## 4.1.44

### Patch Changes

- f8e7df2: fix (ai/core): add `startWithReasoning` option to `extractReasoningMiddleware`

## 4.1.43

### Patch Changes

- ef2e23b: feat (ai/core): add experimental repairText function to generateObject

## 4.1.42

### Patch Changes

- Updated dependencies [2761f06]
  - @ai-sdk/provider@1.0.8
  - @ai-sdk/provider-utils@2.1.9
  - @ai-sdk/ui-utils@1.1.15
  - @ai-sdk/react@1.1.17

## 4.1.41

### Patch Changes

- Updated dependencies [60c3220]
  - @ai-sdk/react@1.1.16

## 4.1.40

### Patch Changes

- Updated dependencies [c43df41]
  - @ai-sdk/react@1.1.15

## 4.1.39

### Patch Changes

- 075a9a9: fix (ai): improve tsdoc on custom provider

## 4.1.38

### Patch Changes

- 4c9c194: chore (ai): add description to provider-defined tools for better accessibility
- 2e898b4: chore (ai): move mockId test helper into provider utils
- Updated dependencies [2e898b4]
  - @ai-sdk/provider-utils@2.1.8
  - @ai-sdk/react@1.1.14
  - @ai-sdk/ui-utils@1.1.14

## 4.1.37

### Patch Changes

- c1e10d1: chore: export UIMessage type

## 4.1.36

### Patch Changes

- Updated dependencies [3ff4ef8]
  - @ai-sdk/provider-utils@2.1.7
  - @ai-sdk/react@1.1.13
  - @ai-sdk/ui-utils@1.1.13

## 4.1.35

### Patch Changes

- 166e09e: feat (ai/ui): forward source parts to useChat
- Updated dependencies [166e09e]
  - @ai-sdk/ui-utils@1.1.12
  - @ai-sdk/react@1.1.12

## 4.1.34

### Patch Changes

- dc49119: chore: deprecate ai/react

## 4.1.33

### Patch Changes

- 74f0f0e: chore (ai/core): move providerMetadata to stable

## 4.1.32

### Patch Changes

- c128ca5: fix (ai/core): fix streamText onFinish messages with structured output

## 4.1.31

### Patch Changes

- b30b1cc: feat (ai/core): add onError callback to streamObject

## 4.1.30

### Patch Changes

- 4ee5b6f: fix (core): remove invalid providerOptions from streamObject onFinish callback

## 4.1.29

### Patch Changes

- 605de49: feat (ai/core): export callback types

## 4.1.28

### Patch Changes

- 6eb7fc4: feat (ai/core): url source support

## 4.1.27

### Patch Changes

- Updated dependencies [318b351]
  - @ai-sdk/ui-utils@1.1.11
  - @ai-sdk/react@1.1.11

## 4.1.26

### Patch Changes

- 34983d4: fix (ai/core): bind supportsUrl when creating wrapper

## 4.1.25

### Patch Changes

- 5a21310: fix (ai/core): use ai types on custom provider to prevent ts error

## 4.1.24

### Patch Changes

- 38142b8: feat (ai/core): introduce streamText consumeStream

## 4.1.23

### Patch Changes

- b08f7c1: fix (ai/core): suppress errors in textStream

## 4.1.22

### Patch Changes

- 2bec72a: feat (ai/core): add onError callback to streamText

## 4.1.21

### Patch Changes

- d387989: feat (ai/core): re-export zodSchema

## 4.1.20

### Patch Changes

- bcc61d4: feat (ui): introduce message parts for useChat
- Updated dependencies [bcc61d4]
  - @ai-sdk/ui-utils@1.1.10
  - @ai-sdk/react@1.1.10

## 4.1.19

### Patch Changes

- Updated dependencies [6b8cc14]
  - @ai-sdk/ui-utils@1.1.9
  - @ai-sdk/react@1.1.9

## 4.1.18

### Patch Changes

- 6a1acfe: fix (ai/core): revert '@internal' tag on function definitions due to build impacts

## 4.1.17

### Patch Changes

- 5af8cdb: fix (ai/core): support this reference in model.supportsUrl implementations

## 4.1.16

### Patch Changes

- 7e299a4: feat (ai/core): wrapLanguageModel can apply multiple middlewares

## 4.1.15

### Patch Changes

- d89c3b9: feat (provider): add image model support to provider specification
- d89c3b9: feat (core): type ahead for model ids with custom provider
- 08f54fc: chore (ai/core): move custom provider to stable
- Updated dependencies [d89c3b9]
  - @ai-sdk/provider@1.0.7
  - @ai-sdk/provider-utils@2.1.6
  - @ai-sdk/ui-utils@1.1.8
  - @ai-sdk/react@1.1.8

## 4.1.14

### Patch Changes

- ca89615: fix (ai/core): only append assistant response at the end when there is a final user message

## 4.1.13

### Patch Changes

- 999085e: feat (ai/core): add write function to DataStreamWriter

## 4.1.12

### Patch Changes

- 0d2d9bf: fix (ui): single assistant message with multiple tool steps
- Updated dependencies [0d2d9bf]
- Updated dependencies [0d2d9bf]
  - @ai-sdk/react@1.1.7
  - @ai-sdk/ui-utils@1.1.7

## 4.1.11

### Patch Changes

- 4c58da5: chore (core): move providerOptions to stable

## 4.1.10

### Patch Changes

- bf2c9c6: feat (core): move middleware to stable

## 4.1.9

### Patch Changes

- 3a602ca: chore (core): rename CoreTool to Tool
- Updated dependencies [3a602ca]
  - @ai-sdk/provider-utils@2.1.5
  - @ai-sdk/ui-utils@1.1.6
  - @ai-sdk/react@1.1.6

## 4.1.8

### Patch Changes

- 92f5f36: feat (core): add extractReasoningMiddleware

## 4.1.7

### Patch Changes

- 066206e: feat (provider-utils): move delay to provider-utils from ai
- Updated dependencies [066206e]
  - @ai-sdk/provider-utils@2.1.4
  - @ai-sdk/react@1.1.5
  - @ai-sdk/ui-utils@1.1.5

## 4.1.6

### Patch Changes

- Updated dependencies [39e5c1f]
  - @ai-sdk/provider-utils@2.1.3
  - @ai-sdk/react@1.1.4
  - @ai-sdk/ui-utils@1.1.4

## 4.1.5

### Patch Changes

- 9ce598c: feat (ai/ui): add reasoning support to useChat
- Updated dependencies [9ce598c]
  - @ai-sdk/ui-utils@1.1.3
  - @ai-sdk/react@1.1.3

## 4.1.4

### Patch Changes

- caaad11: feat (ai/core): re-export languagemodelv1 types for middleware implementations
- caaad11: feat (ai/core): expose TelemetrySettings type

## 4.1.3

### Patch Changes

- 7f30a77: feat (core): export core message schemas
- 4298996: feat (core): add helper for merging single client message

## 4.1.2

### Patch Changes

- 3c5fafa: chore (ai/core): move streamText toolCallStreaming option to stable
- 3a58a2e: feat (ai/core): throw NoImageGeneratedError from generateImage when no predictions are returned.
- Updated dependencies [ed012d2]
- Updated dependencies [6f4d063]
- Updated dependencies [3a58a2e]
  - @ai-sdk/provider-utils@2.1.2
  - @ai-sdk/react@1.1.2
  - @ai-sdk/provider@1.0.6
  - @ai-sdk/ui-utils@1.1.2

## 4.1.1

### Patch Changes

- 0a699f1: feat: add reasoning token support
- Updated dependencies [e7a9ec9]
- Updated dependencies [0a699f1]
  - @ai-sdk/ui-utils@1.1.1
  - @ai-sdk/provider-utils@2.1.1
  - @ai-sdk/provider@1.0.5
  - @ai-sdk/react@1.1.1

## 4.1.0

### Minor Changes

- 62ba5ad: release: AI SDK 4.1

### Patch Changes

- Updated dependencies [62ba5ad]
  - @ai-sdk/provider-utils@2.1.0
  - @ai-sdk/react@1.1.0
  - @ai-sdk/ui-utils@1.1.0

## 4.0.41

### Patch Changes

- Updated dependencies [44f04d5]
  - @ai-sdk/react@1.0.14

## 4.0.40

### Patch Changes

- 33592d2: fix (ai/core): switch to json schema 7 target for zod to json schema conversion
- Updated dependencies [33592d2]
  - @ai-sdk/ui-utils@1.0.12
  - @ai-sdk/react@1.0.13

## 4.0.39

### Patch Changes

- 00114c5: feat: expose IDGenerator and createIdGenerator
- 00114c5: feat (ui): generate and forward message ids for response messages
- Updated dependencies [00114c5]
- Updated dependencies [00114c5]
  - @ai-sdk/provider-utils@2.0.8
  - @ai-sdk/ui-utils@1.0.11
  - @ai-sdk/react@1.0.12

## 4.0.38

### Patch Changes

- 0118fa7: fix (ai/core): handle empty tool invocation array in convertToCoreMessages

## 4.0.37

### Patch Changes

- 8304ed8: feat (ai/core): Add option `throwErrorForEmptyVectors` to cosineSimilarity
- ed28182: feat (ai/ui): add appendResponseMessages helper

## 4.0.36

### Patch Changes

- Updated dependencies [37f4510]
  - @ai-sdk/ui-utils@1.0.10
  - @ai-sdk/react@1.0.11

## 4.0.35

### Patch Changes

- 3491f78: feat (ai/core): support multiple stream text transforms

## 4.0.34

### Patch Changes

- 2495973: feat (ai/core): use openai compatible mode for json schema conversion
- 2495973: fix (ai/core): duplicate instead of using reference in json schema
- Updated dependencies [2495973]
- Updated dependencies [2495973]
  - @ai-sdk/ui-utils@1.0.9
  - @ai-sdk/react@1.0.10

## 4.0.33

### Patch Changes

- 5510ee7: feat (ai/core): add stopStream option to streamText transforms

## 4.0.32

### Patch Changes

- de66619: feat (ai/core): add tool call id to ToolExecution error

## 4.0.31

### Patch Changes

- Updated dependencies [90fb95a]
- Updated dependencies [e6dfef4]
- Updated dependencies [6636db6]
  - @ai-sdk/provider-utils@2.0.7
  - @ai-sdk/react@1.0.9
  - @ai-sdk/ui-utils@1.0.8

## 4.0.30

### Patch Changes

- e4ce80c: fix (ai/core): prevent onFinish from masking stream errors

## 4.0.29

### Patch Changes

- a92f5f6: feat (ai/core): generate many images with parallel model calls

## 4.0.28

### Patch Changes

- 19a2ce7: feat (ai/core): add aspectRatio and seed options to generateImage
- 6337688: feat: change image generation errors to warnings
- 8b422ea: feat (ai/core): add caching to generated images
- Updated dependencies [19a2ce7]
- Updated dependencies [19a2ce7]
- Updated dependencies [6337688]
  - @ai-sdk/provider@1.0.4
  - @ai-sdk/provider-utils@2.0.6
  - @ai-sdk/ui-utils@1.0.7
  - @ai-sdk/react@1.0.8

## 4.0.27

### Patch Changes

- a56734f: feat (ai/core): export simulateReadableStream in ai package
- 9589601: feat (ai/core): support null delay in smoothStream
- e3cc23a: feat (ai/core): support regexp chunking pattern in smoothStream
- e463e73: feat (ai/core): support skipping delays in simulateReadableStream

## 4.0.26

### Patch Changes

- a8f3242: feat (ai/core): add line chunking mode to smoothStream

## 4.0.25

### Patch Changes

- 0823899: fix (ai/core): throw error when accessing output when no output is defined in generateText (breaking/experimental)

## 4.0.24

### Patch Changes

- ae0485b: feat (ai/core): add experimental output setting to streamText

## 4.0.23

### Patch Changes

- bc4cd19: feat (ai/core): consolidate whitespace in smooth stream

## 4.0.22

### Patch Changes

- Updated dependencies [5ed5e45]
  - @ai-sdk/provider-utils@2.0.5
  - @ai-sdk/provider@1.0.3
  - @ai-sdk/react@1.0.7
  - @ai-sdk/ui-utils@1.0.6

## 4.0.21

### Patch Changes

- a8669a2: fix (ai/core): prefer auto-detected image mimetype
- 6fb3e91: fix (ai/core): include type in generateText toolResults result property.

## 4.0.20

### Patch Changes

- da9d240: fix (ai/core): suppress errors caused by writing to closed stream
- 6f1bfde: fix (ai/core): invoke streamText tool call repair when tool cannot be found

## 4.0.19

### Patch Changes

- c3a6065: fix (ai/core): apply transform before callbacks and resolvables

## 4.0.18

### Patch Changes

- 304e6d3: feat (ai/core): standardize generateObject, streamObject, and output errors to NoObjectGeneratedError
- 304e6d3: feat (ai/core): add additional information to NoObjectGeneratedError

## 4.0.17

### Patch Changes

- 54bbf21: fix (ai/core): change streamText.experimental_transform signature to support tool type inference

## 4.0.16

### Patch Changes

- e3fac3f: fix (ai/core): change smoothStream default delay to 10ms

## 4.0.15

### Patch Changes

- cc16a83: feat (ai/core): add smoothStream helper
- cc16a83: feat (ai/core): add experimental transform option to streamText

## 4.0.14

### Patch Changes

- 09a9cab: feat (ai/core): add experimental generateImage function
- Updated dependencies [09a9cab]
  - @ai-sdk/provider@1.0.2
  - @ai-sdk/provider-utils@2.0.4
  - @ai-sdk/ui-utils@1.0.5
  - @ai-sdk/react@1.0.6

## 4.0.13

### Patch Changes

- 9f32213: feat (ai/core): add experimental tool call repair

## 4.0.12

### Patch Changes

- 5167bec: fix (ai/core): forward streamText errors as error parts
- 0984f0b: feat (ai/core): add ToolExecutionError type
- Updated dependencies [0984f0b]
  - @ai-sdk/provider-utils@2.0.3
  - @ai-sdk/react@1.0.5
  - @ai-sdk/ui-utils@1.0.4

## 4.0.11

### Patch Changes

- Updated dependencies [953469c]
- Updated dependencies [a3dd2ed]
  - @ai-sdk/ui-utils@1.0.3
  - @ai-sdk/react@1.0.4

## 4.0.10

### Patch Changes

- 913872d: fix (ai/core): track promise from async createDataStream.execute

## 4.0.9

### Patch Changes

- fda9695: feat (ai/core): reworked data stream management

## 4.0.8

### Patch Changes

- a803d76: feat (ai/core): pass toolCallId option into tool execute function

## 4.0.7

### Patch Changes

- 5b4f07b: fix (ai/core): change default error message for data streams to "An error occurred."

## 4.0.6

### Patch Changes

- fc18132: feat (ai/core): experimental output for generateText
- 2779f6d: fix (ai/core): do not send maxRetries into providers

## 4.0.5

### Patch Changes

- Updated dependencies [630ac31]
  - @ai-sdk/react@1.0.3

## 4.0.4

### Patch Changes

- 6ff6689: fix (ai): trigger onFinal when stream adapter finishes
- 6ff6689: chore (ai): deprecate onCompletion (stream callbacks)

## 4.0.3

### Patch Changes

- Updated dependencies [88b364b]
- Updated dependencies [b446ae5]
  - @ai-sdk/ui-utils@1.0.2
  - @ai-sdk/provider@1.0.1
  - @ai-sdk/react@1.0.2
  - @ai-sdk/provider-utils@2.0.2

## 4.0.2

### Patch Changes

- Updated dependencies [c3ab5de]
  - @ai-sdk/provider-utils@2.0.1
  - @ai-sdk/react@1.0.1
  - @ai-sdk/ui-utils@1.0.1

## 4.0.1

### Patch Changes

- b117255: feat (ai/core): add messages to tool call options

## 4.0.0

### Major Changes

- 4e38b38: chore (ai): remove LanguageModelResponseMetadataWithHeaders type
- 8bf5756: chore: remove legacy function/tool calling
- f0cb69d: chore (ai/core): remove experimental function exports
- da8c609: chore (ai): remove Tokens RSC helper
- cbab571: chore (ai): remove ExperimentalXXXMessage types
- b469a7e: chore: remove isXXXError methods
- 54cb888: chore (ai): remove experimental_StreamData export
- 4d61295: chore (ai): remove streamToResponse and streamingTextResponse
- 9a3d741: chore (ai): remove ExperimentalTool export
- 064257d: chore (ai/core): rename simulateReadableStream values parameter to chunks
- 60e69ed: chore (ai/core): remove ai-stream related methods from streamText
- a4f8ce9: chore (ai): AssistantResponse cleanups
- d3ae4f6: chore (ui/react): remove useObject setInput helper
- 7264b0a: chore (ai): remove responseMessages property from streamText/generateText result
- b801982: chore (ai/core): remove init option from streamText result methods
- f68d7b1: chore (ai/core): streamObject returns result immediately (no Promise)
- 6090cea: chore (ai): remove rawResponse from generate/stream result objects
- 073f282: chore (ai): remove AIStream and related exports
- 1c58337: chore (ai): remove 2.x prompt helpers
- a40a93d: chore (ai/ui): remove vue, svelte, solid re-export and dependency
- a7ad35a: chore: remove legacy providers & rsc render
- c0ddc24: chore (ai): remove toJSON method from AI SDK errors
- 007cb81: chore (ai): change `streamText` warnings result to Promise
- effbce3: chore (ai): remove responseMessage from streamText onFinish callback
- 545d133: chore (ai): remove deprecated roundtrip settings from streamText / generateText
- 7e89ccb: chore: remove nanoid export
- f967199: chore (ai/core): streamText returns result immediately (no Promise)
- 62d08fd: chore (ai): remove TokenUsage, CompletionTokenUsage, and EmbeddingTokenUsage types
- e5d2ce8: chore (ai): remove deprecated provider registry exports
- 70ce742: chore (ai): remove experimental_continuationSteps option
- 2f09717: chore (ai): remove deprecated telemetry data
- 0827bf9: chore (ai): remove LangChain adapter `toAIStream` method

### Patch Changes

- dce4158: chore (dependencies): update eventsource-parser to 3.0.0
- f0ec721: chore (ai): remove openai peer dependency
- f9bb30c: chore (ai): remove unnecessary dev dependencies
- b053413: chore (ui): refactorings & README update
- Updated dependencies [e117b54]
- Updated dependencies [8bf5756]
- Updated dependencies [b469a7e]
- Updated dependencies [79c6dd9]
- Updated dependencies [9f81e66]
- Updated dependencies [70f28f6]
- Updated dependencies [dce4158]
- Updated dependencies [d3ae4f6]
- Updated dependencies [68d30e9]
- Updated dependencies [7814c4b]
- Updated dependencies [ca3e586]
- Updated dependencies [c0ddc24]
- Updated dependencies [fe4f109]
- Updated dependencies [84edae5]
- Updated dependencies [b1da952]
- Updated dependencies [04d3747]
- Updated dependencies [dce4158]
- Updated dependencies [7e89ccb]
- Updated dependencies [8426f55]
- Updated dependencies [db46ce5]
- Updated dependencies [b053413]
  - @ai-sdk/react@1.0.0
  - @ai-sdk/ui-utils@1.0.0
  - @ai-sdk/provider-utils@2.0.0
  - @ai-sdk/provider@1.0.0

## 4.0.0-canary.13

### Major Changes

- 064257d: chore (ai/core): rename simulateReadableStream values parameter to chunks

### Patch Changes

- Updated dependencies [79c6dd9]
- Updated dependencies [04d3747]
  - @ai-sdk/react@1.0.0-canary.9
  - @ai-sdk/ui-utils@1.0.0-canary.9

## 4.0.0-canary.12

### Patch Changes

- b053413: chore (ui): refactorings & README update
- Updated dependencies [b053413]
  - @ai-sdk/ui-utils@1.0.0-canary.8
  - @ai-sdk/react@1.0.0-canary.8

## 4.0.0-canary.11

### Major Changes

- f68d7b1: chore (ai/core): streamObject returns result immediately (no Promise)
- f967199: chore (ai/core): streamText returns result immediately (no Promise)

## 4.0.0-canary.10

### Major Changes

- effbce3: chore (ai): remove responseMessage from streamText onFinish callback

### Patch Changes

- Updated dependencies [fe4f109]
  - @ai-sdk/ui-utils@1.0.0-canary.7
  - @ai-sdk/react@1.0.0-canary.7

## 4.0.0-canary.9

### Patch Changes

- f0ec721: chore (ai): remove openai peer dependency

## 4.0.0-canary.8

### Major Changes

- 007cb81: chore (ai): change `streamText` warnings result to Promise

### Patch Changes

- Updated dependencies [70f28f6]
  - @ai-sdk/ui-utils@1.0.0-canary.6
  - @ai-sdk/react@1.0.0-canary.6

## 4.0.0-canary.7

### Major Changes

- 4e38b38: chore (ai): remove LanguageModelResponseMetadataWithHeaders type
- 54cb888: chore (ai): remove experimental_StreamData export
- 9a3d741: chore (ai): remove ExperimentalTool export
- a4f8ce9: chore (ai): AssistantResponse cleanups
- 7264b0a: chore (ai): remove responseMessages property from streamText/generateText result
- 62d08fd: chore (ai): remove TokenUsage, CompletionTokenUsage, and EmbeddingTokenUsage types
- e5d2ce8: chore (ai): remove deprecated provider registry exports
- 70ce742: chore (ai): remove experimental_continuationSteps option
- 0827bf9: chore (ai): remove LangChain adapter `toAIStream` method

## 4.0.0-canary.6

### Major Changes

- b801982: chore (ai/core): remove init option from streamText result methods

### Patch Changes

- f9bb30c: chore (ai): remove unnecessary dev dependencies

## 4.0.0-canary.5

### Major Changes

- 4d61295: chore (ai): remove streamToResponse and streamingTextResponse
- d3ae4f6: chore (ui/react): remove useObject setInput helper
- 6090cea: chore (ai): remove rawResponse from generate/stream result objects
- 2f09717: chore (ai): remove deprecated telemetry data

### Patch Changes

- Updated dependencies [9f81e66]
- Updated dependencies [d3ae4f6]
- Updated dependencies [8426f55]
  - @ai-sdk/ui-utils@1.0.0-canary.5
  - @ai-sdk/react@1.0.0-canary.5
  - @ai-sdk/provider-utils@2.0.0-canary.3

## 4.0.0-canary.4

### Major Changes

- f0cb69d: chore (ai/core): remove experimental function exports
- da8c609: chore (ai): remove Tokens RSC helper
- cbab571: chore (ai): remove ExperimentalXXXMessage types
- 60e69ed: chore (ai/core): remove ai-stream related methods from streamText
- 073f282: chore (ai): remove AIStream and related exports
- 545d133: chore (ai): remove deprecated roundtrip settings from streamText / generateText

### Patch Changes

- dce4158: chore (dependencies): update eventsource-parser to 3.0.0
- Updated dependencies [dce4158]
- Updated dependencies [ca3e586]
- Updated dependencies [dce4158]
  - @ai-sdk/provider-utils@2.0.0-canary.2
  - @ai-sdk/react@1.0.0-canary.4
  - @ai-sdk/ui-utils@1.0.0-canary.4

## 4.0.0-canary.3

### Patch Changes

- Updated dependencies [68d30e9]
- Updated dependencies [b1da952]
  - @ai-sdk/react@1.0.0-canary.3
  - @ai-sdk/provider-utils@2.0.0-canary.1
  - @ai-sdk/ui-utils@1.0.0-canary.3

## 4.0.0-canary.2

### Major Changes

- b469a7e: chore: remove isXXXError methods
- c0ddc24: chore (ai): remove toJSON method from AI SDK errors

### Patch Changes

- Updated dependencies [e117b54]
- Updated dependencies [b469a7e]
- Updated dependencies [7814c4b]
- Updated dependencies [c0ddc24]
- Updated dependencies [db46ce5]
  - @ai-sdk/react@1.0.0-canary.2
  - @ai-sdk/provider-utils@2.0.0-canary.0
  - @ai-sdk/provider@1.0.0-canary.0
  - @ai-sdk/ui-utils@1.0.0-canary.2

## 4.0.0-canary.1

### Major Changes

- 8bf5756: chore: remove legacy function/tool calling

### Patch Changes

- 1c58337: chore (ai): remove 2.x prompt helpers
- Updated dependencies [8bf5756]
  - @ai-sdk/ui-utils@1.0.0-canary.1
  - @ai-sdk/react@1.0.0-canary.1

## 4.0.0-canary.0

### Major Changes

- a40a93d: chore (ai/ui): remove vue, svelte, solid re-export and dependency

### Patch Changes

- a7ad35a: chore: remove legacy providers & rsc render
- 7e89ccb: chore: remove nanoid export
- Updated dependencies [84edae5]
- Updated dependencies [7e89ccb]
  - @ai-sdk/react@1.0.0-canary.0
  - @ai-sdk/ui-utils@1.0.0-canary.0

## 3.4.33

### Patch Changes

- ac380e3: fix (provider/anthropic): continuation mode with 3+ steps

## 3.4.32

### Patch Changes

- 6bb9e51: fix (ai/core): expose response.messages in streamText

## 3.4.31

### Patch Changes

- Updated dependencies [2dfb93e]
  - @ai-sdk/react@0.0.70

## 3.4.30

### Patch Changes

- Updated dependencies [a85c965]
  - @ai-sdk/ui-utils@0.0.50
  - @ai-sdk/react@0.0.69
  - @ai-sdk/solid@0.0.54
  - @ai-sdk/svelte@0.0.57
  - @ai-sdk/vue@0.0.59

## 3.4.29

### Patch Changes

- 54b56f7: feat (ai/core): send tool and tool choice telemetry data

## 3.4.28

### Patch Changes

- 29f1390: feat (ai/test): add simulateReadableStream helper

## 3.4.27

### Patch Changes

- fa772ae: feat (ai/core): automatically convert ui messages to core messages

## 3.4.26

### Patch Changes

- 57f39ea: feat (ai/core): support multi-modal tool results in convertToCoreMessages

## 3.4.25

### Patch Changes

- 6e0fa1c: fix (ai/core): wait for tool results to arrive before sending finish event

## 3.4.24

### Patch Changes

- d92fd9f: feat (ui/svelte): support Svelte 5 peer dependency
- Updated dependencies [d92fd9f]
  - @ai-sdk/svelte@0.0.56

## 3.4.23

### Patch Changes

- 8301e41: fix (ai/react): update React peer dependency version to allow rc releases.
- Updated dependencies [8301e41]
  - @ai-sdk/react@0.0.68

## 3.4.22

### Patch Changes

- Updated dependencies [3bf8da0]
  - @ai-sdk/ui-utils@0.0.49
  - @ai-sdk/react@0.0.67
  - @ai-sdk/solid@0.0.53
  - @ai-sdk/svelte@0.0.55
  - @ai-sdk/vue@0.0.58

## 3.4.21

### Patch Changes

- 3954471: (experimental) fix passing "experimental_toToolResultContent" into PoolResultPart

## 3.4.20

### Patch Changes

- aa98cdb: chore: more flexible dependency versioning
- 1486128: feat: add supportsUrl to language model specification
- 3b1b69a: feat: provider-defined tools
- 85b98da: revert fix (ai/core): handle tool calls without results in message conversion
- 7ceed77: feat (ai/core): expose response message for each step
- 811a317: feat (ai/core): multi-part tool results (incl. images)
- Updated dependencies [aa98cdb]
- Updated dependencies [1486128]
- Updated dependencies [7b937c5]
- Updated dependencies [3b1b69a]
- Updated dependencies [811a317]
  - @ai-sdk/provider-utils@1.0.22
  - @ai-sdk/provider@0.0.26
  - @ai-sdk/ui-utils@0.0.48
  - @ai-sdk/svelte@0.0.54
  - @ai-sdk/react@0.0.66
  - @ai-sdk/vue@0.0.57
  - @ai-sdk/solid@0.0.52

## 3.4.19

### Patch Changes

- b9b0d7b: feat (ai): access raw request body
- Updated dependencies [b9b0d7b]
  - @ai-sdk/provider@0.0.25
  - @ai-sdk/provider-utils@1.0.21
  - @ai-sdk/ui-utils@0.0.47
  - @ai-sdk/react@0.0.65
  - @ai-sdk/solid@0.0.51
  - @ai-sdk/svelte@0.0.53
  - @ai-sdk/vue@0.0.56

## 3.4.18

### Patch Changes

- 95c67b4: fix (ai/core): handle tool calls without results in message conversion

## 3.4.17

### Patch Changes

- e4ff512: fix (core): prevent unnecessary input/output serialization when telemetry is not enabled

## 3.4.16

### Patch Changes

- 01dcc44: feat (ai/core): add experimental activeTools option to generateText and streamText

## 3.4.15

### Patch Changes

- Updated dependencies [98a3b08]
  - @ai-sdk/react@0.0.64

## 3.4.14

### Patch Changes

- e930f40: feat (ai/core): expose core tool result and tool call types

## 3.4.13

### Patch Changes

- fc39158: fix (ai/core): add abortSignal to tool helper function

## 3.4.12

### Patch Changes

- a23da5b: feat (ai/core): forward abort signal to tools

## 3.4.11

### Patch Changes

- caedcda: feat (ai/ui): add setData helper to useChat
- Updated dependencies [caedcda]
  - @ai-sdk/svelte@0.0.52
  - @ai-sdk/react@0.0.63
  - @ai-sdk/solid@0.0.50
  - @ai-sdk/vue@0.0.55

## 3.4.10

### Patch Changes

- 0b557d7: feat (ai/core): add tracer option to telemetry settings
- 44f6bc5: feat (ai/core): expose StepResult type

## 3.4.9

### Patch Changes

- d347538: fix (ai/core): export FilePart interface

## 3.4.8

### Patch Changes

- Updated dependencies [b5f577e]
  - @ai-sdk/vue@0.0.54

## 3.4.7

### Patch Changes

- db04700: feat (core): support converting attachments to file parts
- 988707c: feat (ai/core): automatically download files from urls

## 3.4.6

### Patch Changes

- d595d0d: feat (ai/core): file content parts
- Updated dependencies [d595d0d]
  - @ai-sdk/provider@0.0.24
  - @ai-sdk/provider-utils@1.0.20
  - @ai-sdk/ui-utils@0.0.46
  - @ai-sdk/react@0.0.62
  - @ai-sdk/solid@0.0.49
  - @ai-sdk/svelte@0.0.51
  - @ai-sdk/vue@0.0.53

## 3.4.5

### Patch Changes

- cd77c5d: feat (ai/core): add isContinued to steps
- Updated dependencies [cd77c5d]
  - @ai-sdk/ui-utils@0.0.45
  - @ai-sdk/react@0.0.61
  - @ai-sdk/solid@0.0.48
  - @ai-sdk/svelte@0.0.50
  - @ai-sdk/vue@0.0.52

## 3.4.4

### Patch Changes

- 4db074b: fix (ai/core): correct whitespace in generateText continueSteps
- 1297e1b: fix (ai/core): correct whitespace in streamText continueSteps

## 3.4.3

### Patch Changes

- b270ae3: feat (ai/core): streamText continueSteps (experimental)
- b270ae3: chore (ai/core): rename generateText continuationSteps to continueSteps

## 3.4.2

### Patch Changes

- e6c7e98: feat (ai/core): add continuationSteps to generateText

## 3.4.1

### Patch Changes

- Updated dependencies [7e7104f]
  - @ai-sdk/react@0.0.60

## 3.4.0

### Minor Changes

- c0cea03: release (ai): 3.4

## 3.3.44

### Patch Changes

- Updated dependencies [d3933e0]
  - @ai-sdk/vue@0.0.51

## 3.3.43

### Patch Changes

- fea6bec: fix (ai/core): support tool calls without arguments

## 3.3.42

### Patch Changes

- de37aee: feat (ai): Add support for LlamaIndex

## 3.3.41

### Patch Changes

- Updated dependencies [692e265]
  - @ai-sdk/vue@0.0.50

## 3.3.40

### Patch Changes

- a91c308: feat (ai/core): add responseMessages to streamText

## 3.3.39

### Patch Changes

- 33cf3e1: feat (ai/core): add providerMetadata to StepResult
- 17ee757: feat (ai/core): add onStepFinish callback to generateText

## 3.3.38

### Patch Changes

- 83da52c: feat (ai/core): add onStepFinish callback to streamText

## 3.3.37

### Patch Changes

- Updated dependencies [273f696]
  - @ai-sdk/provider-utils@1.0.19
  - @ai-sdk/react@0.0.59
  - @ai-sdk/solid@0.0.47
  - @ai-sdk/svelte@0.0.49
  - @ai-sdk/ui-utils@0.0.44
  - @ai-sdk/vue@0.0.49

## 3.3.36

### Patch Changes

- a3882f5: feat (ai/core): add steps property to streamText result and onFinish callback
- 1f590ef: chore (ai): rename roundtrips to steps
- 7e82d36: fix (ai/core): pass topK to providers
- Updated dependencies [54862e4]
- Updated dependencies [1f590ef]
  - @ai-sdk/react@0.0.58
  - @ai-sdk/ui-utils@0.0.43
  - @ai-sdk/solid@0.0.46
  - @ai-sdk/svelte@0.0.48
  - @ai-sdk/vue@0.0.48

## 3.3.35

### Patch Changes

- 14210d5: feat (ai/core): add sendUsage information to streamText data stream methods
- Updated dependencies [14210d5]
  - @ai-sdk/ui-utils@0.0.42
  - @ai-sdk/react@0.0.57
  - @ai-sdk/solid@0.0.45
  - @ai-sdk/svelte@0.0.47
  - @ai-sdk/vue@0.0.47

## 3.3.34

### Patch Changes

- a0403d6: feat (react): support sending attachments using append
- 678449a: feat (ai/core): export test helpers
- ff22fac: fix (ai/rsc): streamUI onFinish is called when tool calls have finished
- Updated dependencies [a0403d6]
  - @ai-sdk/react@0.0.56

## 3.3.33

### Patch Changes

- cbddc83: fix (ai/core): filter out empty text parts

## 3.3.32

### Patch Changes

- ce7a4af: feat (ai/core): support providerMetadata in functions

## 3.3.31

### Patch Changes

- 561fd7e: feat (ai/core): add output: enum to generateObject

## 3.3.30

### Patch Changes

- 6ee1f8e: feat (ai/core): add toDataStream to streamText result

## 3.3.29

### Patch Changes

- 1e3dfd2: feat (ai/core): enhance pipeToData/TextStreamResponse methods

## 3.3.28

### Patch Changes

- db61c53: feat (ai/core): middleware support

## 3.3.27

### Patch Changes

- 03313cd: feat (ai): expose response id, response model, response timestamp in telemetry and api
- 3be7c1c: fix (provider/anthropic): support prompt caching on assistant messages
- Updated dependencies [03313cd]
- Updated dependencies [3be7c1c]
  - @ai-sdk/provider-utils@1.0.18
  - @ai-sdk/provider@0.0.23
  - @ai-sdk/react@0.0.55
  - @ai-sdk/solid@0.0.44
  - @ai-sdk/svelte@0.0.46
  - @ai-sdk/ui-utils@0.0.41
  - @ai-sdk/vue@0.0.46

## 3.3.26

### Patch Changes

- Updated dependencies [4ab883f]
  - @ai-sdk/react@0.0.54

## 3.3.25

### Patch Changes

- 4f1530f: feat (ai/core): add OpenTelemetry Semantic Conventions for GenAI operations to v1.27.0 of standard
- dad775f: feat (ai/core): add finish event and avg output tokens per second (telemetry)

## 3.3.24

### Patch Changes

- d87a655: fix (ai/core): provide fallback when globalThis.performance is not available

## 3.3.23

### Patch Changes

- b55e6f7: fix (ai/core): streamObject text stream in array mode must not include elements: prefix.

## 3.3.22

### Patch Changes

- a5a56fd: fix (ai/core): only send roundtrip-finish event after async tool calls are done

## 3.3.21

### Patch Changes

- aa2dc58: feat (ai/core): add maxToolRoundtrips to streamText
- Updated dependencies [aa2dc58]
  - @ai-sdk/ui-utils@0.0.40
  - @ai-sdk/react@0.0.53
  - @ai-sdk/solid@0.0.43
  - @ai-sdk/svelte@0.0.45
  - @ai-sdk/vue@0.0.45

## 3.3.20

### Patch Changes

- 7807677: fix (rsc): Deep clone currentState in getMutableState()

## 3.3.19

### Patch Changes

- 7235de0: fix (ai/core): convertToCoreMessages accepts Message[]

## 3.3.18

### Patch Changes

- 9e3b5a5: feat (ai/core): add experimental_customProvider
- 26515cb: feat (ai/provider): introduce ProviderV1 specification
- Updated dependencies [26515cb]
  - @ai-sdk/provider@0.0.22
  - @ai-sdk/provider-utils@1.0.17
  - @ai-sdk/ui-utils@0.0.39
  - @ai-sdk/react@0.0.52
  - @ai-sdk/solid@0.0.42
  - @ai-sdk/svelte@0.0.44
  - @ai-sdk/vue@0.0.44

## 3.3.17

### Patch Changes

- d151349: feat (ai/core): array output for generateObject / streamObject
- Updated dependencies [d151349]
  - @ai-sdk/ui-utils@0.0.38
  - @ai-sdk/react@0.0.51
  - @ai-sdk/solid@0.0.41
  - @ai-sdk/svelte@0.0.43
  - @ai-sdk/vue@0.0.43

## 3.3.16

### Patch Changes

- 09f895f: feat (ai/core): no-schema output for generateObject / streamObject
- Updated dependencies [09f895f]
  - @ai-sdk/provider-utils@1.0.16
  - @ai-sdk/react@0.0.50
  - @ai-sdk/solid@0.0.40
  - @ai-sdk/svelte@0.0.42
  - @ai-sdk/ui-utils@0.0.37
  - @ai-sdk/vue@0.0.42

## 3.3.15

### Patch Changes

- b5a82b7: chore (ai): update zod-to-json-schema to 3.23.2
- Updated dependencies [b5a82b7]
  - @ai-sdk/ui-utils@0.0.36
  - @ai-sdk/react@0.0.49
  - @ai-sdk/solid@0.0.39
  - @ai-sdk/svelte@0.0.41
  - @ai-sdk/vue@0.0.41

## 3.3.14

### Patch Changes

- Updated dependencies [d67fa9c]
  - @ai-sdk/provider-utils@1.0.15
  - @ai-sdk/react@0.0.48
  - @ai-sdk/solid@0.0.38
  - @ai-sdk/svelte@0.0.40
  - @ai-sdk/ui-utils@0.0.35
  - @ai-sdk/vue@0.0.40

## 3.3.13

### Patch Changes

- 412f943: fix (ai/core): make Buffer validation optional for environments without buffer

## 3.3.12

### Patch Changes

- f2c025e: feat (ai/core): prompt validation
- Updated dependencies [f2c025e]
  - @ai-sdk/provider@0.0.21
  - @ai-sdk/provider-utils@1.0.14
  - @ai-sdk/ui-utils@0.0.34
  - @ai-sdk/react@0.0.47
  - @ai-sdk/solid@0.0.37
  - @ai-sdk/svelte@0.0.39
  - @ai-sdk/vue@0.0.39

## 3.3.11

### Patch Changes

- 03eb0f4: feat (ai/core): add "ai.operationId" telemetry attribute
- 099db96: feat (ai/core): add msToFirstChunk telemetry data
- Updated dependencies [b6c1dee]
  - @ai-sdk/react@0.0.46

## 3.3.10

### Patch Changes

- Updated dependencies [04084a3]
  - @ai-sdk/vue@0.0.38

## 3.3.9

### Patch Changes

- 6ac355e: feat (provider/anthropic): add cache control support
- b56dee1: chore (ai): deprecate prompt helpers
- Updated dependencies [6ac355e]
  - @ai-sdk/provider@0.0.20
  - @ai-sdk/provider-utils@1.0.13
  - @ai-sdk/ui-utils@0.0.33
  - @ai-sdk/react@0.0.45
  - @ai-sdk/solid@0.0.36
  - @ai-sdk/svelte@0.0.38
  - @ai-sdk/vue@0.0.37

## 3.3.8

### Patch Changes

- Updated dependencies [dd712ac]
  - @ai-sdk/provider-utils@1.0.12
  - @ai-sdk/ui-utils@0.0.32
  - @ai-sdk/react@0.0.44
  - @ai-sdk/solid@0.0.35
  - @ai-sdk/svelte@0.0.37
  - @ai-sdk/vue@0.0.36

## 3.3.7

### Patch Changes

- eccbd8e: feat (ai/core): add onChunk callback to streamText
- Updated dependencies [dd4a0f5]
  - @ai-sdk/provider@0.0.19
  - @ai-sdk/provider-utils@1.0.11
  - @ai-sdk/ui-utils@0.0.31
  - @ai-sdk/react@0.0.43
  - @ai-sdk/solid@0.0.34
  - @ai-sdk/svelte@0.0.36
  - @ai-sdk/vue@0.0.35

## 3.3.6

### Patch Changes

- e9c891d: feat (ai/react): useObject supports non-Zod schemas
- 3719e8a: chore (ai/core): provider registry code improvements
- Updated dependencies [e9c891d]
- Updated dependencies [4bd27a9]
- Updated dependencies [845754b]
  - @ai-sdk/ui-utils@0.0.30
  - @ai-sdk/react@0.0.42
  - @ai-sdk/provider-utils@1.0.10
  - @ai-sdk/provider@0.0.18
  - @ai-sdk/solid@0.0.33
  - @ai-sdk/svelte@0.0.35
  - @ai-sdk/vue@0.0.34

## 3.3.5

### Patch Changes

- 9ada023: feat (ai/core): mask data stream error messages with streamText
- Updated dependencies [e5b58f3]
  - @ai-sdk/ui-utils@0.0.29
  - @ai-sdk/react@0.0.41
  - @ai-sdk/solid@0.0.32
  - @ai-sdk/svelte@0.0.34
  - @ai-sdk/vue@0.0.33

## 3.3.4

### Patch Changes

- 029af4c: feat (ai/core): support schema name & description in generateObject & streamObject
- 3806c0c: chore (ai/ui): increase stream data warning timeout to 15 seconds
- db0118a: feat (ai/core): export Schema type
- Updated dependencies [029af4c]
  - @ai-sdk/provider@0.0.17
  - @ai-sdk/provider-utils@1.0.9
  - @ai-sdk/ui-utils@0.0.28
  - @ai-sdk/react@0.0.40
  - @ai-sdk/solid@0.0.31
  - @ai-sdk/svelte@0.0.33
  - @ai-sdk/vue@0.0.32

## 3.3.3

### Patch Changes

- d58517b: feat (ai/openai): structured outputs
- Updated dependencies [d58517b]
  - @ai-sdk/provider@0.0.16
  - @ai-sdk/provider-utils@1.0.8
  - @ai-sdk/ui-utils@0.0.27
  - @ai-sdk/react@0.0.39
  - @ai-sdk/solid@0.0.30
  - @ai-sdk/svelte@0.0.32
  - @ai-sdk/vue@0.0.31

## 3.3.2

### Patch Changes

- Updated dependencies [96aed25]
  - @ai-sdk/provider@0.0.15
  - @ai-sdk/provider-utils@1.0.7
  - @ai-sdk/ui-utils@0.0.26
  - @ai-sdk/react@0.0.38
  - @ai-sdk/solid@0.0.29
  - @ai-sdk/svelte@0.0.31
  - @ai-sdk/vue@0.0.30

## 3.3.1

### Patch Changes

- 9614584: fix (ai/core): use Symbol.for
- 0762a22: feat (ai/core): support zod transformers in generateObject & streamObject
- Updated dependencies [9614584]
- Updated dependencies [0762a22]
  - @ai-sdk/provider-utils@1.0.6
  - @ai-sdk/react@0.0.37
  - @ai-sdk/solid@0.0.28
  - @ai-sdk/svelte@0.0.30
  - @ai-sdk/ui-utils@0.0.25
  - @ai-sdk/vue@0.0.29

## 3.3.0

### Minor Changes

- dbc3afb7: chore (ai): release AI SDK 3.3

### Patch Changes

- b9827186: feat (ai/core): update operation.name telemetry attribute to include function id and detailed name

## 3.2.45

### Patch Changes

- Updated dependencies [5be25124]
  - @ai-sdk/ui-utils@0.0.24
  - @ai-sdk/react@0.0.36
  - @ai-sdk/solid@0.0.27
  - @ai-sdk/svelte@0.0.29
  - @ai-sdk/vue@0.0.28

## 3.2.44

### Patch Changes

- Updated dependencies [a147d040]
  - @ai-sdk/react@0.0.35

## 3.2.43

### Patch Changes

- Updated dependencies [b68fae4f]
  - @ai-sdk/react@0.0.34

## 3.2.42

### Patch Changes

- f63c99e7: feat (ai/core): record OpenTelemetry gen_ai attributes
- Updated dependencies [fea7b604]
  - @ai-sdk/ui-utils@0.0.23
  - @ai-sdk/react@0.0.33
  - @ai-sdk/solid@0.0.26
  - @ai-sdk/svelte@0.0.28
  - @ai-sdk/vue@0.0.27

## 3.2.41

### Patch Changes

- a12044c7: feat (ai/core): add recordInputs / recordOutputs setting to telemetry options
- Updated dependencies [1d93d716]
  - @ai-sdk/ui-utils@0.0.22
  - @ai-sdk/react@0.0.32
  - @ai-sdk/solid@0.0.25
  - @ai-sdk/svelte@0.0.27
  - @ai-sdk/vue@0.0.26

## 3.2.40

### Patch Changes

- f56b7e66: feat (ai/ui): add toDataStreamResponse to LangchainAdapter.

## 3.2.39

### Patch Changes

- b694f2f9: feat (ai/svelte): add tool calling support to useChat
- Updated dependencies [b694f2f9]
  - @ai-sdk/svelte@0.0.26

## 3.2.38

### Patch Changes

- 5c4b8cfc: chore (ai/core): rename ai stream methods to data stream (in streamText, LangChainAdapter).
- c450fcf7: feat (ui): invoke useChat onFinish with finishReason and tokens
- e4a1719f: chore (ai/ui): rename streamMode to streamProtocol
- 10158bf2: fix (ai/core): generateObject.doGenerate sets object telemetry attribute
- Updated dependencies [c450fcf7]
- Updated dependencies [e4a1719f]
  - @ai-sdk/ui-utils@0.0.21
  - @ai-sdk/svelte@0.0.25
  - @ai-sdk/react@0.0.31
  - @ai-sdk/solid@0.0.24
  - @ai-sdk/vue@0.0.25

## 3.2.37

### Patch Changes

- b2bee4c5: fix (ai/ui): send data, body, headers in useChat().reload
- Updated dependencies [b2bee4c5]
  - @ai-sdk/svelte@0.0.24
  - @ai-sdk/react@0.0.30
  - @ai-sdk/solid@0.0.23

## 3.2.36

### Patch Changes

- a8d1c9e9: feat (ai/core): parallel image download
- cfa360a8: feat (ai/core): add telemetry support to embedMany function.
- 49808ca5: feat (ai/core): add telemetry to streamObject
- Updated dependencies [a8d1c9e9]
  - @ai-sdk/provider-utils@1.0.5
  - @ai-sdk/provider@0.0.14
  - @ai-sdk/react@0.0.29
  - @ai-sdk/svelte@0.0.23
  - @ai-sdk/ui-utils@0.0.20
  - @ai-sdk/vue@0.0.24
  - @ai-sdk/solid@0.0.22

## 3.2.35

### Patch Changes

- 1be014b7: feat (ai/core): add telemetry support for embed function.
- 4f88248f: feat (core): support json schema
- 0d545231: chore (ai/svelte): change sswr into optional peer dependency
- Updated dependencies [4f88248f]
  - @ai-sdk/provider-utils@1.0.4
  - @ai-sdk/react@0.0.28
  - @ai-sdk/svelte@0.0.22
  - @ai-sdk/ui-utils@0.0.19
  - @ai-sdk/vue@0.0.23
  - @ai-sdk/solid@0.0.21

## 3.2.34

### Patch Changes

- 2b9da0f0: feat (core): support stopSequences setting.
- a5b58845: feat (core): support topK setting
- 420f170f: chore (ai/core): use interfaces for core function results
- 13b27ec6: chore (ai/core): remove grammar mode
- 644f6582: feat (ai/core): add telemetry to generateObject
- Updated dependencies [2b9da0f0]
- Updated dependencies [a5b58845]
- Updated dependencies [4aa8deb3]
- Updated dependencies [13b27ec6]
  - @ai-sdk/provider@0.0.13
  - @ai-sdk/provider-utils@1.0.3
  - @ai-sdk/react@0.0.27
  - @ai-sdk/svelte@0.0.21
  - @ai-sdk/ui-utils@0.0.18
  - @ai-sdk/solid@0.0.20
  - @ai-sdk/vue@0.0.22

## 3.2.33

### Patch Changes

- 4b2c09d9: feat (ai/ui): add mutator function support to useChat / setMessages
- 281e7662: chore: add description to ai package
- Updated dependencies [f63829fe]
- Updated dependencies [4b2c09d9]
  - @ai-sdk/ui-utils@0.0.17
  - @ai-sdk/svelte@0.0.20
  - @ai-sdk/react@0.0.26
  - @ai-sdk/solid@0.0.19
  - @ai-sdk/vue@0.0.21

## 3.2.32

### Patch Changes

- Updated dependencies [5b7b3bbe]
  - @ai-sdk/ui-utils@0.0.16
  - @ai-sdk/react@0.0.25
  - @ai-sdk/solid@0.0.18
  - @ai-sdk/svelte@0.0.19
  - @ai-sdk/vue@0.0.20

## 3.2.31

### Patch Changes

- b86af092: feat (ai/core): add langchain stream event v2 support to LangChainAdapter

## 3.2.30

### Patch Changes

- Updated dependencies [19c3d50f]
  - @ai-sdk/react@0.0.24
  - @ai-sdk/vue@0.0.19

## 3.2.29

### Patch Changes

- e710b388: fix (ai/core): race condition in mergeStreams
- 6078a690: feat (ai/core): introduce stream data support in toAIStreamResponse

## 3.2.28

### Patch Changes

- 68d1f78c: fix (ai/core): do not construct object promise in streamObject result until requested
- f0bc1e79: feat (ai/ui): add system message support to convertToCoreMessages
- 1f67fe49: feat (ai/ui): stream tool calls with streamText and useChat
- Updated dependencies [1f67fe49]
  - @ai-sdk/ui-utils@0.0.15
  - @ai-sdk/react@0.0.23
  - @ai-sdk/solid@0.0.17
  - @ai-sdk/svelte@0.0.18
  - @ai-sdk/vue@0.0.18

## 3.2.27

### Patch Changes

- 811f4493: fix (ai/core): generateText token usage is sum over all roundtrips

## 3.2.26

### Patch Changes

- 8f545ce9: fix (ai/core): forward request headers in generateObject and streamObject

## 3.2.25

### Patch Changes

- 99ddbb74: feat (ai/react): add experimental support for managing attachments to useChat
- Updated dependencies [99ddbb74]
  - @ai-sdk/ui-utils@0.0.14
  - @ai-sdk/react@0.0.22
  - @ai-sdk/solid@0.0.16
  - @ai-sdk/svelte@0.0.17
  - @ai-sdk/vue@0.0.17

## 3.2.24

### Patch Changes

- f041c056: feat (ai/core): add roundtrips property to generateText result

## 3.2.23

### Patch Changes

- a6cb2c8b: feat (ai/ui): add keepLastMessageOnError option to useChat
- Updated dependencies [a6cb2c8b]
  - @ai-sdk/ui-utils@0.0.13
  - @ai-sdk/svelte@0.0.16
  - @ai-sdk/react@0.0.21
  - @ai-sdk/solid@0.0.15
  - @ai-sdk/vue@0.0.16

## 3.2.22

### Patch Changes

- 53fccf1c: fix (ai/core): report error on controller
- dd0d854e: feat (ai/vue): add useAssistant
- Updated dependencies [dd0d854e]
  - @ai-sdk/vue@0.0.15

## 3.2.21

### Patch Changes

- 56bbc2a7: feat (ai/ui): set body and headers directly on options for handleSubmit and append
- Updated dependencies [56bbc2a7]
  - @ai-sdk/ui-utils@0.0.12
  - @ai-sdk/svelte@0.0.15
  - @ai-sdk/react@0.0.20
  - @ai-sdk/solid@0.0.14
  - @ai-sdk/vue@0.0.14

## 3.2.20

### Patch Changes

- 671331b6: feat (core): add experimental OpenTelemetry support for generateText and streamText

## 3.2.19

### Patch Changes

- b7290943: chore (ai/core): rename TokenUsage type to CompletionTokenUsage
- b7290943: feat (ai/core): add token usage to embed and embedMany
- Updated dependencies [b7290943]
  - @ai-sdk/provider@0.0.12
  - @ai-sdk/provider-utils@1.0.2
  - @ai-sdk/react@0.0.19
  - @ai-sdk/svelte@0.0.14
  - @ai-sdk/ui-utils@0.0.11
  - @ai-sdk/solid@0.0.13
  - @ai-sdk/vue@0.0.13

## 3.2.18

### Patch Changes

- Updated dependencies [70d18003]
  - @ai-sdk/react@0.0.18

## 3.2.17

### Patch Changes

- 3db90c3d: allow empty handleSubmit submissions for useChat
- abb22602: feat (ai): verify that system messages have string content
- 5c1f0bd3: fix unclosed streamable value console message
- Updated dependencies [6a11cfaa]
- Updated dependencies [3db90c3d]
- Updated dependencies [d481729f]
  - @ai-sdk/react@0.0.17
  - @ai-sdk/svelte@0.0.13
  - @ai-sdk/solid@0.0.12
  - @ai-sdk/vue@0.0.12
  - @ai-sdk/provider-utils@1.0.1
  - @ai-sdk/ui-utils@0.0.10

## 3.2.16

### Patch Changes

- Updated dependencies [3f756a6b]
  - @ai-sdk/react@0.0.16

## 3.2.15

### Patch Changes

- 6c99581e: fix (ai/react): stop() on useObject does not throw error and clears isLoading
- Updated dependencies [6c99581e]
  - @ai-sdk/react@0.0.15

## 3.2.14

### Patch Changes

- Updated dependencies [9b50003d]
- Updated dependencies [1894f811]
  - @ai-sdk/react@0.0.14
  - @ai-sdk/ui-utils@0.0.9
  - @ai-sdk/solid@0.0.11
  - @ai-sdk/svelte@0.0.12
  - @ai-sdk/vue@0.0.11

## 3.2.13

### Patch Changes

- d3100b9c: feat (ai/ui): support custom fetch function in useChat, useCompletion, useAssistant, useObject
- Updated dependencies [d3100b9c]
  - @ai-sdk/ui-utils@0.0.8
  - @ai-sdk/svelte@0.0.11
  - @ai-sdk/react@0.0.13
  - @ai-sdk/solid@0.0.10
  - @ai-sdk/vue@0.0.10

## 3.2.12

### Patch Changes

- 5edc6110: feat (ai/core): add custom request header support
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
  - @ai-sdk/provider@0.0.11
  - @ai-sdk/provider-utils@1.0.0
  - @ai-sdk/react@0.0.12
  - @ai-sdk/svelte@0.0.10
  - @ai-sdk/ui-utils@0.0.7
  - @ai-sdk/solid@0.0.9
  - @ai-sdk/vue@0.0.9

## 3.2.11

### Patch Changes

- c908f741: chore (ui/solid): update solidjs useChat and useCompletion to feature parity with React
- 827ef450: feat (ai/ui): improve error handling in useAssistant
- Updated dependencies [c908f741]
- Updated dependencies [827ef450]
  - @ai-sdk/solid@0.0.8
  - @ai-sdk/svelte@0.0.9
  - @ai-sdk/react@0.0.11

## 3.2.10

### Patch Changes

- Updated dependencies [5b04204b]
- Updated dependencies [8f482903]
  - @ai-sdk/react@0.0.10

## 3.2.9

### Patch Changes

- 82d9c8de: feat (ai/ui): make event in useAssistant submitMessage optional
- Updated dependencies [82d9c8de]
- Updated dependencies [321a7d0e]
- Updated dependencies [82d9c8de]
  - @ai-sdk/svelte@0.0.8
  - @ai-sdk/react@0.0.9
  - @ai-sdk/vue@0.0.8

## 3.2.8

### Patch Changes

- 54bf4083: feat (ai/react): control request body in useChat
- Updated dependencies [54bf4083]
  - @ai-sdk/ui-utils@0.0.6
  - @ai-sdk/react@0.0.8
  - @ai-sdk/solid@0.0.7
  - @ai-sdk/svelte@0.0.7
  - @ai-sdk/vue@0.0.7

## 3.2.7

### Patch Changes

- d42b8907: feat (ui): make event in handleSubmit optional
- Updated dependencies [d42b8907]
  - @ai-sdk/svelte@0.0.6
  - @ai-sdk/react@0.0.7
  - @ai-sdk/solid@0.0.6
  - @ai-sdk/vue@0.0.6

## 3.2.6

### Patch Changes

- 74e28222: fix (ai/rsc): "could not find InternalStreamableUIClient" bug

## 3.2.5

### Patch Changes

- 4d426d0c: fix (ai): split provider and model ids correctly in the provider registry

## 3.2.4

### Patch Changes

- Updated dependencies [3cb103bc]
  - @ai-sdk/react@0.0.6

## 3.2.3

### Patch Changes

- 89b7552b: chore (ai): remove deprecation from ai/react imports, add experimental_useObject
- Updated dependencies [02f6a088]
  - @ai-sdk/provider-utils@0.0.16
  - @ai-sdk/react@0.0.5
  - @ai-sdk/svelte@0.0.5
  - @ai-sdk/ui-utils@0.0.5
  - @ai-sdk/solid@0.0.5
  - @ai-sdk/vue@0.0.5

## 3.2.2

### Patch Changes

- 0565cd72: feat (ai/core): add toJsonResponse to generateObject result.

## 3.2.1

### Patch Changes

- 008725ec: feat (ai): add textStream, toTextStreamResponse(), and pipeTextStreamToResponse() to streamObject
- 520fb2d5: feat (rsc): add streamUI onFinish callback
- Updated dependencies [008725ec]
- Updated dependencies [008725ec]
  - @ai-sdk/react@0.0.4
  - @ai-sdk/ui-utils@0.0.4
  - @ai-sdk/solid@0.0.4
  - @ai-sdk/svelte@0.0.4
  - @ai-sdk/vue@0.0.4

## 3.2.0

### Minor Changes

- 85ef6d18: chore (ai): AI SDK 3.2 release

### Patch Changes

- b965dd2d: fix (core): pass settings correctly for generateObject and streamObject

## 3.1.37

### Patch Changes

- 85712895: chore (@ai-sdk/provider-utils): move test helper to provider utils
- Updated dependencies [85712895]
- Updated dependencies [85712895]
  - @ai-sdk/provider-utils@0.0.15
  - @ai-sdk/react@0.0.3
  - @ai-sdk/svelte@0.0.3
  - @ai-sdk/ui-utils@0.0.3
  - @ai-sdk/solid@0.0.3
  - @ai-sdk/vue@0.0.3

## 3.1.36

### Patch Changes

- 4728c37f: feat (core): add text embedding model support to provider registry
- 8c49166e: chore (core): rename experimental_createModelRegistry to experimental_createProviderRegistry
- Updated dependencies [7910ae84]
  - @ai-sdk/provider-utils@0.0.14
  - @ai-sdk/react@0.0.2
  - @ai-sdk/svelte@0.0.2
  - @ai-sdk/ui-utils@0.0.2
  - @ai-sdk/solid@0.0.2
  - @ai-sdk/vue@0.0.2

## 3.1.35

### Patch Changes

- 06123501: feat (core): support https and data url strings in image parts

## 3.1.34

### Patch Changes

- d25566ac: feat (core): add cosineSimilarity helper function
- 87a5d27e: feat (core): introduce InvalidMessageRoleError.

## 3.1.33

### Patch Changes

- 6fb14b5d: chore (streams): deprecate nanoid export.
- 05536768: feat (core): add experimental model registry

## 3.1.32

### Patch Changes

- 3cabf078: fix(ai/rsc): Refactor streamable UI internal implementation

## 3.1.31

### Patch Changes

- 85f209a4: chore: extracted ui library support into separate modules
- 85f209a4: removed (streams): experimental_StreamingReactResponse was removed. Please use AI SDK RSC instead.
- Updated dependencies [85f209a4]
  - @ai-sdk/ui-utils@0.0.1
  - @ai-sdk/svelte@0.0.1
  - @ai-sdk/react@0.0.1
  - @ai-sdk/solid@0.0.1
  - @ai-sdk/vue@0.0.1

## 3.1.30

### Patch Changes

- fcf4323b: fix (core): filter out empty assistant text messages

## 3.1.29

### Patch Changes

- 28427d3e: feat (core): add streamObject onFinish callback

## 3.1.28

### Patch Changes

- 102ca22f: feat (core): add object promise to streamObject result
- Updated dependencies [102ca22f]
  - @ai-sdk/provider@0.0.10
  - @ai-sdk/provider-utils@0.0.13

## 3.1.27

### Patch Changes

- c9198d4d: feat (ui): send annotation and data fields in useChat when sendExtraMessageFields is true
- Updated dependencies [09295e2e]
- Updated dependencies [09295e2e]
- Updated dependencies [043a5de2]
  - @ai-sdk/provider@0.0.9
  - @ai-sdk/provider-utils@0.0.12

## 3.1.26

### Patch Changes

- 5ee44cae: feat (provider): langchain StringOutputParser support

## 3.1.25

### Patch Changes

- ff281126: fix(ai/rsc): Remove extra reconcilation of streamUI

## 3.1.24

### Patch Changes

- 93cae126: fix(ai/rsc): Fix unsafe {} type in application code for StreamableValue
- 08b5c509: feat (core): add tokenUsage to streamObject result

## 3.1.23

### Patch Changes

- c03cafe6: chore (core, ui): rename maxAutomaticRoundtrips to maxToolRoundtrips

## 3.1.22

### Patch Changes

- 14bb8694: chore (ui): move maxAutomaticRoundtrips and addToolResult out of experimental

## 3.1.21

### Patch Changes

- 213f2411: fix (core,streams): support ResponseInit variants
- 09698bca: chore (streams): deprecate streaming helpers that have a provider replacement

## 3.1.20

### Patch Changes

- 0e1da476: feat (core): add maxAutomaticRoundtrips setting to generateText

## 3.1.19

### Patch Changes

- 9882d24b: fix (ui/svelte): send data to server
- 131bbd3e: fix (ui): remove console.log statements

## 3.1.18

### Patch Changes

- f9dee8ac: fix(ai/rsc): Fix types for createStreamableValue and createStreamableUI
- 1c0ebf8e: feat (core): add responseMessages to generateText result

## 3.1.17

### Patch Changes

- 92b993b7: ai/rsc: improve getAIState and getMutableAIState types
- 7de628e9: chore (ui): deprecate old function/tool call handling
- 7de628e9: feat (ui): add onToolCall handler to useChat

## 3.1.16

### Patch Changes

- f39c0dd2: feat (core, rsc): add toolChoice setting
- Updated dependencies [f39c0dd2]
  - @ai-sdk/provider@0.0.8
  - @ai-sdk/provider-utils@0.0.11

## 3.1.15

### Patch Changes

- 8e780288: feat (ai/core): add onFinish callback to streamText
- 8e780288: feat (ai/core): add text, toolCalls, and toolResults promises to StreamTextResult (matching the generateText result API with async methods)
- Updated dependencies [8e780288]
  - @ai-sdk/provider@0.0.7
  - @ai-sdk/provider-utils@0.0.10

## 3.1.14

### Patch Changes

- 6109c6a: feat (ai/react): add experimental_maxAutomaticRoundtrips to useChat

## 3.1.13

### Patch Changes

- 60117c9: dependencies (ai/ui): add React 18.3 and 19 support (peer dependency)
- Updated dependencies [6a50ac4]
- Updated dependencies [6a50ac4]
  - @ai-sdk/provider@0.0.6
  - @ai-sdk/provider-utils@0.0.9

## 3.1.12

### Patch Changes

- ae05fb7: feat (ai/streams): add StreamData support to streamToResponse

## 3.1.11

### Patch Changes

- a085d42: fix (ai/ui): decouple StreamData chunks from LLM stream

## 3.1.10

### Patch Changes

- 3a21030: feat (ai/core): add embedMany function

## 3.1.9

### Patch Changes

- 18a9655: feat (ai/svelte): add useAssistant

## 3.1.8

### Patch Changes

- 0f6bc4e: feat (ai/core): add embed function
- Updated dependencies [0f6bc4e]
  - @ai-sdk/provider@0.0.5
  - @ai-sdk/provider-utils@0.0.8

## 3.1.7

### Patch Changes

- f617b97: feat (ai): support client/server tool calls with useChat and streamText

## 3.1.6

### Patch Changes

- 2e78acb: Deprecate StreamingReactResponse (use AI SDK RSC instead).
- 8439884: ai/rsc: make RSC streamable utils chainable
- 325ca55: feat (ai/core): improve image content part error message
- Updated dependencies [325ca55]
  - @ai-sdk/provider@0.0.4
  - @ai-sdk/provider-utils@0.0.7

## 3.1.5

### Patch Changes

- 5b01c13: feat (ai/core): add system message support in messages list

## 3.1.4

### Patch Changes

- ceb44bc: feat (ai/ui): add stop() helper to useAssistant (important: AssistantResponse now requires OpenAI SDK 4.42+)
- 37c9d4c: feat (ai/streams): add LangChainAdapter.toAIStream()

## 3.1.3

### Patch Changes

- 970a099: fix (ai/core): streamObject fixes partial json with empty objects correctly
- 1ac2390: feat (ai/core): add usage and finishReason to streamText result.
- Updated dependencies [276f22b]
  - @ai-sdk/provider-utils@0.0.6

## 3.1.2

### Patch Changes

- d1b1880: fix (ai/core): allow reading streams in streamText result multiple times

## 3.1.1

### Patch Changes

- 0f77132: ai/rsc: remove experimental\_ from streamUI

## 3.1.0

### Minor Changes

- 73356a9: Move AI Core functions out of experimental (streamText, generateText, streamObject, generateObject).

## 3.0.35

### Patch Changes

- 41d5736: ai/core: re-expose language model types.
- b4c68ec: ai/rsc: ReadableStream as provider for createStreamableValue; add .append() method
- Updated dependencies [41d5736]
  - @ai-sdk/provider@0.0.3
  - @ai-sdk/provider-utils@0.0.5

## 3.0.34

### Patch Changes

- b9a831e: ai/rsc: add experimental_streamUI()

## 3.0.33

### Patch Changes

- 56ef84a: ai/core: fix abort handling in transformation stream
- Updated dependencies [56ef84a]
  - @ai-sdk/provider-utils@0.0.4

## 3.0.32

### Patch Changes

- 0e0d2af: ai/core: add pipeTextStreamToResponse helper to streamText.

## 3.0.31

### Patch Changes

- 74c63b1: ai/core: add toAIStreamResponse() helper to streamText.

## 3.0.30

### Patch Changes

- e7e5898: use-assistant: fix missing message content

## 3.0.29

### Patch Changes

- 22a737e: Fix: mark useAssistant as in progress for append/submitMessage.

## 3.0.28

### Patch Changes

- d6431ae: ai/core: add logprobs support (thanks @SamStenner for the contribution)
- 25f3350: ai/core: add support for getting raw response headers.
- Updated dependencies [d6431ae]
- Updated dependencies [25f3350]
  - @ai-sdk/provider@0.0.2
  - @ai-sdk/provider-utils@0.0.3

## 3.0.27

### Patch Changes

- eb150a6: ai/core: remove scaling of setting values (breaking change). If you were using the temperature, frequency penalty, or presence penalty settings, you need to update the providers and adjust the setting values.
- Updated dependencies [eb150a6]
  - @ai-sdk/provider-utils@0.0.2
  - @ai-sdk/provider@0.0.1

## 3.0.26

### Patch Changes

- f90f6a1: ai/core: add pipeAIStreamToResponse() to streamText result.

## 3.0.25

### Patch Changes

- 1e84d6d: Fix: remove mistral lib type dependency.
- 9c2a049: Add append() helper to useAssistant.

## 3.0.24

### Patch Changes

- e94fb32: feat(ai/rsc): Make `onSetAIState` and `onGetUIState` stable

## 3.0.23

### Patch Changes

- 66b5892: Add streamMode parameter to useChat and useCompletion.
- Updated dependencies [7b8791d]
  - @ai-sdk/provider-utils@0.0.1

## 3.0.22

### Patch Changes

- d544886: Breaking change: extract experimental AI core provider packages. They can now be imported with e.g. import { openai } from '@ai-sdk/openai' after adding them to a project.
- ea6b0e1: Expose formatStreamPart, parseStreamPart, and readDataStream helpers.

## 3.0.21

### Patch Changes

- 87d3db5: Extracted @ai-sdk/provider package
- 8c40f8c: ai/core: Fix openai provider streamObject for gpt-4-turbo
- 5cd29bd: ai/core: add toTextStreamResponse() method to streamText result

## 3.0.20

### Patch Changes

- f42bbb5: Remove experimental from useAssistant and AssistantResponse.
- 149fe26: Deprecate <Tokens/>
- 2eb4b55: Remove experimental\_ prefix from StreamData.
- e45fa96: Add stream support for Bedrock/Cohere.
- a6b2500: Deprecated the `experimental_streamData: true` setting from AIStreamCallbacksAndOptions. You can delete occurrences in your code. The stream data protocol is now used by default.

## 3.0.19

### Patch Changes

- 4f4c7f5: ai/core: Anthropic tool call support

## 3.0.18

### Patch Changes

- 63d587e: Add Anthropic provider for ai/core functions (no tool calling).
- 63d587e: Add automatic mime type detection for images in ai/core prompts.

## 3.0.17

### Patch Changes

- 2b991c4: Add Google Generative AI provider for ai/core functions.

## 3.0.16

### Patch Changes

- a54ea77: feat(ai/rsc): add `useStreamableValue`

## 3.0.15

### Patch Changes

- 4aed2a5: Add JSDoc comments for ai/core functions.
- cf8d12f: Export experimental language model specification under `ai/spec`.

## 3.0.14

### Patch Changes

- 8088de8: fix(ai/rsc): improve typings for `StreamableValue`
- 20007b9: feat(ai/rsc): support string diff and patch in streamable value
- 6039460: Support Bedrock Anthropic Stream for Messages API.
- e83bfe3: Added experimental ai/core functions (streamText, generateText, streamObject, generateObject). Add OpenAI and Mistral language model providers.

## 3.0.13

### Patch Changes

- 026d061: Expose setMessages in useAssistant hook
- 42209be: AssistantResponse: specify forwardStream return type.

## 3.0.12

### Patch Changes

- b99b008: fix(ai/rsc): avoid appending boundary if the same reference was passed

## 3.0.11

### Patch Changes

- ce009e2: Added OpenAI assistants streaming.
- 3f9bf3e: Updates types to OpenAI SDK 4.29.0

## 3.0.10

### Patch Changes

- 33d261a: fix(ai/rsc): Fix .append() behavior

## 3.0.9

### Patch Changes

- 81ca3d6: fix(ai/rsc): improve .done() argument type

## 3.0.8

### Patch Changes

- a94aab2: ai/rsc: optimize streamable value stream size

## 3.0.7

### Patch Changes

- 9a9ae73: feat(ai/rsc): readStreamableValue

## 3.0.6

### Patch Changes

- 1355ad0: Fix: experimental_onToolCall is called with parsed tool args
- 9348f06: ai/rsc: improve dev error and warnings by trying to detect hanging streams
- 8be9404: fix type resolution

## 3.0.5

### Patch Changes

- a973f1e: Support Anthropic SDK v0.15.0
- e25f3ca: type improvements

## 3.0.4

### Patch Changes

- 7962862: fix `useActions` type inference
- aab5324: Revert "fix(render): parse the args based on the zod schema"
- fe55612: Bump OpenAI dependency to 4.28.4; fix type error in render

## 3.0.3

### Patch Changes

- 4d816ca: fix(render): parse the args based on the zod schema
- d158a47: fix potential race conditions

## 3.0.2

### Patch Changes

- 73bd06e: fix(useActions): return typed object

## 3.0.1

### Patch Changes

- ac20a25: ai/rsc: fix text response and async generator
- b88778f: Added onText callback for text tokens.

## 3.0.0

### Major Changes

- 51054a9: add ai/rsc

## 2.2.37

### Patch Changes

- a6b5764: Add support for Mistral's JavaScript SDK

## 2.2.36

### Patch Changes

- 141f0ce: Fix: onFinal callback is invoked with text from onToolCall when onToolCall returns string

## 2.2.35

### Patch Changes

- b717dad: Adding Inkeep as a stream provider

## 2.2.34

### Patch Changes

- 2c8ffdb: cohere-stream: support AsyncIterable
- ed1e278: Message annotations handling for all Message types

## 2.2.33

### Patch Changes

- 8542ae7: react/use-assistant: add onError handler
- 97039ff: OpenAIStream: Add support for the Azure OpenAI client library

## 2.2.32

### Patch Changes

- 7851fa0: StreamData: add `annotations` and `appendMessageAnnotation` support

## 2.2.31

### Patch Changes

- 9b89c4d: react/use-assistant: Expose setInput
- 75751c9: ai/react: Add experimental_onToolCall to useChat.

## 2.2.30

### Patch Changes

- ac503e0: ai/solid: add chat request options to useChat
- b78a73e: Add GoogleGenerativeAIStream for Gemini support
- 5220336: ai/svelte: Add experimental_onToolCall to useChat.
- ef99062: Add support for the Anthropic message API
- 5220336: Add experimental_onToolCall to OpenAIStream.
- ac503e0: ai/vue: add chat request options to useChat

## 2.2.29

### Patch Changes

- 5a9ae2e: ai/prompt: add `experimental_buildOpenAIMessages` to validate and cast AI SDK messages to OpenAI messages

## 2.2.28

### Patch Changes

- 07a679c: Add data message support to useAssistant & assistantResponse.
- fbae595: ai/react: `api` functions are no longer used as a cache key in `useChat`

## 2.2.27

### Patch Changes

- 0fd1205: ai/vue: Add complex response parsing and StreamData support to useCompletion
- a7dc746: experimental_useAssistant: Expose extra fetch options
- 3dcf01e: ai/react Add data support to useCompletion
- 0c3b338: ai/svelte: Add complex response parsing and StreamData support to useCompletion
- 8284777: ai/solid: Add complex response parsing and StreamData support to useCompletion

## 2.2.26

### Patch Changes

- df1ad33: ai/vue: Add complex response parsing and StreamData support to useChat
- 3ff8a56: Add `generateId` to use-chat params to allow overriding message ID generation
- 6c2a49c: ai/react experimental_useAssistant() submit can be called without an event
- 8b4f7d1: ai/react: Add complex response parsing and StreamData support to useCompletion

## 2.2.25

### Patch Changes

- 1e61c69: chore: specify the minimum react version to 18
- 6aec2d2: Expose threadId in useAssistant
- c2369df: Add AWS Bedrock support
- 223fde3: ai/svelte: Add complex response parsing and StreamData support to useChat

## 2.2.24

### Patch Changes

- 69ca8f5: ai/react: add experimental_useAssistant hook and experimental_AssistantResponse
- 3e2299e: experimental_StreamData/StreamingReactResponse: optimize parsing, improve types
- 70bd2ac: ai/solid: add experimental_StreamData support to useChat

## 2.2.23

### Patch Changes

- 5a04321: add StreamData support to StreamingReactResponse, add client-side data API to react/use-chat

## 2.2.22

### Patch Changes

- 4529831: ai/react: Do not store initialMessages in useState
- db5378c: experimental_StreamData: fix data type to be JSONValue

## 2.2.21

### Patch Changes

- 2c8d4bd: Support openai@4.16.0 and later

## 2.2.20

### Patch Changes

- 424d5ee: experimental_StreamData: fix trailing newline parsing bug in decoder
- c364c6a: cohere: fix closing cohere stream, avoids response from hanging

## 2.2.19

### Patch Changes

- 699552d: add experimental_StreamingReactResponse

## 2.2.18

### Patch Changes

- 0bd27f6: react/use-chat: allow client-side handling of function call without following response

## 2.2.17

### Patch Changes

- 5ed581d: Use interface instead of type for Message to allow declaration merging
- 9adec1e: vue and solid: fix including `function_call` and `name` fields in subsequent requests

## 2.2.16

### Patch Changes

- e569688: Fix for #637, resync interfaces

## 2.2.15

### Patch Changes

- c5d1857: fix: return complete response in onFinish when onCompletion isn't passed
- c5d1857: replicate-stream: fix types for replicate@0.20.0+

## 2.2.14

### Patch Changes

- 6229d6b: openai: fix OpenAIStream types with openai@4.11+

## 2.2.13

### Patch Changes

- a4a997f: all providers: reset error message on (re)submission

## 2.2.12

### Patch Changes

- cb181b4: ai/vue: wrap body with unref to support reactivity

## 2.2.11

### Patch Changes

- 2470658: ai/react: fix: handle partial chunks in react getStreamedResponse when using experimental_StreamData

## 2.2.10

### Patch Changes

- 8a2cbaf: vue/use-completion: fix: don't send network request for loading state"
- bbf4403: langchain-stream: return langchain `writer` from LangChainStream

## 2.2.9

### Patch Changes

- 3fc2b32: ai/vue: fix: make body parameter reactive

## 2.2.8

### Patch Changes

- 26bf998: ai/react: make reload/complete/append functions stable via useCallback

## 2.2.7

### Patch Changes

- 2f97630: react/use-chat: fix aborting clientside function calls too early
- 1157340: fix: infinite loop for experimental stream data (#484)

## 2.2.6

### Patch Changes

- e5bf68d: react/use-chat: fix experimental functions returning proper function messages

  Closes #478

## 2.2.5

### Patch Changes

- e5bf68d: react/use-chat: fix experimental functions returning proper function messages

  Closes #478

## 2.2.4

### Patch Changes

- 7b389a7: fix: improve safety for type check in openai-stream

## 2.2.3

### Patch Changes

- 867a3f9: Fix client-side function calling (#467, #469)

  add Completion type from the `openai` SDK to openai-stream (#472)

## 2.2.2

### Patch Changes

- 84e0cc8: Add experimental_StreamData and new opt-in wire protocol to enable streaming additional data. See https://github.com/vercel/ai/pull/425.

  Changes `onCompletion` back to run every completion, including recursive function calls. Adds an `onFinish` callback that runs once everything has streamed.

  If you're using experimental function handlers on the server _and_ caching via `onCompletion`,
  you may want to adjust your caching code to account for recursive calls so the same key isn't used.

  ```
  let depth = 0

  const stream = OpenAIStream(response, {
      async onCompletion(completion) {
        depth++
        await kv.set(key + '_' + depth, completion)
        await kv.expire(key + '_' + depth, 60 * 60)
      }
    })
  ```

## 2.2.1

### Patch Changes

- 04084a8: openai-stream: fix experimental_onFunctionCall types for OpenAI SDK v4

## 2.2.0

### Minor Changes

- dca1ed9: Update packages and examples to use OpenAI SDK v4

## 2.1.34

### Patch Changes

- c2917d3: Add support for the Anthropic SDK, newer Anthropic API versions, and improve Anthropic error handling

## 2.1.33

### Patch Changes

- 4ef8015: Prevent `isLoading` in vue integration from triggering extraneous network requests

## 2.1.32

### Patch Changes

- 5f91427: ai/svelte: fix isLoading return value

## 2.1.31

### Patch Changes

- ab2b973: fix pnpm-lock.yaml

## 2.1.30

### Patch Changes

- 4df2a49: Fix termination of ReplicateStream by removing the terminating `{}`from output

## 2.1.29

### Patch Changes

- 3929a41: Add ReplicateStream helper

## 2.1.28

### Patch Changes

- 9012e17: react/svelte/vue: fix making unnecessary SWR request to API endpoint

## 2.1.27

### Patch Changes

- 3d29799: React/Svelte/Vue: keep isLoading in sync between hooks with the same ID.

  React: don't throw error when submitting

## 2.1.26

### Patch Changes

- f50d9ef: Add experimental_buildLlama2Prompt helper for Hugging Face

## 2.1.25

### Patch Changes

- 877c16f: ai/react: don't throw error if onError is passed

## 2.1.24

### Patch Changes

- f3f5866: Adds SolidJS support and SolidStart example

## 2.1.23

### Patch Changes

- 0ebc2f0: streams/openai-stream: don't call onStart/onCompletion when recursing

## 2.1.22

### Patch Changes

- 9320e95: Add (experimental) prompt construction helpers for StarChat and OpenAssistant
- e3a7ec8: Support <|end|> token for StarChat beta in huggingface-stream

## 2.1.21

### Patch Changes

- 561a49a: Providing a function to `function_call` request parameter of the OpenAI Chat Completions API no longer breaks OpenAI function stream parsing.

## 2.1.20

### Patch Changes

- e361114: OpenAI functions: allow returning string in callback

## 2.1.19

### Patch Changes

- e4281ca: Add experimental server-side OpenAI function handling

## 2.1.18

### Patch Changes

- 6648b21: Add experimental client side OpenAI function calling to Svelte bindings
- e5b983f: feat(streams): add http error handling for openai

## 2.1.17

### Patch Changes

- 3ed65bf: Remove dependency on node crypto API

## 2.1.16

### Patch Changes

- 8bfb43d: Fix svelte peer dependency version

## 2.1.15

### Patch Changes

- 4a2b978: Update cohere stream and add docs

## 2.1.14

### Patch Changes

- 3164adb: Fix regression with generated ids

## 2.1.13

### Patch Changes

- fd82961: Use rfc4122 IDs when generating chat/completion IDs

## 2.1.12

### Patch Changes

- b7b93e5: Add <Tokens> RSC to ai/react

## 2.1.11

### Patch Changes

- 8bf637a: Fix langchain handlers so that they now are correctly invoked and update examples and docs to show correct usage (passing the handlers to `llm.call` and not the model itself).

## 2.1.10

### Patch Changes

- a7b3d0e: Experimental support for OpenAI function calling

## 2.1.9

### Patch Changes

- 9cdf968: core/react: add Tokens react server component

## 2.1.8

### Patch Changes

- 44d9879: Support extra request options in chat and completion hooks

## 2.1.7

### Patch Changes

- bde3898: Allow an async onResponse callback in useChat/useCompletion

## 2.1.6

### Patch Changes

- 23f0899: Set stream: true when decoding streamed chunks

## 2.1.5

### Patch Changes

- 89938b0: Provider direct callback handlers in LangChain now that `CallbackManager` is deprecated.

## 2.1.4

### Patch Changes

- c16d650: Improve type saftey for AIStream. Added JSDoc comments.

## 2.1.3

### Patch Changes

- a9591fe: Add `createdAt` on `user` input message in `useChat` (it was already present in `assistant` messages)

## 2.1.2

### Patch Changes

- f37d4ec: fix bundling

## 2.1.1

### Patch Changes

- 9fdb51a: fix: add better typing for store within svelte implementation (#104)

## 2.1.0

### Minor Changes

- 71f9c51: This adds Vue support for `ai` via the `ai/vue` subpath export. Vue composables `useChat` and `useCompletion` are provided.

### Patch Changes

- ad54c79: add tests

## 2.0.1

### Patch Changes

- be90740: - Switches `LangChainStream` helper callback `handler` to return use `handleChainEnd` instead of `handleLLMEnd` so as to work with sequential chains

## 2.0.0

### Major Changes

- 095de43: New package name!

## 0.0.14

### Patch Changes

- c6586a2: Add onError callback, include response text in error if response is not okay

## 0.0.13

### Patch Changes

- c1f4a91: Throw error when provided AI response isn't valid

## 0.0.12

### Patch Changes

- ea4e66a: improve API types

## 0.0.11

### Patch Changes

- a6bc35c: fix package exports for react and svelte subpackages

## 0.0.10

### Patch Changes

- 56f9537: add svelte apis

## 0.0.9

### Patch Changes

- 78477d3: - Create `/react` sub-package.
  - Create `import { useChat, useCompletion } from 'ai/react'` and mark React as an optional peer dependency so we can add more framework support in the future.
  - Also renamed `set` to `setMessages` and `setCompletion` to unify the API naming as we have `setInput` too.
  - Added an `sendExtraMessageFields` field to `useChat` that defaults to `false`, to prevent OpenAI errors when `id` is not filtered out.
- c4c1be3: useCompletion.handleSubmit does not clear the input anymore
- 7de2185: create /react export

## 0.0.8

### Patch Changes

- fc83e95: Implement new start-of-stream newline trimming
- 2c6fa04: Optimize callbacks TransformStream to be more memory efficient when `onCompletion` is not specified

## 0.0.7

### Patch Changes

- fdfef52: - Splits the `EventSource` parser into a reusable helper
  - Uses a `TransformStream` for this, so the stream respects back-pressure
  - Splits the "forking" stream for callbacks into a reusable helper
  - Changes the signature for `customParser` to avoid Stringify -> Encode -> Decode -> Parse round trip
  - Uses ?.() optional call syntax for callbacks
  - Uses string.includes to perform newline checking
  - Handles the `null` `res.body` case
  - Fixes Anthropic's streaming responses
    - Anthropic returns cumulative responses, not deltas like OpenAI
    - https://github.com/hwchase17/langchain/blob/3af36943/langchain/llms/anthropic.py#L190-L193

## 0.0.6

### Patch Changes

- d70a9e7: Add streamToResponse
- 47b85b2: Improve abortController and callbacks of `useChat`
- 6f7b43a: Export `UseCompletionHelpers` as a TypeScript type alias

## 0.0.5

### Patch Changes

- 4405a8a: fix duplicated `'use client'` directives

## 0.0.4

### Patch Changes

- b869104: Added `LangChainStream`, `useCompletion`, and `useChat`

## 0.0.3

### Patch Changes

- 677d222: add useCompletion

## 0.0.2

### Patch Changes

- af400e2: Fix release script

## 0.0.1

### Patch Changes

- b7e227d: Add `useChat` hook

## 0.0.2

### Patch Changes

- 9a8a845: Testing out release
