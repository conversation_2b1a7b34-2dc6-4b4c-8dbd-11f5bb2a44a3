{"version": 3, "sources": ["../src/combine-headers.ts", "../src/convert-async-iterator-to-readable-stream.ts", "../src/delay.ts", "../src/extract-response-headers.ts", "../src/generate-id.ts", "../src/get-error-message.ts", "../src/get-from-api.ts", "../src/handle-fetch-error.ts", "../src/is-abort-error.ts", "../src/remove-undefined-entries.ts", "../src/is-url-supported.ts", "../src/load-api-key.ts", "../src/load-optional-setting.ts", "../src/load-setting.ts", "../src/parse-json.ts", "../src/secure-json-parse.ts", "../src/validate-types.ts", "../src/validator.ts", "../src/parse-json-event-stream.ts", "../src/parse-provider-options.ts", "../src/post-to-api.ts", "../src/types/tool.ts", "../src/provider-defined-tool-factory.ts", "../src/resolve.ts", "../src/response-handler.ts", "../src/zod-schema.ts", "../src/schema.ts", "../src/uint8-utils.ts", "../src/without-trailing-slash.ts", "../src/index.ts"], "sourcesContent": ["export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncIterator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncIterator.\n * @param { <T>} iterator - The AsyncIterator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncIterator.\n */\nexport function convertAsyncIteratorToReadableStream<T>(\n  iterator: AsyncIterator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\n * Creates a Promise that resolves after a specified delay\n * @param delayInMs - The delay duration in milliseconds. If null or undefined, resolves immediately.\n * @param signal - Optional AbortSignal to cancel the delay\n * @returns A Promise that resolves after the specified delay\n * @throws {DOMException} When the signal is aborted\n */\nexport async function delay(\n  delayInMs?: number | null,\n  options?: {\n    abortSignal?: AbortSignal;\n  },\n): Promise<void> {\n  if (delayInMs == null) {\n    return Promise.resolve();\n  }\n\n  const signal = options?.abortSignal;\n\n  return new Promise<void>((resolve, reject) => {\n    if (signal?.aborted) {\n      reject(createAbortError());\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      cleanup();\n      resolve();\n    }, delayInMs);\n\n    const cleanup = () => {\n      clearTimeout(timeoutId);\n      signal?.removeEventListener('abort', onAbort);\n    };\n\n    const onAbort = () => {\n      cleanup();\n      reject(createAbortError());\n    };\n\n    signal?.addEventListener('abort', onAbort);\n  });\n}\n\nfunction createAbortError(): DOMException {\n  return new DOMException('Delay was aborted', 'AbortError');\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(response: Response) {\n  return Object.fromEntries<string>([...response.headers]);\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\n\n/**\nCreates an ID generator.\nThe total length of the ID is the sum of the prefix, separator, and random part length.\nNot cryptographically secure.\n\n@param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n@param prefix - The prefix of the ID to generate. Optional.\n@param separator - The separator between the prefix and the random part of the ID. Default: '-'.\n@param size - The size of the random part of the ID to generate. Default: 16.\n */\nexport const createIdGenerator = ({\n  prefix,\n  size = 16,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  separator = '-',\n}: {\n  prefix?: string;\n  separator?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): IdGenerator => {\n  const generator = () => {\n    const alphabetLength = alphabet.length;\n    const chars = new Array(size);\n    for (let i = 0; i < size; i++) {\n      chars[i] = alphabet[(Math.random() * alphabetLength) | 0];\n    }\n    return chars.join('');\n  };\n\n  if (prefix == null) {\n    return generator;\n  }\n\n  // check that the prefix is not part of the alphabet (otherwise prefix checking can fail randomly)\n  if (alphabet.includes(separator)) {\n    throw new InvalidArgumentError({\n      argument: 'separator',\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`,\n    });\n  }\n\n  return () => `${prefix}${separator}${generator()}`;\n};\n\n/**\nA function that generates an ID.\n */\nexport type IdGenerator = () => string;\n\n/**\nGenerates a 16-character random string to use for IDs.\nNot cryptographically secure.\n */\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { handleFetchError } from './handle-fetch-error';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const getFromApi = async <T>({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {},\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {},\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {},\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {},\n      });\n    }\n  } catch (error) {\n    throw handleFetchError({ error, url, requestBodyValues: {} });\n  }\n};\n", "import { APICallError } from '@ai-sdk/provider';\nimport { isAbortError } from './is-abort-error';\n\nconst FETCH_FAILED_ERROR_MESSAGES = ['fetch failed', 'failed to fetch'];\n\nexport function handleFetchError({\n  error,\n  url,\n  requestBodyValues,\n}: {\n  error: unknown;\n  url: string;\n  requestBodyValues: unknown;\n}) {\n  if (isAbortError(error)) {\n    return error;\n  }\n\n  // unwrap original error when fetch failed (for easier debugging):\n  if (\n    error instanceof TypeError &&\n    FETCH_FAILED_ERROR_MESSAGES.includes(error.message.toLowerCase())\n  ) {\n    const cause = (error as any).cause;\n\n    if (cause != null) {\n      // Failed to connect to server:\n      return new APICallError({\n        message: `Cannot connect to API: ${cause.message}`,\n        cause,\n        url,\n        requestBodyValues,\n        isRetryable: true, // retry when network error\n      });\n    }\n  }\n\n  return error;\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    (error instanceof Error || error instanceof DOMException) &&\n    (error.name === 'AbortError' ||\n      error.name === 'ResponseAborted' || // Next.js\n      error.name === 'TimeoutError')\n  );\n}\n", "/**\n * Removes entries from a record where the value is null or undefined.\n * @param record - The input object whose entries may be null or undefined.\n * @returns A new object containing only entries with non-null and non-undefined values.\n */\nexport function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "/**\n * Checks if the given URL is supported natively by the model.\n *\n * @param mediaType - The media type of the URL. Case-sensitive.\n * @param url - The URL to check.\n * @param supportedUrls - A record where keys are case-sensitive media types (or '*')\n *                        and values are arrays of RegExp patterns for URLs.\n *\n * @returns `true` if the URL matches a pattern under the specific media type\n *          or the wildcard '*', `false` otherwise.\n */\nexport function isUrlSupported({\n  mediaType,\n  url,\n  supportedUrls,\n}: {\n  mediaType: string;\n  url: string;\n  supportedUrls: Record<string, RegExp[]>;\n}): boolean {\n  // standardize media type and url to lower case\n  url = url.toLowerCase();\n  mediaType = mediaType.toLowerCase();\n\n  return (\n    Object.entries(supportedUrls)\n      // standardize supported url map into lowercase prefixes:\n      .map(([key, value]) => {\n        const mediaType = key.toLowerCase();\n        return mediaType === '*' || mediaType === '*/*'\n          ? { mediaTypePrefix: '', regexes: value }\n          : { mediaTypePrefix: mediaType.replace(/\\*/, ''), regexes: value };\n      })\n      // gather all regexp pattern from matched media type prefixes:\n      .filter(({ mediaTypePrefix }) => mediaType.startsWith(mediaTypePrefix))\n      .flatMap(({ regexes }) => regexes)\n      // check if any pattern matches the url:\n      .some(pattern => pattern.test(url))\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import {\n  J<PERSON><PERSON>arseError,\n  <PERSON>SO<PERSON><PERSON><PERSON><PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport { secureJsonParse } from './secure-json-parse';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport async function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): Promise<JSONValue>;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {Promise<T>} - The parsed object.\n */\nexport async function parseJSON<T>(options: {\n  text: string;\n  schema: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<T>;\nexport async function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<T> {\n  try {\n    const value = secureJsonParse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes<T>({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isInstance(error) ||\n      TypeValidationError.isInstance(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T; rawValue: unknown }\n  | {\n      success: false;\n      error: JSONParseError | TypeValidationError;\n      rawValue: unknown;\n    };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {Promise<object>} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport async function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): Promise<ParseResult<JSONValue>>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport async function safeParseJSON<T>(options: {\n  text: string;\n  schema: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<ParseResult<T>>;\nexport async function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<ParseResult<T>> {\n  try {\n    const value = secureJsonParse(text);\n\n    if (schema == null) {\n      return { success: true, value: value as T, rawValue: value };\n    }\n\n    return await safeValidateTypes<T>({ value, schema });\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isInstance(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n      rawValue: undefined,\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    secureJsonParse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n", "// Licensed under BSD-3-Clause (this file only)\n// Code adapted from https://github.com/fastify/secure-json-parse/blob/783fcb1b5434709466759847cec974381939673a/index.js\n//\n// Copyright (c) Vercel, Inc. (https://vercel.com)\n// Copyright (c) 2019 The Fastify Team\n// Copyright (c) 2019, Sideway Inc, and project contributors\n// All rights reserved.\n//\n// The complete list of contributors can be found at:\n// - https://github.com/hapijs/bourne/graphs/contributors\n// - https://github.com/fastify/secure-json-parse/graphs/contributors\n// - https://github.com/vercel/ai/commits/main/packages/provider-utils/src/secure-parse-json.ts\n//\n// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n//\n// 1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n//\n// 2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n//\n// 3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nconst suspectProtoRx = /\"__proto__\"\\s*:/;\nconst suspectConstructorRx = /\"constructor\"\\s*:/;\n\nfunction _parse(text: string) {\n  // Parse normally\n  const obj = JSON.parse(text);\n\n  // Ignore null and non-objects\n  if (obj === null || typeof obj !== 'object') {\n    return obj;\n  }\n\n  if (\n    suspectProtoRx.test(text) === false &&\n    suspectConstructorRx.test(text) === false\n  ) {\n    return obj;\n  }\n\n  // Scan result for proto keys\n  return filter(obj);\n}\n\nfunction filter(obj: any) {\n  let next = [obj];\n\n  while (next.length) {\n    const nodes = next;\n    next = [];\n\n    for (const node of nodes) {\n      if (Object.prototype.hasOwnProperty.call(node, '__proto__')) {\n        throw new SyntaxError('Object contains forbidden prototype property');\n      }\n\n      if (\n        Object.prototype.hasOwnProperty.call(node, 'constructor') &&\n        Object.prototype.hasOwnProperty.call(node.constructor, 'prototype')\n      ) {\n        throw new SyntaxError('Object contains forbidden prototype property');\n      }\n\n      for (const key in node) {\n        const value = node[key];\n        if (value && typeof value === 'object') {\n          next.push(value);\n        }\n      }\n    }\n  }\n  return obj;\n}\n\nexport function secureJsonParse(text: string) {\n  // Performance optimization, see https://github.com/fastify/secure-json-parse/pull/90\n  const { stackTraceLimit } = Error;\n  Error.stackTraceLimit = 0;\n  try {\n    return _parse(text);\n  } finally {\n    Error.stackTraceLimit = stackTraceLimit;\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport type { StandardSchemaV1 } from '@standard-schema/spec';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {Promise<T>} - The typed object.\n */\nexport async function validateTypes<OBJECT>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: StandardSchemaV1<unknown, OBJECT> | Validator<OBJECT>;\n}): Promise<OBJECT> {\n  const result = await safeValidateTypes({ value, schema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport async function safeValidateTypes<OBJECT>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: StandardSchemaV1<unknown, OBJECT> | Validator<OBJECT>;\n}): Promise<\n  | {\n      success: true;\n      value: OBJECT;\n      rawValue: unknown;\n    }\n  | {\n      success: false;\n      error: TypeValidationError;\n      rawValue: unknown;\n    }\n> {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as OBJECT, rawValue: value };\n    }\n\n    const result = await validator.validate(value);\n\n    if (result.success) {\n      return { success: true, value: result.value, rawValue: value };\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n      rawValue: value,\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n      rawValue: value,\n    };\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (\n    value: unknown,\n  ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?:\n    | undefined\n    | ((\n        value: unknown,\n      ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | StandardSchemaV1<unknown, OBJECT>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : standardSchemaValidator(value);\n}\n\nexport function standardSchemaValidator<OBJECT>(\n  standardSchema: StandardSchemaV1<unknown, OBJECT>,\n): Validator<OBJECT> {\n  return validator(async value => {\n    const result = await standardSchema['~standard'].validate(value);\n\n    return result.issues == null\n      ? { success: true, value: result.value }\n      : {\n          success: false,\n          error: new TypeValidationError({\n            value,\n            cause: result.issues,\n          }),\n        };\n  });\n}\n", "import {\n  EventSourceMessage,\n  EventSourceParserStream,\n} from 'eventsource-parser/stream';\nimport { ZodType } from 'zod/v4';\nimport { ParseResult, safeParseJSON } from './parse-json';\n\n/**\n * Parses a JSON event stream into a stream of parsed JSON objects.\n */\nexport function parseJsonEventStream<T>({\n  stream,\n  schema,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  schema: ZodType<T>;\n}): ReadableStream<ParseResult<T>> {\n  return stream\n    .pipeThrough(new TextDecoderStream())\n    .pipeThrough(new EventSourceParserStream())\n    .pipeThrough(\n      new TransformStream<EventSourceMessage, ParseResult<T>>({\n        async transform({ data }, controller) {\n          // ignore the 'DONE' event that e.g. OpenAI sends:\n          if (data === '[DONE]') {\n            return;\n          }\n\n          controller.enqueue(await safeParseJSON({ text: data, schema }));\n        },\n      }),\n    );\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from './validate-types';\nimport { z } from 'zod/v4';\n\nexport async function parseProviderOptions<T>({\n  provider,\n  providerOptions,\n  schema,\n}: {\n  provider: string;\n  providerOptions: Record<string, unknown> | undefined;\n  schema: z.core.$ZodType<T, any>;\n}): Promise<T | undefined> {\n  if (providerOptions?.[provider] == null) {\n    return undefined;\n  }\n\n  const parsedProviderOptions = await safeValidateTypes<T | undefined>({\n    value: providerOptions[provider],\n    schema,\n  });\n\n  if (!parsedProviderOptions.success) {\n    throw new InvalidArgumentError({\n      argument: 'providerOptions',\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error,\n    });\n  }\n\n  return parsedProviderOptions.value;\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { handleFetchError } from './handle-fetch-error';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postFormDataToApi = async <T>({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  formData: FormData;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers,\n    body: {\n      content: formData,\n      values: Object.fromEntries((formData as any).entries()),\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    throw handleFetchError({ error, url, requestBodyValues: body.values });\n  }\n};\n", "import { JSONValue, LanguageModelV2ToolResultPart } from '@ai-sdk/provider';\nimport { FlexibleSchema } from '../schema';\nimport { ModelMessage } from './model-message';\nimport { ProviderOptions } from './provider-options';\n\n/**\n * Additional options that are sent into each tool call.\n */\n// TODO AI SDK 6: rename to ToolExecutionOptions\nexport interface ToolCallOptions {\n  /**\n   * The ID of the tool call. You can use it e.g. when sending tool-call related information with stream data.\n   */\n  toolCallId: string;\n\n  /**\n   * Messages that were sent to the language model to initiate the response that contained the tool call.\n   * The messages **do not** include the system prompt nor the assistant response that contained the tool call.\n   */\n  messages: ModelMessage[];\n\n  /**\n   * An optional abort signal that indicates that the overall operation should be aborted.\n   */\n  abortSignal?: AbortSignal;\n\n  /**\n   * Additional context.\n   *\n   * Experimental (can break in patch releases).\n   */\n  experimental_context?: unknown;\n}\n\nexport type ToolExecuteFunction<INPUT, OUTPUT> = (\n  input: INPUT,\n  options: ToolCallOptions,\n) => PromiseLike<OUTPUT> | OUTPUT;\n\n// 0 extends 1 & N checks for any\n// [N] extends [never] checks for never\ntype NeverOptional<N, T> = 0 extends 1 & N\n  ? Partial<T>\n  : [N] extends [never]\n    ? Partial<Record<keyof T, undefined>>\n    : T;\n\n/**\nA tool contains the description and the schema of the input that the tool expects.\nThis enables the language model to generate the input.\n\nThe tool can also contain an optional execute function for the actual execution function of the tool.\n */\nexport type Tool<\n  INPUT extends JSONValue | unknown | never = any,\n  OUTPUT extends JSONValue | unknown | never = any,\n> = {\n  /**\nAn optional description of what the tool does.\nWill be used by the language model to decide whether to use the tool.\nNot used for provider-defined tools.\n   */\n  description?: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n   */\n  providerOptions?: ProviderOptions;\n} & NeverOptional<\n  INPUT,\n  {\n    /**\nThe schema of the input that the tool expects. The language model will use this to generate the input.\nIt is also used to validate the output of the language model.\nUse descriptions to make the input understandable for the language model.\n   */\n    inputSchema: FlexibleSchema<INPUT>;\n\n    /**\n     * Optional function that is called when the argument streaming starts.\n     * Only called when the tool is used in a streaming context.\n     */\n    onInputStart?: (options: ToolCallOptions) => void | PromiseLike<void>;\n\n    /**\n     * Optional function that is called when an argument streaming delta is available.\n     * Only called when the tool is used in a streaming context.\n     */\n    onInputDelta?: (\n      options: { inputTextDelta: string } & ToolCallOptions,\n    ) => void | PromiseLike<void>;\n\n    /**\n     * Optional function that is called when a tool call can be started,\n     * even if the execute function is not provided.\n     */\n    onInputAvailable?: (\n      options: {\n        input: [INPUT] extends [never] ? undefined : INPUT;\n      } & ToolCallOptions,\n    ) => void | PromiseLike<void>;\n  }\n> &\n  NeverOptional<\n    OUTPUT,\n    {\n      /**\nOptional conversion function that maps the tool result to an output that can be used by the language model.\n\nIf not provided, the tool result will be sent as a JSON object.\n      */\n      toModelOutput?: (\n        output: OUTPUT,\n      ) => LanguageModelV2ToolResultPart['output'];\n    } & (\n      | {\n          /**\nAn async function that is called with the arguments from the tool call and produces a result.\nIf not provided, the tool will not be executed automatically.\n\n@args is the input of the tool call.\********************* is a signal that can be used to abort the tool call.\n      */\n          execute: ToolExecuteFunction<INPUT, OUTPUT>;\n\n          outputSchema?: FlexibleSchema<OUTPUT>;\n        }\n      | {\n          outputSchema: FlexibleSchema<OUTPUT>;\n\n          execute?: never;\n        }\n    )\n  > &\n  (\n    | {\n        /**\nTool with user-defined input and output schemas.\n     */\n        type?: undefined | 'function';\n      }\n    | {\n        /**\nTool that is defined at runtime (e.g. an MCP tool).\nThe types of input and output are not known at development time.\n       */\n        type: 'dynamic';\n      }\n    | {\n        /**\nTool with provider-defined input and output schemas.\n     */\n        type: 'provider-defined';\n\n        /**\nThe ID of the tool. Should follow the format `<provider-name>.<unique-tool-name>`.\n   */\n        id: `${string}.${string}`;\n\n        /**\nThe name of the tool that the user must use in the tool set.\n */\n        name: string;\n\n        /**\nThe arguments for configuring the tool. Must match the expected arguments defined by the provider for this tool.\n     */\n        args: Record<string, unknown>;\n      }\n  );\n\n/**\n * Infer the input type of a tool.\n */\nexport type InferToolInput<TOOL extends Tool> =\n  TOOL extends Tool<infer INPUT, any> ? INPUT : never;\n\n/**\n * Infer the output type of a tool.\n */\nexport type InferToolOutput<TOOL extends Tool> =\n  TOOL extends Tool<any, infer OUTPUT> ? OUTPUT : never;\n\n/**\nHelper function for inferring the execute args of a tool.\n */\n// Note: overload order is important for auto-completion\nexport function tool<INPUT, OUTPUT>(\n  tool: Tool<INPUT, OUTPUT>,\n): Tool<INPUT, OUTPUT>;\nexport function tool<INPUT>(tool: Tool<INPUT, never>): Tool<INPUT, never>;\nexport function tool<OUTPUT>(tool: Tool<never, OUTPUT>): Tool<never, OUTPUT>;\nexport function tool(tool: Tool<never, never>): Tool<never, never>;\nexport function tool(tool: any): any {\n  return tool;\n}\n\n/**\nHelper function for defining a dynamic tool.\n */\nexport function dynamicTool(tool: {\n  description?: string;\n  providerOptions?: ProviderOptions;\n  inputSchema: FlexibleSchema<unknown>;\n  execute: ToolExecuteFunction<unknown, unknown>;\n  toModelOutput?: (output: unknown) => LanguageModelV2ToolResultPart['output'];\n}): Tool<unknown, unknown> & {\n  type: 'dynamic';\n} {\n  return { ...tool, type: 'dynamic' };\n}\n", "import { tool, Tool, ToolExecuteFunction } from './types/tool';\nimport { FlexibleSchema } from './schema';\n\nexport type ProviderDefinedToolFactory<INPUT, ARGS extends object> = <OUTPUT>(\n  options: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  },\n) => Tool<INPUT, OUTPUT>;\n\nexport function createProviderDefinedToolFactory<INPUT, ARGS extends object>({\n  id,\n  name,\n  inputSchema,\n}: {\n  id: `${string}.${string}`;\n  name: string;\n  inputSchema: FlexibleSchema<INPUT>;\n}): ProviderDefinedToolFactory<INPUT, ARGS> {\n  return <OUTPUT>({\n    execute,\n    outputSchema,\n    toModelOutput,\n    onInputStart,\n    onInputDelta,\n    onInputAvailable,\n    ...args\n  }: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    outputSchema?: FlexibleSchema<OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  }): Tool<INPUT, OUTPUT> =>\n    tool({\n      type: 'provider-defined',\n      id,\n      name,\n      args,\n      inputSchema,\n      outputSchema,\n      execute,\n      toModelOutput,\n      onInputStart,\n      onInputDelta,\n      onInputAvailable,\n    });\n}\n\nexport type ProviderDefinedToolFactoryWithOutputSchema<\n  INPUT,\n  OUTPUT,\n  ARGS extends object,\n> = (\n  options: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  },\n) => Tool<INPUT, OUTPUT>;\n\nexport function createProviderDefinedToolFactoryWithOutputSchema<\n  INPUT,\n  OUTPUT,\n  ARGS extends object,\n>({\n  id,\n  name,\n  inputSchema,\n  outputSchema,\n}: {\n  id: `${string}.${string}`;\n  name: string;\n  inputSchema: FlexibleSchema<INPUT>;\n  outputSchema: FlexibleSchema<OUTPUT>;\n}): ProviderDefinedToolFactoryWithOutputSchema<INPUT, OUTPUT, ARGS> {\n  return ({\n    execute,\n    toModelOutput,\n    onInputStart,\n    onInputDelta,\n    onInputAvailable,\n    ...args\n  }: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  }): Tool<INPUT, OUTPUT> =>\n    tool({\n      type: 'provider-defined',\n      id,\n      name,\n      args,\n      inputSchema,\n      outputSchema,\n      execute,\n      toModelOutput,\n      onInputStart,\n      onInputDelta,\n      onInputAvailable,\n    });\n}\n", "export type Resolvable<T> =\n  | T // Raw value\n  | Promise<T> // Promise of value\n  | (() => T) // Function returning value\n  | (() => Promise<T>); // Function returning promise of value\n\n/**\n * Resolves a value that could be a raw value, a Promise, a function returning a value,\n * or a function returning a Promise.\n */\nexport async function resolve<T>(value: Resolvable<T>): Promise<T> {\n  // If it's a function, call it to get the value/promise\n  if (typeof value === 'function') {\n    value = (value as Function)();\n  }\n\n  // Otherwise just resolve whatever we got (value or promise)\n  return Promise.resolve(value as T);\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport { ZodType } from 'zod/v4';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { parseJSON, ParseResult, safeParseJSON } from './parse-json';\nimport { parseJsonEventStream } from './parse-json-event-stream';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  rawValue?: unknown;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodType<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = await parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodType<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: parseJsonEventStream({\n        stream: response.body,\n        schema: chunkSchema,\n      }),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodType<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          async transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                await safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodType<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = await safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n      rawValue: parsedResult.rawValue,\n    };\n  };\n\nexport const createBinaryResponseHandler =\n  (): ResponseHandler<Uint8Array> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.body) {\n      throw new APICallError({\n        message: 'Response body is empty',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n      });\n    }\n\n    try {\n      const buffer = await response.arrayBuffer();\n      return {\n        responseHeaders,\n        value: new Uint8Array(buffer),\n      };\n    } catch (error) {\n      throw new APICallError({\n        message: 'Failed to read response as array buffer',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n        cause: error,\n      });\n    }\n  };\n\nexport const createStatusCodeErrorResponseHandler =\n  (): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n    const responseBody = await response.text();\n\n    return {\n      responseHeaders,\n      value: new APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues: requestBodyValues as Record<string, unknown>,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n      }),\n    };\n  };\n", "import { JSONSchema7 } from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { jsonSchema, Schema } from './schema';\n\nexport function zod3Schema<OBJECT>(\n  zodSchema: z3.Schema<OBJECT, z3.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  return jsonSchema(\n    zodToJsonSchema(zodSchema, {\n      $refStrategy: useReferences ? 'root' : 'none',\n      target: 'jsonSchema7', // note: openai mode breaks various gemini conversions\n    }) as JSONSchema7,\n    {\n      validate: async value => {\n        const result = await zodSchema.safeParseAsync(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n\nexport function zod4Schema<OBJECT>(\n  zodSchema: z4.core.$ZodType<OBJECT, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  const z4JSONSchema = z4.toJSONSchema(zodSchema, {\n    target: 'draft-7',\n    io: 'output',\n    reused: useReferences ? 'ref' : 'inline',\n  }) as JSONSchema7;\n\n  return jsonSchema(z4JSONSchema, {\n    validate: async value => {\n      const result = await z4.safeParseAsync(zodSchema, value);\n      return result.success\n        ? { success: true, value: result.data }\n        : { success: false, error: result.error };\n    },\n  });\n}\n\nexport function isZod4Schema(\n  zodSchema: z4.core.$ZodType<any, any> | z3.Schema<any, z3.ZodTypeDef, any>,\n): zodSchema is z4.core.$ZodType<any, any> {\n  // https://zod.dev/library-authors?id=how-to-support-zod-3-and-zod-4-simultaneously\n  return '_zod' in zodSchema;\n}\n\nexport function zodSchema<OBJECT>(\n  zodSchema:\n    | z4.core.$ZodType<OBJECT, any>\n    | z3.Schema<OBJECT, z3.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  if (isZod4Schema(zodSchema)) {\n    return zod4Schema(zodSchema, options);\n  } else {\n    return zod3Schema(zodSchema, options);\n  }\n}\n", "import { Validator, validatorSymbol, type ValidationResult } from './validator';\nimport { JSONSchema7 } from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport { zodSchema } from './zod-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol.for('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\nexport type FlexibleSchema<T> = z4.core.$ZodType<T> | z3.Schema<T> | Schema<T>;\n\nexport type InferSchema<SCHEMA> = SCHEMA extends z3.Schema\n  ? z3.infer<SCHEMA>\n  : SCHEMA extends z4.core.$ZodType\n    ? z4.infer<SCHEMA>\n    : SCHEMA extends Schema<infer T>\n      ? T\n      : never;\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>;\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema:\n    | z4.core.$ZodType<OBJECT, any>\n    | z3.Schema<OBJECT, z3.ZodTypeDef, any>\n    | Schema<OBJECT>\n    | undefined,\n): Schema<OBJECT> {\n  return schema == null\n    ? jsonSchema({\n        properties: {},\n        additionalProperties: false,\n      })\n    : isSchema(schema)\n      ? schema\n      : zodSchema(schema);\n}\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise <PERSON><PERSON><PERSON><PERSON> will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n\nexport function convertToBase64(value: string | Uint8Array): string {\n  return value instanceof Uint8Array ? convertUint8ArrayToBase64(value) : value;\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n", "export * from './combine-headers';\nexport { convertAsyncIteratorToReadableStream } from './convert-async-iterator-to-readable-stream';\nexport * from './delay';\nexport * from './extract-response-headers';\nexport * from './fetch-function';\nexport { createIdGenerator, generateId, type IdGenerator } from './generate-id';\nexport * from './get-error-message';\nexport * from './get-from-api';\nexport * from './is-abort-error';\nexport { isUrlSupported } from './is-url-supported';\nexport * from './load-api-key';\nexport { loadOptionalSetting } from './load-optional-setting';\nexport { loadSetting } from './load-setting';\nexport * from './parse-json';\nexport { parseJsonEventStream } from './parse-json-event-stream';\nexport { parseProviderOptions } from './parse-provider-options';\nexport * from './post-to-api';\nexport {\n  createProviderDefinedToolFactory,\n  type ProviderDefinedToolFactory,\n  createProviderDefinedToolFactoryWithOutputSchema,\n  type ProviderDefinedToolFactoryWithOutputSchema,\n} from './provider-defined-tool-factory';\nexport * from './remove-undefined-entries';\nexport * from './resolve';\nexport * from './response-handler';\nexport {\n  asSchema,\n  jsonSchema,\n  type FlexibleSchema,\n  type InferSchema,\n  type Schema,\n} from './schema';\nexport * from './uint8-utils';\nexport * from './validate-types';\nexport * from './validator';\nexport * from './without-trailing-slash';\nexport { zodSchema } from './zod-schema';\n\n// folder re-exports\nexport * from './types';\n\n// external re-exports\nexport * from '@standard-schema/spec';\nexport {\n  EventSourceParserStream,\n  type EventSourceMessage,\n} from 'eventsource-parser/stream';\n"], "mappings": ";AAAO,SAAS,kBACX,SACiC;AACpC,SAAO,QAAQ;AAAA,IACb,CAAC,iBAAiB,oBAAoB;AAAA,MACpC,GAAG;AAAA,MACH,GAAI,0CAAkB,CAAC;AAAA,IACzB;AAAA,IACA,CAAC;AAAA,EACH;AACF;;;ACHO,SAAS,qCACd,UACmB;AACnB,SAAO,IAAI,eAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAO3B,MAAM,KAAK,YAAY;AACrB,UAAI;AACF,cAAM,EAAE,OAAO,KAAK,IAAI,MAAM,SAAS,KAAK;AAC5C,YAAI,MAAM;AACR,qBAAW,MAAM;AAAA,QACnB,OAAO;AACL,qBAAW,QAAQ,KAAK;AAAA,QAC1B;AAAA,MACF,SAAS,OAAO;AACd,mBAAW,MAAM,KAAK;AAAA,MACxB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,SAAS;AAAA,IAAC;AAAA,EACZ,CAAC;AACH;;;AC3BA,eAAsB,MACpB,WACA,SAGe;AACf,MAAI,aAAa,MAAM;AACrB,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEA,QAAM,SAAS,mCAAS;AAExB,SAAO,IAAI,QAAc,CAACA,UAAS,WAAW;AAC5C,QAAI,iCAAQ,SAAS;AACnB,aAAO,iBAAiB,CAAC;AACzB;AAAA,IACF;AAEA,UAAM,YAAY,WAAW,MAAM;AACjC,cAAQ;AACR,MAAAA,SAAQ;AAAA,IACV,GAAG,SAAS;AAEZ,UAAM,UAAU,MAAM;AACpB,mBAAa,SAAS;AACtB,uCAAQ,oBAAoB,SAAS;AAAA,IACvC;AAEA,UAAM,UAAU,MAAM;AACpB,cAAQ;AACR,aAAO,iBAAiB,CAAC;AAAA,IAC3B;AAEA,qCAAQ,iBAAiB,SAAS;AAAA,EACpC,CAAC;AACH;AAEA,SAAS,mBAAiC;AACxC,SAAO,IAAI,aAAa,qBAAqB,YAAY;AAC3D;;;ACxCO,SAAS,uBAAuB,UAAoB;AACzD,SAAO,OAAO,YAAoB,CAAC,GAAG,SAAS,OAAO,CAAC;AACzD;;;ACRA,SAAS,4BAA4B;AAY9B,IAAM,oBAAoB,CAAC;AAAA,EAChC;AAAA,EACA,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AACd,IAKI,CAAC,MAAmB;AACtB,QAAM,YAAY,MAAM;AACtB,UAAM,iBAAiB,SAAS;AAChC,UAAM,QAAQ,IAAI,MAAM,IAAI;AAC5B,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,YAAM,CAAC,IAAI,SAAU,KAAK,OAAO,IAAI,iBAAkB,CAAC;AAAA,IAC1D;AACA,WAAO,MAAM,KAAK,EAAE;AAAA,EACtB;AAEA,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AAGA,MAAI,SAAS,SAAS,SAAS,GAAG;AAChC,UAAM,IAAI,qBAAqB;AAAA,MAC7B,UAAU;AAAA,MACV,SAAS,kBAAkB,SAAS,uCAAuC,QAAQ;AAAA,IACrF,CAAC;AAAA,EACH;AAEA,SAAO,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC;AAClD;AAWO,IAAM,aAAa,kBAAkB;;;ACxDrC,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;AAAA,EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;;;ACdA,SAAS,gBAAAC,qBAAoB;;;ACA7B,SAAS,oBAAoB;;;ACAtB,SAAS,aAAa,OAAgC;AAC3D,UACG,iBAAiB,SAAS,iBAAiB,kBAC3C,MAAM,SAAS,gBACd,MAAM,SAAS;AAAA,EACf,MAAM,SAAS;AAErB;;;ADJA,IAAM,8BAA8B,CAAC,gBAAgB,iBAAiB;AAE/D,SAAS,iBAAiB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,MAAI,aAAa,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AAGA,MACE,iBAAiB,aACjB,4BAA4B,SAAS,MAAM,QAAQ,YAAY,CAAC,GAChE;AACA,UAAM,QAAS,MAAc;AAE7B,QAAI,SAAS,MAAM;AAEjB,aAAO,IAAI,aAAa;AAAA,QACtB,SAAS,0BAA0B,MAAM,OAAO;AAAA,QAChD;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;;;AEjCO,SAAS,uBACd,QACmB;AACnB,SAAO,OAAO;AAAA,IACZ,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,SAAS,IAAI;AAAA,EAChE;AACF;;;AHFA,IAAM,mBAAmB,MAAM,WAAW;AAEnC,IAAM,aAAa,OAAU;AAAA,EAClC;AAAA,EACA,UAAU,CAAC;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,iBAAiB;AAC3B,MAOM;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK;AAAA,MAChC,QAAQ;AAAA,MACR,SAAS,uBAAuB,OAAO;AAAA,MACvC,QAAQ;AAAA,IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;AAAA,UAC7C;AAAA,UACA;AAAA,UACA,mBAAmB,CAAC;AAAA,QACtB,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAKC,cAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAEA,cAAM,IAAIA,cAAa;AAAA,UACrB,SAAS;AAAA,UACT,OAAO;AAAA,UACP,YAAY,SAAS;AAAA,UACrB;AAAA,UACA;AAAA,UACA,mBAAmB,CAAC;AAAA,QACtB,CAAC;AAAA,MACH;AAEA,YAAM,iBAAiB;AAAA,IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;AAAA,QACrC;AAAA,QACA;AAAA,QACA,mBAAmB,CAAC;AAAA,MACtB,CAAC;AAAA,IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAKA,cAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,YAAM,IAAIA,cAAa;AAAA,QACrB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,mBAAmB,CAAC;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF,SAAS,OAAO;AACd,UAAM,iBAAiB,EAAE,OAAO,KAAK,mBAAmB,CAAC,EAAE,CAAC;AAAA,EAC9D;AACF;;;AI/EO,SAAS,eAAe;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AACF,GAIY;AAEV,QAAM,IAAI,YAAY;AACtB,cAAY,UAAU,YAAY;AAElC,SACE,OAAO,QAAQ,aAAa,EAEzB,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACrB,UAAMC,aAAY,IAAI,YAAY;AAClC,WAAOA,eAAc,OAAOA,eAAc,QACtC,EAAE,iBAAiB,IAAI,SAAS,MAAM,IACtC,EAAE,iBAAiBA,WAAU,QAAQ,MAAM,EAAE,GAAG,SAAS,MAAM;AAAA,EACrE,CAAC,EAEA,OAAO,CAAC,EAAE,gBAAgB,MAAM,UAAU,WAAW,eAAe,CAAC,EACrE,QAAQ,CAAC,EAAE,QAAQ,MAAM,OAAO,EAEhC,KAAK,aAAW,QAAQ,KAAK,GAAG,CAAC;AAExC;;;ACvCA,SAAS,uBAAuB;AAEzB,SAAS,WAAW;AAAA,EACzB;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB;AACF,GAKW;AACT,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW;AAAA,IACzB,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,YAAY,aAAa;AAClC,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB;AAAA,IACvF,CAAC;AAAA,EACH;AAEA,WAAS,QAAQ,IAAI,uBAAuB;AAE5C,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB,sBAAsB,uBAAuB;AAAA,IACpI,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW,+CAA+C,uBAAuB;AAAA,IAC/F,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;ACrCO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AACF,GAGuB;AACrB,MAAI,OAAO,iBAAiB,UAAU;AACpC,WAAO;AAAA,EACT;AAEA,MAAI,gBAAgB,QAAQ,OAAO,YAAY,aAAa;AAC1D,WAAO;AAAA,EACT;AAEA,iBAAe,QAAQ,IAAI,uBAAuB;AAElD,MAAI,gBAAgB,QAAQ,OAAO,iBAAiB,UAAU;AAC5D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC7BA,SAAS,wBAAwB;AAW1B,SAAS,YAAY;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKW;AACT,MAAI,OAAO,iBAAiB,UAAU;AACpC,WAAO;AAAA,EACT;AAEA,MAAI,gBAAgB,MAAM;AACxB,UAAM,IAAI,iBAAiB;AAAA,MACzB,SAAS,GAAG,WAAW;AAAA,IACzB,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,YAAY,aAAa;AAClC,UAAM,IAAI,iBAAiB;AAAA,MACzB,SACE,GAAG,WAAW,2CACQ,WAAW;AAAA,IAErC,CAAC;AAAA,EACH;AAEA,iBAAe,QAAQ,IAAI,uBAAuB;AAElD,MAAI,gBAAgB,MAAM;AACxB,UAAM,IAAI,iBAAiB;AAAA,MACzB,SACE,GAAG,WAAW,2CACQ,WAAW,sBACvB,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,iBAAiB,UAAU;AACpC,UAAM,IAAI,iBAAiB;AAAA,MACzB,SACE,GAAG,WAAW,+CACM,uBAAuB;AAAA,IAC/C,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AC7DA;AAAA,EACE;AAAA,EAEA,uBAAAC;AAAA,OACK;;;ACmBP,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAE7B,SAAS,OAAO,MAAc;AAE5B,QAAM,MAAM,KAAK,MAAM,IAAI;AAG3B,MAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;AAC3C,WAAO;AAAA,EACT;AAEA,MACE,eAAe,KAAK,IAAI,MAAM,SAC9B,qBAAqB,KAAK,IAAI,MAAM,OACpC;AACA,WAAO;AAAA,EACT;AAGA,SAAO,OAAO,GAAG;AACnB;AAEA,SAAS,OAAO,KAAU;AACxB,MAAI,OAAO,CAAC,GAAG;AAEf,SAAO,KAAK,QAAQ;AAClB,UAAM,QAAQ;AACd,WAAO,CAAC;AAER,eAAW,QAAQ,OAAO;AACxB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,GAAG;AAC3D,cAAM,IAAI,YAAY,8CAA8C;AAAA,MACtE;AAEA,UACE,OAAO,UAAU,eAAe,KAAK,MAAM,aAAa,KACxD,OAAO,UAAU,eAAe,KAAK,KAAK,aAAa,WAAW,GAClE;AACA,cAAM,IAAI,YAAY,8CAA8C;AAAA,MACtE;AAEA,iBAAW,OAAO,MAAM;AACtB,cAAM,QAAQ,KAAK,GAAG;AACtB,YAAI,SAAS,OAAO,UAAU,UAAU;AACtC,eAAK,KAAK,KAAK;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEO,SAAS,gBAAgB,MAAc;AAE5C,QAAM,EAAE,gBAAgB,IAAI;AAC5B,QAAM,kBAAkB;AACxB,MAAI;AACF,WAAO,OAAO,IAAI;AAAA,EACpB,UAAE;AACA,UAAM,kBAAkB;AAAA,EAC1B;AACF;;;ACrFA,SAAS,uBAAAC,4BAA2B;;;ACApC,SAAS,2BAA2B;AAM7B,IAAM,kBAAkB,OAAO,IAAI,qBAAqB;AA0BxD,SAAS,UACd,UAKmB;AACnB,SAAO,EAAE,CAAC,eAAe,GAAG,MAAM,SAAS;AAC7C;AAEO,SAAS,YAAY,OAAoC;AAC9D,SACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,MAAM,eAAe,MAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,OACmB;AACnB,SAAO,YAAY,KAAK,IAAI,QAAQ,wBAAwB,KAAK;AACnE;AAEO,SAAS,wBACd,gBACmB;AACnB,SAAO,UAAU,OAAM,UAAS;AAC9B,UAAM,SAAS,MAAM,eAAe,WAAW,EAAE,SAAS,KAAK;AAE/D,WAAO,OAAO,UAAU,OACpB,EAAE,SAAS,MAAM,OAAO,OAAO,MAAM,IACrC;AAAA,MACE,SAAS;AAAA,MACT,OAAO,IAAI,oBAAoB;AAAA,QAC7B;AAAA,QACA,OAAO,OAAO;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACN,CAAC;AACH;;;AD7DA,eAAsB,cAAsB;AAAA,EAC1C;AAAA,EACA;AACF,GAGoB;AAClB,QAAM,SAAS,MAAM,kBAAkB,EAAE,OAAO,OAAO,CAAC;AAExD,MAAI,CAAC,OAAO,SAAS;AACnB,UAAMC,qBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,EAC/D;AAEA,SAAO,OAAO;AAChB;AAWA,eAAsB,kBAA0B;AAAA,EAC9C;AAAA,EACA;AACF,GAcE;AACA,QAAMC,aAAY,YAAY,MAAM;AAEpC,MAAI;AACF,QAAIA,WAAU,YAAY,MAAM;AAC9B,aAAO,EAAE,SAAS,MAAM,OAAwB,UAAU,MAAM;AAAA,IAClE;AAEA,UAAM,SAAS,MAAMA,WAAU,SAAS,KAAK;AAE7C,QAAI,OAAO,SAAS;AAClB,aAAO,EAAE,SAAS,MAAM,OAAO,OAAO,OAAO,UAAU,MAAM;AAAA,IAC/D;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAOD,qBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,MAC9D,UAAU;AAAA,IACZ;AAAA,EACF,SAAS,OAAO;AACd,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAOA,qBAAoB,KAAK,EAAE,OAAO,OAAO,MAAM,CAAC;AAAA,MACvD,UAAU;AAAA,IACZ;AAAA,EACF;AACF;;;AFhDA,eAAsB,UAAa;AAAA,EACjC;AAAA,EACA;AACF,GAGe;AACb,MAAI;AACF,UAAM,QAAQ,gBAAgB,IAAI;AAElC,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AAEA,WAAO,cAAiB,EAAE,OAAO,OAAO,CAAC;AAAA,EAC3C,SAAS,OAAO;AACd,QACE,eAAe,WAAW,KAAK,KAC/BE,qBAAoB,WAAW,KAAK,GACpC;AACA,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;AAAA,EACjD;AACF;AAgCA,eAAsB,cAAiB;AAAA,EACrC;AAAA,EACA;AACF,GAG4B;AAC1B,MAAI;AACF,UAAM,QAAQ,gBAAgB,IAAI;AAElC,QAAI,UAAU,MAAM;AAClB,aAAO,EAAE,SAAS,MAAM,OAAmB,UAAU,MAAM;AAAA,IAC7D;AAEA,WAAO,MAAM,kBAAqB,EAAE,OAAO,OAAO,CAAC;AAAA,EACrD,SAAS,OAAO;AACd,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,eAAe,WAAW,KAAK,IAClC,QACA,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;AAAA,MAC7C,UAAU;AAAA,IACZ;AAAA,EACF;AACF;AAEO,SAAS,eAAe,OAAwB;AACrD,MAAI;AACF,oBAAgB,KAAK;AACrB,WAAO;AAAA,EACT,SAAQ;AACN,WAAO;AAAA,EACT;AACF;;;AI3HA;AAAA,EAEE;AAAA,OACK;AAOA,SAAS,qBAAwB;AAAA,EACtC;AAAA,EACA;AACF,GAGmC;AACjC,SAAO,OACJ,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,IAAI,wBAAwB,CAAC,EACzC;AAAA,IACC,IAAI,gBAAoD;AAAA,MACtD,MAAM,UAAU,EAAE,KAAK,GAAG,YAAY;AAEpC,YAAI,SAAS,UAAU;AACrB;AAAA,QACF;AAEA,mBAAW,QAAQ,MAAM,cAAc,EAAE,MAAM,MAAM,OAAO,CAAC,CAAC;AAAA,MAChE;AAAA,IACF,CAAC;AAAA,EACH;AACJ;;;AChCA,SAAS,wBAAAC,6BAA4B;AAIrC,eAAsB,qBAAwB;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AACF,GAI2B;AACzB,OAAI,mDAAkB,cAAa,MAAM;AACvC,WAAO;AAAA,EACT;AAEA,QAAM,wBAAwB,MAAM,kBAAiC;AAAA,IACnE,OAAO,gBAAgB,QAAQ;AAAA,IAC/B;AAAA,EACF,CAAC;AAED,MAAI,CAAC,sBAAsB,SAAS;AAClC,UAAM,IAAIC,sBAAqB;AAAA,MAC7B,UAAU;AAAA,MACV,SAAS,WAAW,QAAQ;AAAA,MAC5B,OAAO,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH;AAEA,SAAO,sBAAsB;AAC/B;;;AC/BA,SAAS,gBAAAC,qBAAoB;AAS7B,IAAMC,oBAAmB,MAAM,WAAW;AAEnC,IAAM,gBAAgB,OAAU;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MASE,UAAU;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AAAA,EACA,MAAM;AAAA,IACJ,SAAS,KAAK,UAAU,IAAI;AAAA,IAC5B,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAEI,IAAM,oBAAoB,OAAU;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MASE,UAAU;AAAA,EACR;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ,SAAS;AAAA,IACT,QAAQ,OAAO,YAAa,SAAiB,QAAQ,CAAC;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAEI,IAAM,YAAY,OAAU;AAAA,EACjC;AAAA,EACA,UAAU,CAAC;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQA,kBAAiB;AAC3B,MAWM;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK;AAAA,MAChC,QAAQ;AAAA,MACR,SAAS,uBAAuB,OAAO;AAAA,MACvC,MAAM,KAAK;AAAA,MACX,QAAQ;AAAA,IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;AAAA,UAC7C;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,QAC1B,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAKC,cAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAEA,cAAM,IAAIA,cAAa;AAAA,UACrB,SAAS;AAAA,UACT,OAAO;AAAA,UACP,YAAY,SAAS;AAAA,UACrB;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,QAC1B,CAAC;AAAA,MACH;AAEA,YAAM,iBAAiB;AAAA,IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;AAAA,QACrC;AAAA,QACA;AAAA,QACA,mBAAmB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAKA,cAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,YAAM,IAAIA,cAAa;AAAA,QACrB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,mBAAmB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF,SAAS,OAAO;AACd,UAAM,iBAAiB,EAAE,OAAO,KAAK,mBAAmB,KAAK,OAAO,CAAC;AAAA,EACvE;AACF;;;ACoCO,SAAS,KAAKC,OAAgB;AACnC,SAAOA;AACT;AAKO,SAAS,YAAYA,OAQ1B;AACA,SAAO,EAAE,GAAGA,OAAM,MAAM,UAAU;AACpC;;;ACvMO,SAAS,iCAA6D;AAAA,EAC3E;AAAA,EACA;AAAA,EACA;AACF,GAI4C;AAC1C,SAAO,CAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,MAQE,KAAK;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACL;AAgBO,SAAS,iDAId;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKoE;AAClE,SAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,MAOE,KAAK;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACL;;;ACnGA,eAAsB,QAAW,OAAkC;AAEjE,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAS,MAAmB;AAAA,EAC9B;AAGA,SAAO,QAAQ,QAAQ,KAAU;AACnC;;;AClBA,SAAS,gBAAAC,eAAc,8BAA8B;AAgB9C,IAAM,iCACX,CAAI;AAAA,EACF;AAAA,EACA;AAAA,EACA;AACF,MAKA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,kBAAkB,uBAAuB,QAAQ;AAGvD,MAAI,aAAa,KAAK,MAAM,IAAI;AAC9B,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAIC,cAAa;AAAA,QACtB,SAAS,SAAS;AAAA,QAClB;AAAA,QACA;AAAA,QACA,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,aAAa,2CAAc;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,EACF;AAGA,MAAI;AACF,UAAM,cAAc,MAAM,UAAU;AAAA,MAClC,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAIA,cAAa;AAAA,QACtB,SAAS,eAAe,WAAW;AAAA,QACnC;AAAA,QACA;AAAA,QACA,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,aAAa,2CAAc,UAAU;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF,SAAS,YAAY;AACnB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAIA,cAAa;AAAA,QACtB,SAAS,SAAS;AAAA,QAClB;AAAA,QACA;AAAA,QACA,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,aAAa,2CAAc;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEK,IAAM,mCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;AAAA,EACrC;AAEA,SAAO;AAAA,IACL;AAAA,IACA,OAAO,qBAAqB;AAAA,MAC1B,QAAQ,SAAS;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;AAEK,IAAM,kCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS;AAEb,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAS,KAAK,YAAY,IAAI,kBAAkB,CAAC,EAAE;AAAA,MACxD,IAAI,gBAAwC;AAAA,QAC1C,MAAM,UAAU,WAAW,YAAY;AACrC,cAAI,UAAU,SAAS,IAAI,GAAG;AAC5B,uBAAW;AAAA,cACT,MAAM,cAAc;AAAA,gBAClB,MAAM,SAAS;AAAA,gBACf,QAAQ;AAAA,cACV,CAAC;AAAA,YACH;AACA,qBAAS;AAAA,UACX,OAAO;AACL,sBAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEK,IAAM,4BACX,CAAI,mBACJ,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,QAAM,eAAe,MAAM,cAAc;AAAA,IACvC,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM,IAAIA,cAAa;AAAA,MACrB,SAAS;AAAA,MACT,OAAO,aAAa;AAAA,MACpB,YAAY,SAAS;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL;AAAA,IACA,OAAO,aAAa;AAAA,IACpB,UAAU,aAAa;AAAA,EACzB;AACF;AAEK,IAAM,8BACX,MACA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAIA,cAAa;AAAA,MACrB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,YAAY,SAAS;AAAA,MACrB;AAAA,MACA,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,MAAI;AACF,UAAM,SAAS,MAAM,SAAS,YAAY;AAC1C,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAI,WAAW,MAAM;AAAA,IAC9B;AAAA,EACF,SAAS,OAAO;AACd,UAAM,IAAIA,cAAa;AAAA,MACrB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,YAAY,SAAS;AAAA,MACrB;AAAA,MACA,cAAc;AAAA,MACd,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEK,IAAM,uCACX,MACA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AACvD,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,SAAO;AAAA,IACL;AAAA,IACA,OAAO,IAAIA,cAAa;AAAA,MACtB,SAAS,SAAS;AAAA,MAClB;AAAA,MACA;AAAA,MACA,YAAY,SAAS;AAAA,MACrB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AC1NF,YAAY,QAAQ;AACpB,OAAO,qBAAqB;AAGrB,SAAS,WACdC,YACA,SASgB;AAjBlB;AAmBE,QAAM,iBAAgB,wCAAS,kBAAT,YAA0B;AAEhD,SAAO;AAAA,IACL,gBAAgBA,YAAW;AAAA,MACzB,cAAc,gBAAgB,SAAS;AAAA,MACvC,QAAQ;AAAA;AAAA,IACV,CAAC;AAAA,IACD;AAAA,MACE,UAAU,OAAM,UAAS;AACvB,cAAM,SAAS,MAAMA,WAAU,eAAe,KAAK;AACnD,eAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,WACdA,YACA,SASgB;AAhDlB;AAkDE,QAAM,iBAAgB,wCAAS,kBAAT,YAA0B;AAEhD,QAAM,eAAkB,gBAAaA,YAAW;AAAA,IAC9C,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,QAAQ,gBAAgB,QAAQ;AAAA,EAClC,CAAC;AAED,SAAO,WAAW,cAAc;AAAA,IAC9B,UAAU,OAAM,UAAS;AACvB,YAAM,SAAS,MAAS,kBAAeA,YAAW,KAAK;AACvD,aAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;AAAA,IAC5C;AAAA,EACF,CAAC;AACH;AAEO,SAAS,aACdA,YACyC;AAEzC,SAAO,UAAUA;AACnB;AAEO,SAAS,UACdA,YAGA,SASgB;AAChB,MAAI,aAAaA,UAAS,GAAG;AAC3B,WAAO,WAAWA,YAAW,OAAO;AAAA,EACtC,OAAO;AACL,WAAO,WAAWA,YAAW,OAAO;AAAA,EACtC;AACF;;;ACrFA,IAAM,eAAe,OAAO,IAAI,kBAAkB;AAmC3C,SAAS,WACdC,aACA;AAAA,EACE;AACF,IAII,CAAC,GACW;AAChB,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,IAChB,OAAO;AAAA;AAAA,IACP,CAAC,eAAe,GAAG;AAAA,IACnB,YAAAA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,OAAiC;AACjD,SACE,OAAO,UAAU,YACjB,UAAU,QACV,gBAAgB,SAChB,MAAM,YAAY,MAAM,QACxB,gBAAgB,SAChB,cAAc;AAElB;AAEO,SAAS,SACd,QAKgB;AAChB,SAAO,UAAU,OACb,WAAW;AAAA,IACT,YAAY,CAAC;AAAA,IACb,sBAAsB;AAAA,EACxB,CAAC,IACD,SAAS,MAAM,IACb,SACA,UAAU,MAAM;AACxB;;;ACtFA,IAAM,EAAE,MAAM,KAAK,IAAI;AAEhB,SAAS,0BAA0B,cAAsB;AAC9D,QAAM,YAAY,aAAa,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACnE,QAAM,eAAe,KAAK,SAAS;AACnC,SAAO,WAAW,KAAK,cAAc,UAAQ,KAAK,YAAY,CAAC,CAAE;AACnE;AAEO,SAAS,0BAA0B,OAA2B;AACnE,MAAI,eAAe;AAInB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAgB,OAAO,cAAc,MAAM,CAAC,CAAC;AAAA,EAC/C;AAEA,SAAO,KAAK,YAAY;AAC1B;AAEO,SAAS,gBAAgB,OAAoC;AAClE,SAAO,iBAAiB,aAAa,0BAA0B,KAAK,IAAI;AAC1E;;;ACzBO,SAAS,qBAAqB,KAAyB;AAC5D,SAAO,2BAAK,QAAQ,OAAO;AAC7B;;;ACyCA,cAAc;AACd;AAAA,EACE,2BAAAC;AAAA,OAEK;", "names": ["resolve", "APICallError", "APICallError", "mediaType", "TypeValidationError", "TypeValidationError", "TypeValidationError", "validator", "TypeValidationError", "InvalidArgumentError", "InvalidArgumentError", "APICallError", "getOriginalFetch", "APICallError", "tool", "APICallError", "APICallError", "zodSchema", "jsonSchema", "EventSourceParserStream"]}