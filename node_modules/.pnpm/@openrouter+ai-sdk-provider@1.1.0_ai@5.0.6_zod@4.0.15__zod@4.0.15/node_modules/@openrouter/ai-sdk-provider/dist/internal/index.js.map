{"version": 3, "sources": ["../../src/internal/index.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/ai-sdk-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/api-call-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/empty-response-body-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/get-error-message.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/invalid-argument-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/invalid-prompt-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/invalid-response-data-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/json-parse-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/load-api-key-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/load-setting-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/no-content-generated-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/no-such-model-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/too-many-embedding-values-for-call-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/type-validation-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/errors/unsupported-functionality-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider@2.0.0/node_modules/@ai-sdk/provider/src/json-value/is-json.ts", "../../node_modules/.pnpm/eventsource-parser@3.0.3/node_modules/eventsource-parser/src/errors.ts", "../../node_modules/.pnpm/eventsource-parser@3.0.3/node_modules/eventsource-parser/src/parse.ts", "../../node_modules/.pnpm/eventsource-parser@3.0.3/node_modules/eventsource-parser/src/stream.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/combine-headers.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/convert-async-iterator-to-readable-stream.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/delay.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/extract-response-headers.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/generate-id.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/get-error-message.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/get-from-api.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/handle-fetch-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/is-abort-error.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/remove-undefined-entries.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/is-url-supported.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/load-api-key.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/load-optional-setting.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/load-setting.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/parse-json.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/secure-json-parse.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/validate-types.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/validator.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/parse-json-event-stream.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/parse-provider-options.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/post-to-api.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/types/tool.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/provider-defined-tool-factory.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/resolve.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/response-handler.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/zod-schema.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/schema.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/uint8-utils.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/without-trailing-slash.ts", "../../node_modules/.pnpm/@ai-sdk+provider-utils@3.0.1_zod@3.25.76/node_modules/@ai-sdk/provider-utils/src/index.ts", "../../node_modules/.pnpm/zod-to-json-schema@3.24.6_zod@3.25.76/node_modules/zod-to-json-schema/dist/esm/Options.js", "../../node_modules/.pnpm/zod-to-json-schema@3.24.6_zod@3.25.76/node_modules/zod-to-json-schema/dist/esm/selectParser.js", "../../node_modules/.pnpm/zod-to-json-schema@3.24.6_zod@3.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/array.js", "../../node_modules/.pnpm/zod-to-json-schema@3.24.6_zod@3.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/record.js", "../../node_modules/.pnpm/zod-to-json-schema@3.24.6_zod@3.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/string.js", "../../src/schemas/reasoning-details.ts", "../../src/schemas/error-response.ts", "../../src/utils/map-finish-reason.ts", "../../src/chat/is-url.ts", "../../src/chat/file-url-utils.ts", "../../src/chat/convert-to-openrouter-chat-messages.ts", "../../src/chat/get-tool-choice.ts", "../../src/chat/schemas.ts", "../../src/chat/index.ts", "../../src/completion/convert-to-openrouter-completion-prompt.ts", "../../src/completion/schemas.ts", "../../src/completion/index.ts"], "sourcesContent": ["export * from '../chat';\nexport * from '../completion';\nexport * from '../types';\nexport * from '../types/openrouter-chat-settings';\nexport * from '../types/openrouter-completion-settings';\n", "/**\n * Symbol used for identifying AI SDK Error instances.\n * Enables checking if an error is an instance of AISDKError across package versions.\n */\nconst marker = 'vercel.ai.error';\nconst symbol = Symbol.for(marker);\n\n/**\n * Custom error class for AI SDK related errors.\n * @extends Error\n */\nexport class AISDKError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  /**\n   * The underlying cause of the error, if any.\n   */\n  readonly cause?: unknown;\n\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name,\n    message,\n    cause,\n  }: {\n    name: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = name;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is AISDKError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  protected static hasMarker(error: unknown, marker: string): boolean {\n    const markerSymbol = Symbol.for(marker);\n    return (\n      error != null &&\n      typeof error === 'object' &&\n      markerSymbol in error &&\n      typeof error[markerSymbol] === 'boolean' &&\n      error[markerSymbol] === true\n    );\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_APICallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class APICallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is APICallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_EmptyResponseBodyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class EmptyResponseBodyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is EmptyResponseBodyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A function argument is invalid.\n */\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly argument: string;\n\n  constructor({\n    message,\n    cause,\n    argument,\n  }: {\n    argument: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.argument = argument;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidPromptError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A prompt is invalid. This error should be thrown by providers when they cannot\n * process a prompt.\n */\nexport class InvalidPromptError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly prompt: unknown;\n\n  constructor({\n    prompt,\n    message,\n    cause,\n  }: {\n    prompt: unknown;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message: `Invalid prompt: ${message}`, cause });\n\n    this.prompt = prompt;\n  }\n\n  static isInstance(error: unknown): error is InvalidPromptError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidResponseDataError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Server returned a response with invalid data content.\n * This should be thrown by providers when they cannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is InvalidResponseDataError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_JSONParseError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n// TODO v5: rename to ParseError\nexport class JSONParseError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly text: string;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super({\n      name,\n      message:\n        `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.text = text;\n  }\n\n  static isInstance(error: unknown): error is JSONParseError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadAPIKeyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadAPIKeyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadAPIKeyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadSettingError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadSettingError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadSettingError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoContentGeneratedError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is NoContentGeneratedError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoSuchModelError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchModelError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly modelId: string;\n  readonly modelType:\n    | 'languageModel'\n    | 'textEmbeddingModel'\n    | 'imageModel'\n    | 'transcriptionModel'\n    | 'speechModel';\n\n  constructor({\n    errorName = name,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    errorName?: string;\n    modelId: string;\n    modelType:\n      | 'languageModel'\n      | 'textEmbeddingModel'\n      | 'imageModel'\n      | 'transcriptionModel'\n      | 'speechModel';\n    message?: string;\n  }) {\n    super({ name: errorName, message });\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isInstance(error: unknown): error is NoSuchModelError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_TooManyEmbeddingValuesForCallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TooManyEmbeddingValuesForCallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super({\n      name,\n      message:\n        `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    });\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInstance(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_TypeValidationError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TypeValidationError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly value: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super({\n      name,\n      message:\n        `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is TypeValidationError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause,\n  }: {\n    value: unknown;\n    cause: unknown;\n  }): TypeValidationError {\n    return TypeValidationError.isInstance(cause) && cause.value === value\n      ? cause\n      : new TypeValidationError({ value, cause });\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_UnsupportedFunctionalityError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class UnsupportedFunctionalityError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly functionality: string;\n\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`,\n  }: {\n    functionality: string;\n    message?: string;\n  }) {\n    super({ name, message });\n    this.functionality = functionality;\n  }\n\n  static isInstance(error: unknown): error is UnsupportedFunctionalityError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { JSONArray, JSONObject, JSONValue } from './json-value';\n\nexport function isJSONValue(value: unknown): value is JSONValue {\n  if (\n    value === null ||\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean'\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n\n  if (typeof value === 'object') {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is JSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is JSONObject {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    )\n  );\n}\n", "/**\n * The type of error that occurred.\n * @public\n */\nexport type ErrorType = 'invalid-retry' | 'unknown-field'\n\n/**\n * Error thrown when encountering an issue during parsing.\n *\n * @public\n */\nexport class ParseError extends Error {\n  /**\n   * The type of error that occurred.\n   */\n  type: ErrorType\n\n  /**\n   * In the case of an unknown field encountered in the stream, this will be the field name.\n   */\n  field?: string\n\n  /**\n   * In the case of an unknown field encountered in the stream, this will be the value of the field.\n   */\n  value?: string\n\n  /**\n   * The line that caused the error, if available.\n   */\n  line?: string\n\n  constructor(\n    message: string,\n    options: {type: ErrorType; field?: string; value?: string; line?: string},\n  ) {\n    super(message)\n    this.name = 'ParseError'\n    this.type = options.type\n    this.field = options.field\n    this.value = options.value\n    this.line = options.line\n  }\n}\n", "/**\n * EventSource/Server-Sent Events parser\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nimport {ParseError} from './errors.ts'\nimport type {EventSourceParser, ParserCallbacks} from './types.ts'\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction noop(_arg: unknown) {\n  // intentional noop\n}\n\n/**\n * Creates a new EventSource parser.\n *\n * @param callbacks - Callbacks to invoke on different parsing events:\n *   - `onEvent` when a new event is parsed\n *   - `onError` when an error occurs\n *   - `onRetry` when a new reconnection interval has been sent from the server\n *   - `onComment` when a comment is encountered in the stream\n *\n * @returns A new EventSource parser, with `parse` and `reset` methods.\n * @public\n */\nexport function createParser(callbacks: ParserCallbacks): EventSourceParser {\n  if (typeof callbacks === 'function') {\n    throw new TypeError(\n      '`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?',\n    )\n  }\n\n  const {onEvent = noop, onError = noop, onRetry = noop, onComment} = callbacks\n\n  let incompleteLine = ''\n\n  let isFirstChunk = true\n  let id: string | undefined\n  let data = ''\n  let eventType = ''\n\n  function feed(newChunk: string) {\n    // Strip any UTF8 byte order mark (BOM) at the start of the stream\n    const chunk = isFirstChunk ? newChunk.replace(/^\\xEF\\xBB\\xBF/, '') : newChunk\n\n    // If there was a previous incomplete line, append it to the new chunk,\n    // so we may process it together as a new (hopefully complete) chunk.\n    const [complete, incomplete] = splitLines(`${incompleteLine}${chunk}`)\n\n    for (const line of complete) {\n      parseLine(line)\n    }\n\n    incompleteLine = incomplete\n    isFirstChunk = false\n  }\n\n  function parseLine(line: string) {\n    // If the line is empty (a blank line), dispatch the event\n    if (line === '') {\n      dispatchEvent()\n      return\n    }\n\n    // If the line starts with a U+003A COLON character (:), ignore the line.\n    if (line.startsWith(':')) {\n      if (onComment) {\n        onComment(line.slice(line.startsWith(': ') ? 2 : 1))\n      }\n      return\n    }\n\n    // If the line contains a U+003A COLON character (:)\n    const fieldSeparatorIndex = line.indexOf(':')\n    if (fieldSeparatorIndex !== -1) {\n      // Collect the characters on the line before the first U+003A COLON character (:),\n      // and let `field` be that string.\n      const field = line.slice(0, fieldSeparatorIndex)\n\n      // Collect the characters on the line after the first U+003A COLON character (:),\n      // and let `value` be that string. If value starts with a U+0020 SPACE character,\n      // remove it from value.\n      const offset = line[fieldSeparatorIndex + 1] === ' ' ? 2 : 1\n      const value = line.slice(fieldSeparatorIndex + offset)\n\n      processField(field, value, line)\n      return\n    }\n\n    // Otherwise, the string is not empty but does not contain a U+003A COLON character (:)\n    // Process the field using the whole line as the field name, and an empty string as the field value.\n    // 👆 This is according to spec. That means that a line that has the value `data` will result in\n    // a newline being added to the current `data` buffer, for instance.\n    processField(line, '', line)\n  }\n\n  function processField(field: string, value: string, line: string) {\n    // Field names must be compared literally, with no case folding performed.\n    switch (field) {\n      case 'event':\n        // Set the `event type` buffer to field value\n        eventType = value\n        break\n      case 'data':\n        // Append the field value to the `data` buffer, then append a single U+000A LINE FEED(LF)\n        // character to the `data` buffer.\n        data = `${data}${value}\\n`\n        break\n      case 'id':\n        // If the field value does not contain U+0000 NULL, then set the `ID` buffer to\n        // the field value. Otherwise, ignore the field.\n        id = value.includes('\\0') ? undefined : value\n        break\n      case 'retry':\n        // If the field value consists of only ASCII digits, then interpret the field value as an\n        // integer in base ten, and set the event stream's reconnection time to that integer.\n        // Otherwise, ignore the field.\n        if (/^\\d+$/.test(value)) {\n          onRetry(parseInt(value, 10))\n        } else {\n          onError(\n            new ParseError(`Invalid \\`retry\\` value: \"${value}\"`, {\n              type: 'invalid-retry',\n              value,\n              line,\n            }),\n          )\n        }\n        break\n      default:\n        // Otherwise, the field is ignored.\n        onError(\n          new ParseError(\n            `Unknown field \"${field.length > 20 ? `${field.slice(0, 20)}…` : field}\"`,\n            {type: 'unknown-field', field, value, line},\n          ),\n        )\n        break\n    }\n  }\n\n  function dispatchEvent() {\n    const shouldDispatch = data.length > 0\n    if (shouldDispatch) {\n      onEvent({\n        id,\n        event: eventType || undefined,\n        // If the data buffer's last character is a U+000A LINE FEED (LF) character,\n        // then remove the last character from the data buffer.\n        data: data.endsWith('\\n') ? data.slice(0, -1) : data,\n      })\n    }\n\n    // Reset for the next event\n    id = undefined\n    data = ''\n    eventType = ''\n  }\n\n  function reset(options: {consume?: boolean} = {}) {\n    if (incompleteLine && options.consume) {\n      parseLine(incompleteLine)\n    }\n\n    isFirstChunk = true\n    id = undefined\n    data = ''\n    eventType = ''\n    incompleteLine = ''\n  }\n\n  return {feed, reset}\n}\n\n/**\n * For the given `chunk`, split it into lines according to spec, and return any remaining incomplete line.\n *\n * @param chunk - The chunk to split into lines\n * @returns A tuple containing an array of complete lines, and any remaining incomplete line\n * @internal\n */\nfunction splitLines(chunk: string): [complete: Array<string>, incomplete: string] {\n  /**\n   * According to the spec, a line is terminated by either:\n   * - U+000D CARRIAGE RETURN U+000A LINE FEED (CRLF) character pair\n   * - a single U+000A LINE FEED(LF) character not preceded by a U+000D CARRIAGE RETURN(CR) character\n   * - a single U+000D CARRIAGE RETURN(CR) character not followed by a U+000A LINE FEED(LF) character\n   */\n  const lines: Array<string> = []\n  let incompleteLine = ''\n  let searchIndex = 0\n\n  while (searchIndex < chunk.length) {\n    // Find next line terminator\n    const crIndex = chunk.indexOf('\\r', searchIndex)\n    const lfIndex = chunk.indexOf('\\n', searchIndex)\n\n    // Determine line end\n    let lineEnd = -1\n    if (crIndex !== -1 && lfIndex !== -1) {\n      // CRLF case\n      lineEnd = Math.min(crIndex, lfIndex)\n    } else if (crIndex !== -1) {\n      lineEnd = crIndex\n    } else if (lfIndex !== -1) {\n      lineEnd = lfIndex\n    }\n\n    // Extract line if terminator found\n    if (lineEnd === -1) {\n      // No terminator found, rest is incomplete\n      incompleteLine = chunk.slice(searchIndex)\n      break\n    } else {\n      const line = chunk.slice(searchIndex, lineEnd)\n      lines.push(line)\n\n      // Move past line terminator\n      searchIndex = lineEnd + 1\n      if (chunk[searchIndex - 1] === '\\r' && chunk[searchIndex] === '\\n') {\n        searchIndex++\n      }\n    }\n  }\n\n  return [lines, incompleteLine]\n}\n", "import {createParser} from './parse.ts'\nimport type {EventSourceMessage, EventSourceParser} from './types.ts'\n\n/**\n * Options for the EventSourceParserStream.\n *\n * @public\n */\nexport interface StreamOptions {\n  /**\n   * Behavior when a parsing error occurs.\n   *\n   * - A custom function can be provided to handle the error.\n   * - `'terminate'` will error the stream and stop parsing.\n   * - Any other value will ignore the error and continue parsing.\n   *\n   * @defaultValue `undefined`\n   */\n  onError?: 'terminate' | ((error: Error) => void)\n\n  /**\n   * Callback for when a reconnection interval is sent from the server.\n   *\n   * @param retry - The number of milliseconds to wait before reconnecting.\n   */\n  onRetry?: (retry: number) => void\n\n  /**\n   * Callback for when a comment is encountered in the stream.\n   *\n   * @param comment - The comment encountered in the stream.\n   */\n  onComment?: (comment: string) => void\n}\n\n/**\n * A TransformStream that ingests a stream of strings and produces a stream of `EventSourceMessage`.\n *\n * @example Basic usage\n * ```\n * const eventStream =\n *   response.body\n *     .pipeThrough(new TextDecoderStream())\n *     .pipeThrough(new EventSourceParserStream())\n * ```\n *\n * @example Terminate stream on parsing errors\n * ```\n * const eventStream =\n *  response.body\n *   .pipeThrough(new TextDecoderStream())\n *   .pipeThrough(new EventSourceParserStream({terminateOnError: true}))\n * ```\n *\n * @public\n */\nexport class EventSourceParserStream extends TransformStream<string, EventSourceMessage> {\n  constructor({onError, onRetry, onComment}: StreamOptions = {}) {\n    let parser!: EventSourceParser\n\n    super({\n      start(controller) {\n        parser = createParser({\n          onEvent: (event) => {\n            controller.enqueue(event)\n          },\n          onError(error) {\n            if (onError === 'terminate') {\n              controller.error(error)\n            } else if (typeof onError === 'function') {\n              onError(error)\n            }\n\n            // Ignore by default\n          },\n          onRetry,\n          onComment,\n        })\n      },\n      transform(chunk) {\n        parser.feed(chunk)\n      },\n    })\n  }\n}\n\nexport {type ErrorType, ParseError} from './errors.ts'\nexport type {EventSourceMessage} from './types.ts'\n", "export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncIterator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncIterator.\n * @param { <T>} iterator - The AsyncIterator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncIterator.\n */\nexport function convertAsyncIteratorToReadableStream<T>(\n  iterator: AsyncIterator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\n * Creates a Promise that resolves after a specified delay\n * @param delayInMs - The delay duration in milliseconds. If null or undefined, resolves immediately.\n * @param signal - Optional AbortSignal to cancel the delay\n * @returns A Promise that resolves after the specified delay\n * @throws {DOMException} When the signal is aborted\n */\nexport async function delay(\n  delayInMs?: number | null,\n  options?: {\n    abortSignal?: AbortSignal;\n  },\n): Promise<void> {\n  if (delayInMs == null) {\n    return Promise.resolve();\n  }\n\n  const signal = options?.abortSignal;\n\n  return new Promise<void>((resolve, reject) => {\n    if (signal?.aborted) {\n      reject(createAbortError());\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      cleanup();\n      resolve();\n    }, delayInMs);\n\n    const cleanup = () => {\n      clearTimeout(timeoutId);\n      signal?.removeEventListener('abort', onAbort);\n    };\n\n    const onAbort = () => {\n      cleanup();\n      reject(createAbortError());\n    };\n\n    signal?.addEventListener('abort', onAbort);\n  });\n}\n\nfunction createAbortError(): DOMException {\n  return new DOMException('Delay was aborted', 'AbortError');\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(response: Response) {\n  return Object.fromEntries<string>([...response.headers]);\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\n\n/**\nCreates an ID generator.\nThe total length of the ID is the sum of the prefix, separator, and random part length.\nNot cryptographically secure.\n\n@param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n@param prefix - The prefix of the ID to generate. Optional.\n@param separator - The separator between the prefix and the random part of the ID. Default: '-'.\n@param size - The size of the random part of the ID to generate. Default: 16.\n */\nexport const createIdGenerator = ({\n  prefix,\n  size = 16,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  separator = '-',\n}: {\n  prefix?: string;\n  separator?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): IdGenerator => {\n  const generator = () => {\n    const alphabetLength = alphabet.length;\n    const chars = new Array(size);\n    for (let i = 0; i < size; i++) {\n      chars[i] = alphabet[(Math.random() * alphabetLength) | 0];\n    }\n    return chars.join('');\n  };\n\n  if (prefix == null) {\n    return generator;\n  }\n\n  // check that the prefix is not part of the alphabet (otherwise prefix checking can fail randomly)\n  if (alphabet.includes(separator)) {\n    throw new InvalidArgumentError({\n      argument: 'separator',\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`,\n    });\n  }\n\n  return () => `${prefix}${separator}${generator()}`;\n};\n\n/**\nA function that generates an ID.\n */\nexport type IdGenerator = () => string;\n\n/**\nGenerates a 16-character random string to use for IDs.\nNot cryptographically secure.\n */\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { handleFetchError } from './handle-fetch-error';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const getFromApi = async <T>({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {},\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {},\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {},\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {},\n      });\n    }\n  } catch (error) {\n    throw handleFetchError({ error, url, requestBodyValues: {} });\n  }\n};\n", "import { APICallError } from '@ai-sdk/provider';\nimport { isAbortError } from './is-abort-error';\n\nconst FETCH_FAILED_ERROR_MESSAGES = ['fetch failed', 'failed to fetch'];\n\nexport function handleFetchError({\n  error,\n  url,\n  requestBodyValues,\n}: {\n  error: unknown;\n  url: string;\n  requestBodyValues: unknown;\n}) {\n  if (isAbortError(error)) {\n    return error;\n  }\n\n  // unwrap original error when fetch failed (for easier debugging):\n  if (\n    error instanceof TypeError &&\n    FETCH_FAILED_ERROR_MESSAGES.includes(error.message.toLowerCase())\n  ) {\n    const cause = (error as any).cause;\n\n    if (cause != null) {\n      // Failed to connect to server:\n      return new APICallError({\n        message: `Cannot connect to API: ${cause.message}`,\n        cause,\n        url,\n        requestBodyValues,\n        isRetryable: true, // retry when network error\n      });\n    }\n  }\n\n  return error;\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    (error instanceof Error || error instanceof DOMException) &&\n    (error.name === 'AbortError' ||\n      error.name === 'ResponseAborted' || // Next.js\n      error.name === 'TimeoutError')\n  );\n}\n", "/**\n * Removes entries from a record where the value is null or undefined.\n * @param record - The input object whose entries may be null or undefined.\n * @returns A new object containing only entries with non-null and non-undefined values.\n */\nexport function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "/**\n * Checks if the given URL is supported natively by the model.\n *\n * @param mediaType - The media type of the URL. Case-sensitive.\n * @param url - The URL to check.\n * @param supportedUrls - A record where keys are case-sensitive media types (or '*')\n *                        and values are arrays of RegExp patterns for URLs.\n *\n * @returns `true` if the URL matches a pattern under the specific media type\n *          or the wildcard '*', `false` otherwise.\n */\nexport function isUrlSupported({\n  mediaType,\n  url,\n  supportedUrls,\n}: {\n  mediaType: string;\n  url: string;\n  supportedUrls: Record<string, RegExp[]>;\n}): boolean {\n  // standardize media type and url to lower case\n  url = url.toLowerCase();\n  mediaType = mediaType.toLowerCase();\n\n  return (\n    Object.entries(supportedUrls)\n      // standardize supported url map into lowercase prefixes:\n      .map(([key, value]) => {\n        const mediaType = key.toLowerCase();\n        return mediaType === '*' || mediaType === '*/*'\n          ? { mediaTypePrefix: '', regexes: value }\n          : { mediaTypePrefix: mediaType.replace(/\\*/, ''), regexes: value };\n      })\n      // gather all regexp pattern from matched media type prefixes:\n      .filter(({ mediaTypePrefix }) => mediaType.startsWith(mediaTypePrefix))\n      .flatMap(({ regexes }) => regexes)\n      // check if any pattern matches the url:\n      .some(pattern => pattern.test(url))\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import {\n  J<PERSON><PERSON>arseError,\n  <PERSON>SO<PERSON><PERSON><PERSON><PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport { secureJsonParse } from './secure-json-parse';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport async function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): Promise<JSONValue>;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {Promise<T>} - The parsed object.\n */\nexport async function parseJSON<T>(options: {\n  text: string;\n  schema: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<T>;\nexport async function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<T> {\n  try {\n    const value = secureJsonParse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes<T>({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isInstance(error) ||\n      TypeValidationError.isInstance(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T; rawValue: unknown }\n  | {\n      success: false;\n      error: JSONParseError | TypeValidationError;\n      rawValue: unknown;\n    };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {Promise<object>} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport async function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): Promise<ParseResult<JSONValue>>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport async function safeParseJSON<T>(options: {\n  text: string;\n  schema: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<ParseResult<T>>;\nexport async function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: z4.core.$ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<ParseResult<T>> {\n  try {\n    const value = secureJsonParse(text);\n\n    if (schema == null) {\n      return { success: true, value: value as T, rawValue: value };\n    }\n\n    return await safeValidateTypes<T>({ value, schema });\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isInstance(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n      rawValue: undefined,\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    secureJsonParse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n", "// Licensed under BSD-3-Clause (this file only)\n// Code adapted from https://github.com/fastify/secure-json-parse/blob/783fcb1b5434709466759847cec974381939673a/index.js\n//\n// Copyright (c) Vercel, Inc. (https://vercel.com)\n// Copyright (c) 2019 The Fastify Team\n// Copyright (c) 2019, Sideway Inc, and project contributors\n// All rights reserved.\n//\n// The complete list of contributors can be found at:\n// - https://github.com/hapijs/bourne/graphs/contributors\n// - https://github.com/fastify/secure-json-parse/graphs/contributors\n// - https://github.com/vercel/ai/commits/main/packages/provider-utils/src/secure-parse-json.ts\n//\n// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n//\n// 1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n//\n// 2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n//\n// 3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nconst suspectProtoRx = /\"__proto__\"\\s*:/;\nconst suspectConstructorRx = /\"constructor\"\\s*:/;\n\nfunction _parse(text: string) {\n  // Parse normally\n  const obj = JSON.parse(text);\n\n  // Ignore null and non-objects\n  if (obj === null || typeof obj !== 'object') {\n    return obj;\n  }\n\n  if (\n    suspectProtoRx.test(text) === false &&\n    suspectConstructorRx.test(text) === false\n  ) {\n    return obj;\n  }\n\n  // Scan result for proto keys\n  return filter(obj);\n}\n\nfunction filter(obj: any) {\n  let next = [obj];\n\n  while (next.length) {\n    const nodes = next;\n    next = [];\n\n    for (const node of nodes) {\n      if (Object.prototype.hasOwnProperty.call(node, '__proto__')) {\n        throw new SyntaxError('Object contains forbidden prototype property');\n      }\n\n      if (\n        Object.prototype.hasOwnProperty.call(node, 'constructor') &&\n        Object.prototype.hasOwnProperty.call(node.constructor, 'prototype')\n      ) {\n        throw new SyntaxError('Object contains forbidden prototype property');\n      }\n\n      for (const key in node) {\n        const value = node[key];\n        if (value && typeof value === 'object') {\n          next.push(value);\n        }\n      }\n    }\n  }\n  return obj;\n}\n\nexport function secureJsonParse(text: string) {\n  // Performance optimization, see https://github.com/fastify/secure-json-parse/pull/90\n  const { stackTraceLimit } = Error;\n  Error.stackTraceLimit = 0;\n  try {\n    return _parse(text);\n  } finally {\n    Error.stackTraceLimit = stackTraceLimit;\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport type { StandardSchemaV1 } from '@standard-schema/spec';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {Promise<T>} - The typed object.\n */\nexport async function validateTypes<OBJECT>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: StandardSchemaV1<unknown, OBJECT> | Validator<OBJECT>;\n}): Promise<OBJECT> {\n  const result = await safeValidateTypes({ value, schema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport async function safeValidateTypes<OBJECT>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: StandardSchemaV1<unknown, OBJECT> | Validator<OBJECT>;\n}): Promise<\n  | {\n      success: true;\n      value: OBJECT;\n      rawValue: unknown;\n    }\n  | {\n      success: false;\n      error: TypeValidationError;\n      rawValue: unknown;\n    }\n> {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as OBJECT, rawValue: value };\n    }\n\n    const result = await validator.validate(value);\n\n    if (result.success) {\n      return { success: true, value: result.value, rawValue: value };\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n      rawValue: value,\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n      rawValue: value,\n    };\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (\n    value: unknown,\n  ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?:\n    | undefined\n    | ((\n        value: unknown,\n      ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | StandardSchemaV1<unknown, OBJECT>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : standardSchemaValidator(value);\n}\n\nexport function standardSchemaValidator<OBJECT>(\n  standardSchema: StandardSchemaV1<unknown, OBJECT>,\n): Validator<OBJECT> {\n  return validator(async value => {\n    const result = await standardSchema['~standard'].validate(value);\n\n    return result.issues == null\n      ? { success: true, value: result.value }\n      : {\n          success: false,\n          error: new TypeValidationError({\n            value,\n            cause: result.issues,\n          }),\n        };\n  });\n}\n", "import {\n  EventSourceMessage,\n  EventSourceParserStream,\n} from 'eventsource-parser/stream';\nimport { ZodType } from 'zod/v4';\nimport { ParseResult, safeParseJSON } from './parse-json';\n\n/**\n * Parses a JSON event stream into a stream of parsed JSON objects.\n */\nexport function parseJsonEventStream<T>({\n  stream,\n  schema,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  schema: ZodType<T>;\n}): ReadableStream<ParseResult<T>> {\n  return stream\n    .pipeThrough(new TextDecoderStream())\n    .pipeThrough(new EventSourceParserStream())\n    .pipeThrough(\n      new TransformStream<EventSourceMessage, ParseResult<T>>({\n        async transform({ data }, controller) {\n          // ignore the 'DONE' event that e.g. OpenAI sends:\n          if (data === '[DONE]') {\n            return;\n          }\n\n          controller.enqueue(await safeParseJSON({ text: data, schema }));\n        },\n      }),\n    );\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from './validate-types';\nimport { z } from 'zod/v4';\n\nexport async function parseProviderOptions<T>({\n  provider,\n  providerOptions,\n  schema,\n}: {\n  provider: string;\n  providerOptions: Record<string, unknown> | undefined;\n  schema: z.core.$ZodType<T, any>;\n}): Promise<T | undefined> {\n  if (providerOptions?.[provider] == null) {\n    return undefined;\n  }\n\n  const parsedProviderOptions = await safeValidateTypes<T | undefined>({\n    value: providerOptions[provider],\n    schema,\n  });\n\n  if (!parsedProviderOptions.success) {\n    throw new InvalidArgumentError({\n      argument: 'providerOptions',\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error,\n    });\n  }\n\n  return parsedProviderOptions.value;\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { handleFetchError } from './handle-fetch-error';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postFormDataToApi = async <T>({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  formData: FormData;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers,\n    body: {\n      content: formData,\n      values: Object.fromEntries((formData as any).entries()),\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    throw handleFetchError({ error, url, requestBodyValues: body.values });\n  }\n};\n", "import { JSONValue, LanguageModelV2ToolResultPart } from '@ai-sdk/provider';\nimport { FlexibleSchema } from '../schema';\nimport { ModelMessage } from './model-message';\nimport { ProviderOptions } from './provider-options';\n\n/**\n * Additional options that are sent into each tool call.\n */\n// TODO AI SDK 6: rename to ToolExecutionOptions\nexport interface ToolCallOptions {\n  /**\n   * The ID of the tool call. You can use it e.g. when sending tool-call related information with stream data.\n   */\n  toolCallId: string;\n\n  /**\n   * Messages that were sent to the language model to initiate the response that contained the tool call.\n   * The messages **do not** include the system prompt nor the assistant response that contained the tool call.\n   */\n  messages: ModelMessage[];\n\n  /**\n   * An optional abort signal that indicates that the overall operation should be aborted.\n   */\n  abortSignal?: AbortSignal;\n\n  /**\n   * Additional context.\n   *\n   * Experimental (can break in patch releases).\n   */\n  experimental_context?: unknown;\n}\n\nexport type ToolExecuteFunction<INPUT, OUTPUT> = (\n  input: INPUT,\n  options: ToolCallOptions,\n) => PromiseLike<OUTPUT> | OUTPUT;\n\n// 0 extends 1 & N checks for any\n// [N] extends [never] checks for never\ntype NeverOptional<N, T> = 0 extends 1 & N\n  ? Partial<T>\n  : [N] extends [never]\n    ? Partial<Record<keyof T, undefined>>\n    : T;\n\n/**\nA tool contains the description and the schema of the input that the tool expects.\nThis enables the language model to generate the input.\n\nThe tool can also contain an optional execute function for the actual execution function of the tool.\n */\nexport type Tool<\n  INPUT extends JSONValue | unknown | never = any,\n  OUTPUT extends JSONValue | unknown | never = any,\n> = {\n  /**\nAn optional description of what the tool does.\nWill be used by the language model to decide whether to use the tool.\nNot used for provider-defined tools.\n   */\n  description?: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n   */\n  providerOptions?: ProviderOptions;\n} & NeverOptional<\n  INPUT,\n  {\n    /**\nThe schema of the input that the tool expects. The language model will use this to generate the input.\nIt is also used to validate the output of the language model.\nUse descriptions to make the input understandable for the language model.\n   */\n    inputSchema: FlexibleSchema<INPUT>;\n\n    /**\n     * Optional function that is called when the argument streaming starts.\n     * Only called when the tool is used in a streaming context.\n     */\n    onInputStart?: (options: ToolCallOptions) => void | PromiseLike<void>;\n\n    /**\n     * Optional function that is called when an argument streaming delta is available.\n     * Only called when the tool is used in a streaming context.\n     */\n    onInputDelta?: (\n      options: { inputTextDelta: string } & ToolCallOptions,\n    ) => void | PromiseLike<void>;\n\n    /**\n     * Optional function that is called when a tool call can be started,\n     * even if the execute function is not provided.\n     */\n    onInputAvailable?: (\n      options: {\n        input: [INPUT] extends [never] ? undefined : INPUT;\n      } & ToolCallOptions,\n    ) => void | PromiseLike<void>;\n  }\n> &\n  NeverOptional<\n    OUTPUT,\n    {\n      /**\nOptional conversion function that maps the tool result to an output that can be used by the language model.\n\nIf not provided, the tool result will be sent as a JSON object.\n      */\n      toModelOutput?: (\n        output: OUTPUT,\n      ) => LanguageModelV2ToolResultPart['output'];\n    } & (\n      | {\n          /**\nAn async function that is called with the arguments from the tool call and produces a result.\nIf not provided, the tool will not be executed automatically.\n\n@args is the input of the tool call.\********************* is a signal that can be used to abort the tool call.\n      */\n          execute: ToolExecuteFunction<INPUT, OUTPUT>;\n\n          outputSchema?: FlexibleSchema<OUTPUT>;\n        }\n      | {\n          outputSchema: FlexibleSchema<OUTPUT>;\n\n          execute?: never;\n        }\n    )\n  > &\n  (\n    | {\n        /**\nTool with user-defined input and output schemas.\n     */\n        type?: undefined | 'function';\n      }\n    | {\n        /**\nTool that is defined at runtime (e.g. an MCP tool).\nThe types of input and output are not known at development time.\n       */\n        type: 'dynamic';\n      }\n    | {\n        /**\nTool with provider-defined input and output schemas.\n     */\n        type: 'provider-defined';\n\n        /**\nThe ID of the tool. Should follow the format `<provider-name>.<unique-tool-name>`.\n   */\n        id: `${string}.${string}`;\n\n        /**\nThe name of the tool that the user must use in the tool set.\n */\n        name: string;\n\n        /**\nThe arguments for configuring the tool. Must match the expected arguments defined by the provider for this tool.\n     */\n        args: Record<string, unknown>;\n      }\n  );\n\n/**\n * Infer the input type of a tool.\n */\nexport type InferToolInput<TOOL extends Tool> =\n  TOOL extends Tool<infer INPUT, any> ? INPUT : never;\n\n/**\n * Infer the output type of a tool.\n */\nexport type InferToolOutput<TOOL extends Tool> =\n  TOOL extends Tool<any, infer OUTPUT> ? OUTPUT : never;\n\n/**\nHelper function for inferring the execute args of a tool.\n */\n// Note: overload order is important for auto-completion\nexport function tool<INPUT, OUTPUT>(\n  tool: Tool<INPUT, OUTPUT>,\n): Tool<INPUT, OUTPUT>;\nexport function tool<INPUT>(tool: Tool<INPUT, never>): Tool<INPUT, never>;\nexport function tool<OUTPUT>(tool: Tool<never, OUTPUT>): Tool<never, OUTPUT>;\nexport function tool(tool: Tool<never, never>): Tool<never, never>;\nexport function tool(tool: any): any {\n  return tool;\n}\n\n/**\nHelper function for defining a dynamic tool.\n */\nexport function dynamicTool(tool: {\n  description?: string;\n  providerOptions?: ProviderOptions;\n  inputSchema: FlexibleSchema<unknown>;\n  execute: ToolExecuteFunction<unknown, unknown>;\n  toModelOutput?: (output: unknown) => LanguageModelV2ToolResultPart['output'];\n}): Tool<unknown, unknown> & {\n  type: 'dynamic';\n} {\n  return { ...tool, type: 'dynamic' };\n}\n", "import { tool, Tool, ToolExecuteFunction } from './types/tool';\nimport { FlexibleSchema } from './schema';\n\nexport type ProviderDefinedToolFactory<INPUT, ARGS extends object> = <OUTPUT>(\n  options: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  },\n) => Tool<INPUT, OUTPUT>;\n\nexport function createProviderDefinedToolFactory<INPUT, ARGS extends object>({\n  id,\n  name,\n  inputSchema,\n}: {\n  id: `${string}.${string}`;\n  name: string;\n  inputSchema: FlexibleSchema<INPUT>;\n}): ProviderDefinedToolFactory<INPUT, ARGS> {\n  return <OUTPUT>({\n    execute,\n    outputSchema,\n    toModelOutput,\n    onInputStart,\n    onInputDelta,\n    onInputAvailable,\n    ...args\n  }: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    outputSchema?: FlexibleSchema<OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  }): Tool<INPUT, OUTPUT> =>\n    tool({\n      type: 'provider-defined',\n      id,\n      name,\n      args,\n      inputSchema,\n      outputSchema,\n      execute,\n      toModelOutput,\n      onInputStart,\n      onInputDelta,\n      onInputAvailable,\n    });\n}\n\nexport type ProviderDefinedToolFactoryWithOutputSchema<\n  INPUT,\n  OUTPUT,\n  ARGS extends object,\n> = (\n  options: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  },\n) => Tool<INPUT, OUTPUT>;\n\nexport function createProviderDefinedToolFactoryWithOutputSchema<\n  INPUT,\n  OUTPUT,\n  ARGS extends object,\n>({\n  id,\n  name,\n  inputSchema,\n  outputSchema,\n}: {\n  id: `${string}.${string}`;\n  name: string;\n  inputSchema: FlexibleSchema<INPUT>;\n  outputSchema: FlexibleSchema<OUTPUT>;\n}): ProviderDefinedToolFactoryWithOutputSchema<INPUT, OUTPUT, ARGS> {\n  return ({\n    execute,\n    toModelOutput,\n    onInputStart,\n    onInputDelta,\n    onInputAvailable,\n    ...args\n  }: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  }): Tool<INPUT, OUTPUT> =>\n    tool({\n      type: 'provider-defined',\n      id,\n      name,\n      args,\n      inputSchema,\n      outputSchema,\n      execute,\n      toModelOutput,\n      onInputStart,\n      onInputDelta,\n      onInputAvailable,\n    });\n}\n", "export type Resolvable<T> =\n  | T // Raw value\n  | Promise<T> // Promise of value\n  | (() => T) // Function returning value\n  | (() => Promise<T>); // Function returning promise of value\n\n/**\n * Resolves a value that could be a raw value, a Promise, a function returning a value,\n * or a function returning a Promise.\n */\nexport async function resolve<T>(value: Resolvable<T>): Promise<T> {\n  // If it's a function, call it to get the value/promise\n  if (typeof value === 'function') {\n    value = (value as Function)();\n  }\n\n  // Otherwise just resolve whatever we got (value or promise)\n  return Promise.resolve(value as T);\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport { ZodType } from 'zod/v4';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { parseJSON, ParseResult, safeParseJSON } from './parse-json';\nimport { parseJsonEventStream } from './parse-json-event-stream';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  rawValue?: unknown;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodType<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = await parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodType<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: parseJsonEventStream({\n        stream: response.body,\n        schema: chunkSchema,\n      }),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodType<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          async transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                await safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodType<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = await safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n      rawValue: parsedResult.rawValue,\n    };\n  };\n\nexport const createBinaryResponseHandler =\n  (): ResponseHandler<Uint8Array> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.body) {\n      throw new APICallError({\n        message: 'Response body is empty',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n      });\n    }\n\n    try {\n      const buffer = await response.arrayBuffer();\n      return {\n        responseHeaders,\n        value: new Uint8Array(buffer),\n      };\n    } catch (error) {\n      throw new APICallError({\n        message: 'Failed to read response as array buffer',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n        cause: error,\n      });\n    }\n  };\n\nexport const createStatusCodeErrorResponseHandler =\n  (): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n    const responseBody = await response.text();\n\n    return {\n      responseHeaders,\n      value: new APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues: requestBodyValues as Record<string, unknown>,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n      }),\n    };\n  };\n", "import { JSONSchema7 } from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { jsonSchema, Schema } from './schema';\n\nexport function zod3Schema<OBJECT>(\n  zodSchema: z3.Schema<OBJECT, z3.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  return jsonSchema(\n    zodToJsonSchema(zodSchema, {\n      $refStrategy: useReferences ? 'root' : 'none',\n      target: 'jsonSchema7', // note: openai mode breaks various gemini conversions\n    }) as JSONSchema7,\n    {\n      validate: async value => {\n        const result = await zodSchema.safeParseAsync(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n\nexport function zod4Schema<OBJECT>(\n  zodSchema: z4.core.$ZodType<OBJECT, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  const z4JSONSchema = z4.toJSONSchema(zodSchema, {\n    target: 'draft-7',\n    io: 'output',\n    reused: useReferences ? 'ref' : 'inline',\n  }) as JSONSchema7;\n\n  return jsonSchema(z4JSONSchema, {\n    validate: async value => {\n      const result = await z4.safeParseAsync(zodSchema, value);\n      return result.success\n        ? { success: true, value: result.data }\n        : { success: false, error: result.error };\n    },\n  });\n}\n\nexport function isZod4Schema(\n  zodSchema: z4.core.$ZodType<any, any> | z3.Schema<any, z3.ZodTypeDef, any>,\n): zodSchema is z4.core.$ZodType<any, any> {\n  // https://zod.dev/library-authors?id=how-to-support-zod-3-and-zod-4-simultaneously\n  return '_zod' in zodSchema;\n}\n\nexport function zodSchema<OBJECT>(\n  zodSchema:\n    | z4.core.$ZodType<OBJECT, any>\n    | z3.Schema<OBJECT, z3.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  if (isZod4Schema(zodSchema)) {\n    return zod4Schema(zodSchema, options);\n  } else {\n    return zod3Schema(zodSchema, options);\n  }\n}\n", "import { Validator, validatorSymbol, type ValidationResult } from './validator';\nimport { JSONSchema7 } from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport { zodSchema } from './zod-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol.for('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\nexport type FlexibleSchema<T> = z4.core.$ZodType<T> | z3.Schema<T> | Schema<T>;\n\nexport type InferSchema<SCHEMA> = SCHEMA extends z3.Schema\n  ? z3.infer<SCHEMA>\n  : SCHEMA extends z4.core.$ZodType\n    ? z4.infer<SCHEMA>\n    : SCHEMA extends Schema<infer T>\n      ? T\n      : never;\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>;\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema:\n    | z4.core.$ZodType<OBJECT, any>\n    | z3.Schema<OBJECT, z3.ZodTypeDef, any>\n    | Schema<OBJECT>\n    | undefined,\n): Schema<OBJECT> {\n  return schema == null\n    ? jsonSchema({\n        properties: {},\n        additionalProperties: false,\n      })\n    : isSchema(schema)\n      ? schema\n      : zodSchema(schema);\n}\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise <PERSON><PERSON><PERSON><PERSON> will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n\nexport function convertToBase64(value: string | Uint8Array): string {\n  return value instanceof Uint8Array ? convertUint8ArrayToBase64(value) : value;\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n", "export * from './combine-headers';\nexport { convertAsyncIteratorToReadableStream } from './convert-async-iterator-to-readable-stream';\nexport * from './delay';\nexport * from './extract-response-headers';\nexport * from './fetch-function';\nexport { createIdGenerator, generateId, type IdGenerator } from './generate-id';\nexport * from './get-error-message';\nexport * from './get-from-api';\nexport * from './is-abort-error';\nexport { isUrlSupported } from './is-url-supported';\nexport * from './load-api-key';\nexport { loadOptionalSetting } from './load-optional-setting';\nexport { loadSetting } from './load-setting';\nexport * from './parse-json';\nexport { parseJsonEventStream } from './parse-json-event-stream';\nexport { parseProviderOptions } from './parse-provider-options';\nexport * from './post-to-api';\nexport {\n  createProviderDefinedToolFactory,\n  type ProviderDefinedToolFactory,\n  createProviderDefinedToolFactoryWithOutputSchema,\n  type ProviderDefinedToolFactoryWithOutputSchema,\n} from './provider-defined-tool-factory';\nexport * from './remove-undefined-entries';\nexport * from './resolve';\nexport * from './response-handler';\nexport {\n  asSchema,\n  jsonSchema,\n  type FlexibleSchema,\n  type InferSchema,\n  type Schema,\n} from './schema';\nexport * from './uint8-utils';\nexport * from './validate-types';\nexport * from './validator';\nexport * from './without-trailing-slash';\nexport { zodSchema } from './zod-schema';\n\n// folder re-exports\nexport * from './types';\n\n// external re-exports\nexport * from '@standard-schema/spec';\nexport {\n  EventSourceParserStream,\n  type EventSourceMessage,\n} from 'eventsource-parser/stream';\n", "export const ignoreOverride = Symbol(\"Let zodToJsonSchema decide on which parser to use\");\nexport const jsonDescription = (jsonSchema, def) => {\n    if (def.description) {\n        try {\n            return {\n                ...jsonSchema,\n                ...JSON.parse(def.description),\n            };\n        }\n        catch { }\n    }\n    return jsonSchema;\n};\nexport const defaultOptions = {\n    name: undefined,\n    $refStrategy: \"root\",\n    basePath: [\"#\"],\n    effectStrategy: \"input\",\n    pipeStrategy: \"all\",\n    dateStrategy: \"format:date-time\",\n    mapStrategy: \"entries\",\n    removeAdditionalStrategy: \"passthrough\",\n    allowedAdditionalProperties: true,\n    rejectedAdditionalProperties: false,\n    definitionPath: \"definitions\",\n    target: \"jsonSchema7\",\n    strictUnions: false,\n    definitions: {},\n    errorMessages: false,\n    markdownDescription: false,\n    patternStrategy: \"escape\",\n    applyRegexFlags: false,\n    emailStrategy: \"format:email\",\n    base64Strategy: \"contentEncoding:base64\",\n    nameStrategy: \"ref\",\n    openAiAnyTypeName: \"OpenAiAnyType\"\n};\nexport const getDefaultOptions = (options) => (typeof options === \"string\"\n    ? {\n        ...defaultOptions,\n        name: options,\n    }\n    : {\n        ...defaultOptions,\n        ...options,\n    });\n", "import { Zod<PERSON>irstPartyTypeKind } from \"zod\";\nimport { parseAnyDef } from \"./parsers/any.js\";\nimport { parseArrayDef } from \"./parsers/array.js\";\nimport { parseBigintDef } from \"./parsers/bigint.js\";\nimport { parseBooleanDef } from \"./parsers/boolean.js\";\nimport { parseBrandedDef } from \"./parsers/branded.js\";\nimport { parseCatchDef } from \"./parsers/catch.js\";\nimport { parseDateDef } from \"./parsers/date.js\";\nimport { parseDefaultDef } from \"./parsers/default.js\";\nimport { parseEffectsDef } from \"./parsers/effects.js\";\nimport { parseEnumDef } from \"./parsers/enum.js\";\nimport { parseIntersectionDef } from \"./parsers/intersection.js\";\nimport { parseLiteralDef } from \"./parsers/literal.js\";\nimport { parseMapDef } from \"./parsers/map.js\";\nimport { parseNativeEnumDef } from \"./parsers/nativeEnum.js\";\nimport { parseNeverDef } from \"./parsers/never.js\";\nimport { parseNullDef } from \"./parsers/null.js\";\nimport { parseNullableDef } from \"./parsers/nullable.js\";\nimport { parseNumberDef } from \"./parsers/number.js\";\nimport { parseObjectDef } from \"./parsers/object.js\";\nimport { parseOptionalDef } from \"./parsers/optional.js\";\nimport { parsePipelineDef } from \"./parsers/pipeline.js\";\nimport { parsePromiseDef } from \"./parsers/promise.js\";\nimport { parseRecordDef } from \"./parsers/record.js\";\nimport { parseSetDef } from \"./parsers/set.js\";\nimport { parseStringDef } from \"./parsers/string.js\";\nimport { parseTupleDef } from \"./parsers/tuple.js\";\nimport { parseUndefinedDef } from \"./parsers/undefined.js\";\nimport { parseUnionDef } from \"./parsers/union.js\";\nimport { parseUnknownDef } from \"./parsers/unknown.js\";\nimport { parseReadonlyDef } from \"./parsers/readonly.js\";\nexport const selectParser = (def, typeName, refs) => {\n    switch (typeName) {\n        case ZodFirstPartyTypeKind.ZodString:\n            return parseStringDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodNumber:\n            return parseNumberDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodObject:\n            return parseObjectDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBigInt:\n            return parseBigintDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBoolean:\n            return parseBooleanDef();\n        case ZodFirstPartyTypeKind.ZodDate:\n            return parseDateDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodUndefined:\n            return parseUndefinedDef(refs);\n        case ZodFirstPartyTypeKind.ZodNull:\n            return parseNullDef(refs);\n        case ZodFirstPartyTypeKind.ZodArray:\n            return parseArrayDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodUnion:\n        case ZodFirstPartyTypeKind.ZodDiscriminatedUnion:\n            return parseUnionDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodIntersection:\n            return parseIntersectionDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodTuple:\n            return parseTupleDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodRecord:\n            return parseRecordDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodLiteral:\n            return parseLiteralDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodEnum:\n            return parseEnumDef(def);\n        case ZodFirstPartyTypeKind.ZodNativeEnum:\n            return parseNativeEnumDef(def);\n        case ZodFirstPartyTypeKind.ZodNullable:\n            return parseNullableDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodOptional:\n            return parseOptionalDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodMap:\n            return parseMapDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodSet:\n            return parseSetDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodLazy:\n            return () => def.getter()._def;\n        case ZodFirstPartyTypeKind.ZodPromise:\n            return parsePromiseDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodNaN:\n        case ZodFirstPartyTypeKind.ZodNever:\n            return parseNeverDef(refs);\n        case ZodFirstPartyTypeKind.ZodEffects:\n            return parseEffectsDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodAny:\n            return parseAnyDef(refs);\n        case ZodFirstPartyTypeKind.ZodUnknown:\n            return parseUnknownDef(refs);\n        case ZodFirstPartyTypeKind.ZodDefault:\n            return parseDefaultDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBranded:\n            return parseBrandedDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodReadonly:\n            return parseReadonlyDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodCatch:\n            return parseCatchDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodPipeline:\n            return parsePipelineDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodFunction:\n        case ZodFirstPartyTypeKind.ZodVoid:\n        case ZodFirstPartyTypeKind.ZodSymbol:\n            return undefined;\n        default:\n            /* c8 ignore next */\n            return ((_) => undefined)(typeName);\n    }\n};\n", "import { ZodFirstPartyTypeKind } from \"zod\";\nimport { setResponseValueAndErrors } from \"../errorMessages.js\";\nimport { parseDef } from \"../parseDef.js\";\nexport function parseArrayDef(def, refs) {\n    const res = {\n        type: \"array\",\n    };\n    if (def.type?._def &&\n        def.type?._def?.typeName !== ZodFirstPartyTypeKind.ZodAny) {\n        res.items = parseDef(def.type._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"items\"],\n        });\n    }\n    if (def.minLength) {\n        setResponseValueAndErrors(res, \"minItems\", def.minLength.value, def.minLength.message, refs);\n    }\n    if (def.maxLength) {\n        setResponseValueAndErrors(res, \"maxItems\", def.maxLength.value, def.maxLength.message, refs);\n    }\n    if (def.exactLength) {\n        setResponseValueAndErrors(res, \"minItems\", def.exactLength.value, def.exactLength.message, refs);\n        setResponseValueAndErrors(res, \"maxItems\", def.exactLength.value, def.exactLength.message, refs);\n    }\n    return res;\n}\n", "import { ZodFirstPartyTypeKind, } from \"zod\";\nimport { parseDef } from \"../parseDef.js\";\nimport { parseStringDef } from \"./string.js\";\nimport { parseBrandedDef } from \"./branded.js\";\nimport { parseAnyDef } from \"./any.js\";\nexport function parseRecordDef(def, refs) {\n    if (refs.target === \"openAi\") {\n        console.warn(\"Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead.\");\n    }\n    if (refs.target === \"openApi3\" &&\n        def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            type: \"object\",\n            required: def.keyType._def.values,\n            properties: def.keyType._def.values.reduce((acc, key) => ({\n                ...acc,\n                [key]: parseDef(def.valueType._def, {\n                    ...refs,\n                    currentPath: [...refs.currentPath, \"properties\", key],\n                }) ?? parseAnyDef(refs),\n            }), {}),\n            additionalProperties: refs.rejectedAdditionalProperties,\n        };\n    }\n    const schema = {\n        type: \"object\",\n        additionalProperties: parseDef(def.valueType._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"additionalProperties\"],\n        }) ?? refs.allowedAdditionalProperties,\n    };\n    if (refs.target === \"openApi3\") {\n        return schema;\n    }\n    if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.checks?.length) {\n        const { type, ...keyType } = parseStringDef(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            ...schema,\n            propertyNames: {\n                enum: def.keyType._def.values,\n            },\n        };\n    }\n    else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodBranded &&\n        def.keyType._def.type._def.typeName === ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.type._def.checks?.length) {\n        const { type, ...keyType } = parseBrandedDef(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    return schema;\n}\n", "import { setResponseValueAndErrors } from \"../errorMessages.js\";\nlet emojiRegex = undefined;\n/**\n * Generated from the regular expressions found here as of 2024-05-22:\n * https://github.com/colinhacks/zod/blob/master/src/types.ts.\n *\n * Expressions with /i flag have been changed accordingly.\n */\nexport const zodPatterns = {\n    /**\n     * `c` was changed to `[cC]` to replicate /i flag\n     */\n    cuid: /^[cC][^\\s-]{8,}$/,\n    cuid2: /^[0-9a-z]+$/,\n    ulid: /^[0-9A-HJKMNP-TV-Z]{26}$/,\n    /**\n     * `a-z` was added to replicate /i flag\n     */\n    email: /^(?!\\.)(?!.*\\.\\.)([a-zA-Z0-9_'+\\-\\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\\-]*\\.)+[a-zA-Z]{2,}$/,\n    /**\n     * Constructed a valid Unicode RegExp\n     *\n     * Lazily instantiate since this type of regex isn't supported\n     * in all envs (e.g. React Native).\n     *\n     * See:\n     * https://github.com/colinhacks/zod/issues/2433\n     * Fix in Zod:\n     * https://github.com/colinhacks/zod/commit/9340fd51e48576a75adc919bff65dbc4a5d4c99b\n     */\n    emoji: () => {\n        if (emojiRegex === undefined) {\n            emojiRegex = RegExp(\"^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$\", \"u\");\n        }\n        return emojiRegex;\n    },\n    /**\n     * Unused\n     */\n    uuid: /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/,\n    /**\n     * Unused\n     */\n    ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,\n    ipv4Cidr: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/(3[0-2]|[12]?[0-9])$/,\n    /**\n     * Unused\n     */\n    ipv6: /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,\n    ipv6Cidr: /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,\n    base64: /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,\n    base64url: /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,\n    nanoid: /^[a-zA-Z0-9_-]{21}$/,\n    jwt: /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/,\n};\nexport function parseStringDef(def, refs) {\n    const res = {\n        type: \"string\",\n    };\n    if (def.checks) {\n        for (const check of def.checks) {\n            switch (check.kind) {\n                case \"min\":\n                    setResponseValueAndErrors(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"max\":\n                    setResponseValueAndErrors(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"email\":\n                    switch (refs.emailStrategy) {\n                        case \"format:email\":\n                            addFormat(res, \"email\", check.message, refs);\n                            break;\n                        case \"format:idn-email\":\n                            addFormat(res, \"idn-email\", check.message, refs);\n                            break;\n                        case \"pattern:zod\":\n                            addPattern(res, zodPatterns.email, check.message, refs);\n                            break;\n                    }\n                    break;\n                case \"url\":\n                    addFormat(res, \"uri\", check.message, refs);\n                    break;\n                case \"uuid\":\n                    addFormat(res, \"uuid\", check.message, refs);\n                    break;\n                case \"regex\":\n                    addPattern(res, check.regex, check.message, refs);\n                    break;\n                case \"cuid\":\n                    addPattern(res, zodPatterns.cuid, check.message, refs);\n                    break;\n                case \"cuid2\":\n                    addPattern(res, zodPatterns.cuid2, check.message, refs);\n                    break;\n                case \"startsWith\":\n                    addPattern(res, RegExp(`^${escapeLiteralCheckValue(check.value, refs)}`), check.message, refs);\n                    break;\n                case \"endsWith\":\n                    addPattern(res, RegExp(`${escapeLiteralCheckValue(check.value, refs)}$`), check.message, refs);\n                    break;\n                case \"datetime\":\n                    addFormat(res, \"date-time\", check.message, refs);\n                    break;\n                case \"date\":\n                    addFormat(res, \"date\", check.message, refs);\n                    break;\n                case \"time\":\n                    addFormat(res, \"time\", check.message, refs);\n                    break;\n                case \"duration\":\n                    addFormat(res, \"duration\", check.message, refs);\n                    break;\n                case \"length\":\n                    setResponseValueAndErrors(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    setResponseValueAndErrors(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"includes\": {\n                    addPattern(res, RegExp(escapeLiteralCheckValue(check.value, refs)), check.message, refs);\n                    break;\n                }\n                case \"ip\": {\n                    if (check.version !== \"v6\") {\n                        addFormat(res, \"ipv4\", check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addFormat(res, \"ipv6\", check.message, refs);\n                    }\n                    break;\n                }\n                case \"base64url\":\n                    addPattern(res, zodPatterns.base64url, check.message, refs);\n                    break;\n                case \"jwt\":\n                    addPattern(res, zodPatterns.jwt, check.message, refs);\n                    break;\n                case \"cidr\": {\n                    if (check.version !== \"v6\") {\n                        addPattern(res, zodPatterns.ipv4Cidr, check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addPattern(res, zodPatterns.ipv6Cidr, check.message, refs);\n                    }\n                    break;\n                }\n                case \"emoji\":\n                    addPattern(res, zodPatterns.emoji(), check.message, refs);\n                    break;\n                case \"ulid\": {\n                    addPattern(res, zodPatterns.ulid, check.message, refs);\n                    break;\n                }\n                case \"base64\": {\n                    switch (refs.base64Strategy) {\n                        case \"format:binary\": {\n                            addFormat(res, \"binary\", check.message, refs);\n                            break;\n                        }\n                        case \"contentEncoding:base64\": {\n                            setResponseValueAndErrors(res, \"contentEncoding\", \"base64\", check.message, refs);\n                            break;\n                        }\n                        case \"pattern:zod\": {\n                            addPattern(res, zodPatterns.base64, check.message, refs);\n                            break;\n                        }\n                    }\n                    break;\n                }\n                case \"nanoid\": {\n                    addPattern(res, zodPatterns.nanoid, check.message, refs);\n                }\n                case \"toLowerCase\":\n                case \"toUpperCase\":\n                case \"trim\":\n                    break;\n                default:\n                    /* c8 ignore next */\n                    ((_) => { })(check);\n            }\n        }\n    }\n    return res;\n}\nfunction escapeLiteralCheckValue(literal, refs) {\n    return refs.patternStrategy === \"escape\"\n        ? escapeNonAlphaNumeric(literal)\n        : literal;\n}\nconst ALPHA_NUMERIC = new Set(\"ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789\");\nfunction escapeNonAlphaNumeric(source) {\n    let result = \"\";\n    for (let i = 0; i < source.length; i++) {\n        if (!ALPHA_NUMERIC.has(source[i])) {\n            result += \"\\\\\";\n        }\n        result += source[i];\n    }\n    return result;\n}\n// Adds a \"format\" keyword to the schema. If a format exists, both formats will be joined in an allOf-node, along with subsequent ones.\nfunction addFormat(schema, value, message, refs) {\n    if (schema.format || schema.anyOf?.some((x) => x.format)) {\n        if (!schema.anyOf) {\n            schema.anyOf = [];\n        }\n        if (schema.format) {\n            schema.anyOf.push({\n                format: schema.format,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { format: schema.errorMessage.format },\n                }),\n            });\n            delete schema.format;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.format;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.anyOf.push({\n            format: value,\n            ...(message &&\n                refs.errorMessages && { errorMessage: { format: message } }),\n        });\n    }\n    else {\n        setResponseValueAndErrors(schema, \"format\", value, message, refs);\n    }\n}\n// Adds a \"pattern\" keyword to the schema. If a pattern exists, both patterns will be joined in an allOf-node, along with subsequent ones.\nfunction addPattern(schema, regex, message, refs) {\n    if (schema.pattern || schema.allOf?.some((x) => x.pattern)) {\n        if (!schema.allOf) {\n            schema.allOf = [];\n        }\n        if (schema.pattern) {\n            schema.allOf.push({\n                pattern: schema.pattern,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { pattern: schema.errorMessage.pattern },\n                }),\n            });\n            delete schema.pattern;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.pattern;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.allOf.push({\n            pattern: stringifyRegExpWithFlags(regex, refs),\n            ...(message &&\n                refs.errorMessages && { errorMessage: { pattern: message } }),\n        });\n    }\n    else {\n        setResponseValueAndErrors(schema, \"pattern\", stringifyRegExpWithFlags(regex, refs), message, refs);\n    }\n}\n// Mutate z.string.regex() in a best attempt to accommodate for regex flags when applyRegexFlags is true\nfunction stringifyRegExpWithFlags(regex, refs) {\n    if (!refs.applyRegexFlags || !regex.flags) {\n        return regex.source;\n    }\n    // Currently handled flags\n    const flags = {\n        i: regex.flags.includes(\"i\"),\n        m: regex.flags.includes(\"m\"),\n        s: regex.flags.includes(\"s\"), // `.` matches newlines\n    };\n    // The general principle here is to step through each character, one at a time, applying mutations as flags require. We keep track when the current character is escaped, and when it's inside a group /like [this]/ or (also) a range like /[a-z]/. The following is fairly brittle imperative code; edit at your peril!\n    const source = flags.i ? regex.source.toLowerCase() : regex.source;\n    let pattern = \"\";\n    let isEscaped = false;\n    let inCharGroup = false;\n    let inCharRange = false;\n    for (let i = 0; i < source.length; i++) {\n        if (isEscaped) {\n            pattern += source[i];\n            isEscaped = false;\n            continue;\n        }\n        if (flags.i) {\n            if (inCharGroup) {\n                if (source[i].match(/[a-z]/)) {\n                    if (inCharRange) {\n                        pattern += source[i];\n                        pattern += `${source[i - 2]}-${source[i]}`.toUpperCase();\n                        inCharRange = false;\n                    }\n                    else if (source[i + 1] === \"-\" && source[i + 2]?.match(/[a-z]/)) {\n                        pattern += source[i];\n                        inCharRange = true;\n                    }\n                    else {\n                        pattern += `${source[i]}${source[i].toUpperCase()}`;\n                    }\n                    continue;\n                }\n            }\n            else if (source[i].match(/[a-z]/)) {\n                pattern += `[${source[i]}${source[i].toUpperCase()}]`;\n                continue;\n            }\n        }\n        if (flags.m) {\n            if (source[i] === \"^\") {\n                pattern += `(^|(?<=[\\r\\n]))`;\n                continue;\n            }\n            else if (source[i] === \"$\") {\n                pattern += `($|(?=[\\r\\n]))`;\n                continue;\n            }\n        }\n        if (flags.s && source[i] === \".\") {\n            pattern += inCharGroup ? `${source[i]}\\r\\n` : `[${source[i]}\\r\\n]`;\n            continue;\n        }\n        pattern += source[i];\n        if (source[i] === \"\\\\\") {\n            isEscaped = true;\n        }\n        else if (inCharGroup && source[i] === \"]\") {\n            inCharGroup = false;\n        }\n        else if (!inCharGroup && source[i] === \"[\") {\n            inCharGroup = true;\n        }\n    }\n    try {\n        new RegExp(pattern);\n    }\n    catch {\n        console.warn(`Could not convert regex pattern at ${refs.currentPath.join(\"/\")} to a flag-independent form! Falling back to the flag-ignorant source`);\n        return regex.source;\n    }\n    return pattern;\n}\n", "import { z } from 'zod/v4';\n\nexport enum ReasoningDetailType {\n  Summary = 'reasoning.summary',\n  Encrypted = 'reasoning.encrypted',\n  Text = 'reasoning.text',\n}\n\nexport const ReasoningDetailSummarySchema = z.object({\n  type: z.literal(ReasoningDetailType.Summary),\n  summary: z.string(),\n});\nexport type ReasoningDetailSummary = z.infer<\n  typeof ReasoningDetailSummarySchema\n>;\n\nexport const ReasoningDetailEncryptedSchema = z.object({\n  type: z.literal(ReasoningDetailType.Encrypted),\n  data: z.string(),\n});\nexport type ReasoningDetailEncrypted = z.infer<\n  typeof ReasoningDetailEncryptedSchema\n>;\n\nexport const ReasoningDetailTextSchema = z.object({\n  type: z.literal(ReasoningDetailType.Text),\n  text: z.string().nullish(),\n  signature: z.string().nullish(),\n});\n\nexport type ReasoningDetailText = z.infer<typeof ReasoningDetailTextSchema>;\n\nexport const ReasoningDetailUnionSchema = z.union([\n  ReasoningDetailSummarySchema,\n  ReasoningDetailEncryptedSchema,\n  ReasoningDetailTextSchema,\n]);\n\nconst ReasoningDetailsWithUnknownSchema = z.union([\n  ReasoningDetailUnionSchema,\n  z.unknown().transform(() => null),\n]);\n\nexport type ReasoningDetailUnion = z.infer<typeof ReasoningDetailUnionSchema>;\n\nexport const ReasoningDetailArraySchema = z\n  .array(ReasoningDetailsWithUnknownSchema)\n  .transform((d) => d.filter((d): d is ReasoningDetailUnion => !!d));\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod/v4';\n\nexport const OpenRouterErrorResponseSchema = z.object({\n  error: z.object({\n    code: z.union([z.string(), z.number()]).nullable().optional().default(null),\n    message: z.string(),\n    type: z.string().nullable().optional().default(null),\n    param: z.any().nullable().optional().default(null),\n  }),\n});\n\nexport type OpenRouterErrorData = z.infer<typeof OpenRouterErrorResponseSchema>;\n\nexport const openrouterFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: OpenRouterErrorResponseSchema,\n  errorToMessage: (data: OpenRouterErrorData) => data.error.message,\n});\n", "import type { LanguageModelV2FinishReason } from '@ai-sdk/provider';\n\nexport function mapOpenRouterFinishReason(\n  finishReason: string | null | undefined,\n): LanguageModelV2FinishReason {\n  switch (finishReason) {\n    case 'stop':\n      return 'stop';\n    case 'length':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    case 'function_call':\n    case 'tool_calls':\n      return 'tool-calls';\n    default:\n      return 'unknown';\n  }\n}\n", "export function isUrl({\n  url,\n  protocols,\n}: {\n  url: string | URL;\n  protocols: Set<`${string}:`>;\n}): boolean {\n  try {\n    const urlObj = new URL(url);\n    // Cast to the literal string due to Set inferred input type\n    return protocols.has(urlObj.protocol as `${string}:`);\n  } catch (_) {\n    return false;\n  }\n}\n", "import type { LanguageModelV2FilePart } from '@ai-sdk/provider';\n\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport { isUrl } from './is-url';\n\nexport function getFileUrl({\n  part,\n  defaultMediaType,\n}: {\n  part: LanguageModelV2FilePart;\n  defaultMediaType: string;\n}) {\n  if (part.data instanceof Uint8Array) {\n    const base64 = convertUint8ArrayToBase64(part.data);\n    return `data:${part.mediaType ?? defaultMediaType};base64,${base64}`;\n  }\n\n  const stringUrl = part.data.toString();\n\n  if (\n    isUrl({\n      url: stringUrl,\n      protocols: new Set(['http:', 'https:']),\n    })\n  ) {\n    return stringUrl;\n  }\n\n  return stringUrl.startsWith('data:')\n    ? stringUrl\n    : `data:${part.mediaType ?? defaultMediaType};base64,${stringUrl}`;\n}\n", "import type {\n  LanguageModelV2FilePart,\n  LanguageModelV2Prompt,\n  LanguageModelV2TextPart,\n  LanguageModelV2ToolResultPart,\n  SharedV2ProviderMetadata,\n} from '@ai-sdk/provider';\nimport type { ReasoningDetailUnion } from '@/src/schemas/reasoning-details';\nimport type {\n  ChatCompletionContentPart,\n  OpenRouterChatCompletionsInput,\n} from '../types/openrouter-chat-completions-input';\n\nimport { ReasoningDetailType } from '@/src/schemas/reasoning-details';\nimport { getFileUrl } from './file-url-utils';\nimport { isUrl } from './is-url';\n\n// Type for OpenRouter Cache Control following Anthropic's pattern\nexport type OpenRouterCacheControl = { type: 'ephemeral' };\n\nfunction getCacheControl(\n  providerMetadata: SharedV2ProviderMetadata | undefined,\n): OpenRouterCacheControl | undefined {\n  const anthropic = providerMetadata?.anthropic;\n  const openrouter = providerMetadata?.openrouter;\n\n  // Allow both cacheControl and cache_control:\n  return (openrouter?.cacheControl ??\n    openrouter?.cache_control ??\n    anthropic?.cacheControl ??\n    anthropic?.cache_control) as OpenRouterCacheControl | undefined;\n}\n\nexport function convertToOpenRouterChatMessages(\n  prompt: LanguageModelV2Prompt,\n): OpenRouterChatCompletionsInput {\n  const messages: OpenRouterChatCompletionsInput = [];\n  for (const { role, content, providerOptions } of prompt) {\n    switch (role) {\n      case 'system': {\n        messages.push({\n          role: 'system',\n          content,\n          cache_control: getCacheControl(providerOptions),\n        });\n        break;\n      }\n\n      case 'user': {\n        if (content.length === 1 && content[0]?.type === 'text') {\n          const cacheControl =\n            getCacheControl(providerOptions) ??\n            getCacheControl(content[0].providerOptions);\n          const contentWithCacheControl: string | ChatCompletionContentPart[] =\n            cacheControl\n              ? [\n                  {\n                    type: 'text',\n                    text: content[0].text,\n                    cache_control: cacheControl,\n                  },\n                ]\n              : content[0].text;\n          messages.push({\n            role: 'user',\n            content: contentWithCacheControl,\n          });\n          break;\n        }\n\n        // Get message level cache control\n        const messageCacheControl = getCacheControl(providerOptions);\n        const contentParts: ChatCompletionContentPart[] = content.map(\n          (part: LanguageModelV2TextPart | LanguageModelV2FilePart) => {\n            const cacheControl =\n              getCacheControl(part.providerOptions) ?? messageCacheControl;\n\n            switch (part.type) {\n              case 'text':\n                return {\n                  type: 'text' as const,\n                  text: part.text,\n                  // For text parts, only use part-specific cache control\n                  cache_control: cacheControl,\n                };\n              case 'file': {\n                if (part.mediaType?.startsWith('image/')) {\n                  const url = getFileUrl({\n                    part,\n                    defaultMediaType: 'image/jpeg',\n                  });\n                  return {\n                    type: 'image_url' as const,\n                    image_url: {\n                      url,\n                    },\n                    // For image parts, use part-specific or message-level cache control\n                    cache_control: cacheControl,\n                  };\n                }\n\n                const fileName = String(\n                  part.providerOptions?.openrouter?.filename ??\n                    part.filename ??\n                    '',\n                );\n\n                const fileData = getFileUrl({\n                  part,\n                  defaultMediaType: 'application/pdf',\n                });\n\n                if (\n                  isUrl({\n                    url: fileData,\n                    protocols: new Set(['http:', 'https:']),\n                  })\n                ) {\n                  return {\n                    type: 'file' as const,\n                    file: {\n                      filename: fileName,\n                      file_data: fileData,\n                    },\n                  } satisfies ChatCompletionContentPart;\n                }\n\n                return {\n                  type: 'file' as const,\n                  file: {\n                    filename: fileName,\n                    file_data: fileData,\n                  },\n                  cache_control: cacheControl,\n                } satisfies ChatCompletionContentPart;\n              }\n              default: {\n                return {\n                  type: 'text' as const,\n                  text: '',\n                  cache_control: cacheControl,\n                };\n              }\n            }\n          },\n        );\n\n        // For multi-part messages, don't add cache_control at the root level\n        messages.push({\n          role: 'user',\n          content: contentParts,\n        });\n\n        break;\n      }\n\n      case 'assistant': {\n        let text = '';\n        let reasoning = '';\n        const reasoningDetails: ReasoningDetailUnion[] = [];\n        const toolCalls: Array<{\n          id: string;\n          type: 'function';\n          function: { name: string; arguments: string };\n        }> = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              text += part.text;\n              break;\n            }\n            case 'tool-call': {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: 'function',\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.input),\n                },\n              });\n              break;\n            }\n            case 'reasoning': {\n              reasoning += part.text;\n              reasoningDetails.push({\n                type: ReasoningDetailType.Text,\n                text: part.text,\n              });\n\n              break;\n            }\n\n            case 'file':\n              break;\n            default: {\n              break;\n            }\n          }\n        }\n\n        messages.push({\n          role: 'assistant',\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,\n          reasoning: reasoning || undefined,\n          reasoning_details:\n            reasoningDetails.length > 0 ? reasoningDetails : undefined,\n          cache_control: getCacheControl(providerOptions),\n        });\n\n        break;\n      }\n\n      case 'tool': {\n        for (const toolResponse of content) {\n          const content = getToolResultContent(toolResponse);\n\n          messages.push({\n            role: 'tool',\n            tool_call_id: toolResponse.toolCallId,\n            content,\n            cache_control:\n              getCacheControl(providerOptions) ??\n              getCacheControl(toolResponse.providerOptions),\n          });\n        }\n        break;\n      }\n\n      default: {\n        break;\n      }\n    }\n  }\n\n  return messages;\n}\n\nfunction getToolResultContent(input: LanguageModelV2ToolResultPart): string {\n  return input.output.type === 'text'\n    ? input.output.value\n    : JSON.stringify(input.output.value);\n}\n", "import type { LanguageModelV2ToolChoice } from '@ai-sdk/provider';\n\nimport { z } from 'zod/v4';\n\nconst ChatCompletionToolChoiceSchema = z.union([\n  z.literal('auto'),\n  z.literal('none'),\n  z.literal('required'),\n  z.object({\n    type: z.literal('function'),\n    function: z.object({\n      name: z.string(),\n    }),\n  }),\n]);\n\ntype ChatCompletionToolChoice = z.infer<typeof ChatCompletionToolChoiceSchema>;\n\nexport function getChatCompletionToolChoice(\n  toolChoice: LanguageModelV2ToolChoice,\n): ChatCompletionToolChoice {\n  switch (toolChoice.type) {\n    case 'auto':\n    case 'none':\n    case 'required':\n      return toolChoice.type;\n    case 'tool': {\n      return {\n        type: 'function',\n        function: { name: toolChoice.toolName },\n      };\n    }\n    default: {\n      toolChoice satisfies never;\n      throw new Error(`Invalid tool choice type: ${toolChoice}`);\n    }\n  }\n}\n", "import { z } from 'zod/v4';\nimport { OpenRouterErrorResponseSchema } from '../schemas/error-response';\nimport { ReasoningDetailArraySchema } from '../schemas/reasoning-details';\n\nconst OpenRouterChatCompletionBaseResponseSchema = z.object({\n  id: z.string().optional(),\n  model: z.string().optional(),\n  provider: z.string().optional(),\n  usage: z\n    .object({\n      prompt_tokens: z.number(),\n      prompt_tokens_details: z\n        .object({\n          cached_tokens: z.number(),\n        })\n        .nullish(),\n      completion_tokens: z.number(),\n      completion_tokens_details: z\n        .object({\n          reasoning_tokens: z.number(),\n        })\n        .nullish(),\n      total_tokens: z.number(),\n      cost: z.number().optional(),\n      cost_details: z\n        .object({\n          upstream_inference_cost: z.number().nullish(),\n        })\n        .nullish(),\n    })\n    .nullish(),\n});\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nexport const OpenRouterNonStreamChatCompletionResponseSchema =\n  OpenRouterChatCompletionBaseResponseSchema.extend({\n    choices: z.array(\n      z.object({\n        message: z.object({\n          role: z.literal('assistant'),\n          content: z.string().nullable().optional(),\n          reasoning: z.string().nullable().optional(),\n          reasoning_details: ReasoningDetailArraySchema.nullish(),\n\n          tool_calls: z\n            .array(\n              z.object({\n                id: z.string().optional().nullable(),\n                type: z.literal('function'),\n                function: z.object({\n                  name: z.string(),\n                  arguments: z.string(),\n                }),\n              }),\n            )\n            .optional(),\n\n          annotations: z\n            .array(\n              z.object({\n                type: z.enum(['url_citation']),\n                url_citation: z.object({\n                  end_index: z.number(),\n                  start_index: z.number(),\n                  title: z.string(),\n                  url: z.string(),\n                  content: z.string().optional(),\n                }),\n              }),\n            )\n            .nullish(),\n        }),\n        index: z.number().nullish(),\n        logprobs: z\n          .object({\n            content: z\n              .array(\n                z.object({\n                  token: z.string(),\n                  logprob: z.number(),\n                  top_logprobs: z.array(\n                    z.object({\n                      token: z.string(),\n                      logprob: z.number(),\n                    }),\n                  ),\n                }),\n              )\n              .nullable(),\n          })\n          .nullable()\n          .optional(),\n        finish_reason: z.string().optional().nullable(),\n      }),\n    ),\n  });\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nexport const OpenRouterStreamChatCompletionChunkSchema = z.union([\n  OpenRouterChatCompletionBaseResponseSchema.extend({\n    choices: z.array(\n      z.object({\n        delta: z\n          .object({\n            role: z.enum(['assistant']).optional(),\n            content: z.string().nullish(),\n            reasoning: z.string().nullish().optional(),\n            reasoning_details: ReasoningDetailArraySchema.nullish(),\n            tool_calls: z\n              .array(\n                z.object({\n                  index: z.number().nullish(),\n                  id: z.string().nullish(),\n                  type: z.literal('function').optional(),\n                  function: z.object({\n                    name: z.string().nullish(),\n                    arguments: z.string().nullish(),\n                  }),\n                }),\n              )\n              .nullish(),\n\n            annotations: z\n              .array(\n                z.object({\n                  type: z.enum(['url_citation']),\n                  url_citation: z.object({\n                    end_index: z.number(),\n                    start_index: z.number(),\n                    title: z.string(),\n                    url: z.string(),\n                    content: z.string().optional(),\n                  }),\n                }),\n              )\n              .nullish(),\n          })\n          .nullish(),\n        logprobs: z\n          .object({\n            content: z\n              .array(\n                z.object({\n                  token: z.string(),\n                  logprob: z.number(),\n                  top_logprobs: z.array(\n                    z.object({\n                      token: z.string(),\n                      logprob: z.number(),\n                    }),\n                  ),\n                }),\n              )\n              .nullable(),\n          })\n          .nullish(),\n        finish_reason: z.string().nullable().optional(),\n        index: z.number().nullish(),\n      }),\n    ),\n  }),\n  OpenRouterErrorResponseSchema,\n]);\n", "import type {\n  LanguageModelV2,\n  LanguageModelV2CallOptions,\n  LanguageModelV2CallWarning,\n  LanguageModelV2Content,\n  LanguageModelV2FinishReason,\n  LanguageModelV2ResponseMetadata,\n  LanguageModelV2StreamPart,\n  LanguageModelV2Usage,\n  SharedV2Headers,\n} from '@ai-sdk/provider';\nimport type { ParseResult } from '@ai-sdk/provider-utils';\nimport type { FinishReason } from 'ai';\nimport type { z } from 'zod/v4';\nimport type { OpenRouterUsageAccounting } from '@/src/types/index';\nimport type {\n  OpenRouterChatModelId,\n  OpenRouterChatSettings,\n} from '../types/openrouter-chat-settings';\n\nimport { InvalidResponseDataError } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  isParsable<PERSON>son,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { ReasoningDetailType } from '@/src/schemas/reasoning-details';\nimport { openrouterFailedResponseHandler } from '../schemas/error-response';\nimport { mapOpenRouterFinishReason } from '../utils/map-finish-reason';\nimport { convertToOpenRouterChatMessages } from './convert-to-openrouter-chat-messages';\nimport { getChatCompletionToolChoice } from './get-tool-choice';\nimport {\n  OpenRouterNonStreamChatCompletionResponseSchema,\n  OpenRouterStreamChatCompletionChunkSchema,\n} from './schemas';\n\ntype OpenRouterChatConfig = {\n  provider: string;\n  compatibility: 'strict' | 'compatible';\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: typeof fetch;\n  extraBody?: Record<string, unknown>;\n};\n\nexport class OpenRouterChatLanguageModel implements LanguageModelV2 {\n  readonly specificationVersion = 'v2' as const;\n  readonly provider = 'openrouter';\n  readonly defaultObjectGenerationMode = 'tool' as const;\n\n  readonly modelId: OpenRouterChatModelId;\n  readonly supportedUrls: Record<string, RegExp[]> = {\n    'image/*': [\n      /^data:image\\/[a-zA-Z]+;base64,/,\n      /^https?:\\/\\/.+\\.(jpg|jpeg|png|gif|webp)$/i,\n    ],\n    // 'text/*': [/^data:text\\//, /^https?:\\/\\/.+$/],\n    'application/*': [/^data:application\\//, /^https?:\\/\\/.+$/],\n  };\n  readonly settings: OpenRouterChatSettings;\n\n  private readonly config: OpenRouterChatConfig;\n\n  constructor(\n    modelId: OpenRouterChatModelId,\n    settings: OpenRouterChatSettings,\n    config: OpenRouterChatConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  private getArgs({\n    prompt,\n    maxOutputTokens,\n    temperature,\n    topP,\n    frequencyPenalty,\n    presencePenalty,\n    seed,\n    stopSequences,\n    responseFormat,\n    topK,\n    tools,\n    toolChoice,\n  }: LanguageModelV2CallOptions) {\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      models: this.settings.models,\n\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        this.settings.logprobs === true ||\n        typeof this.settings.logprobs === 'number'\n          ? true\n          : undefined,\n      top_logprobs:\n        typeof this.settings.logprobs === 'number'\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === 'boolean'\n            ? this.settings.logprobs\n              ? 0\n              : undefined\n            : undefined,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n\n      // standardized settings:\n      max_tokens: maxOutputTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n\n      stop: stopSequences,\n      response_format: responseFormat,\n      top_k: topK,\n\n      // messages:\n      messages: convertToOpenRouterChatMessages(prompt),\n\n      // OpenRouter specific settings:\n      include_reasoning: this.settings.includeReasoning,\n      reasoning: this.settings.reasoning,\n      usage: this.settings.usage,\n\n      // Web search settings:\n      plugins: this.settings.plugins,\n      web_search_options: this.settings.web_search_options,\n      // Provider routing settings:\n      provider: this.settings.provider,\n\n      // extra body:\n      ...this.config.extraBody,\n      ...this.settings.extraBody,\n    };\n\n    if (responseFormat?.type === 'json' && responseFormat.schema != null) {\n      return {\n        ...baseArgs,\n        response_format: {\n          type: 'json_schema',\n          json_schema: {\n            schema: responseFormat.schema,\n            strict: true,\n            name: responseFormat.name ?? 'response',\n            ...(responseFormat.description && {\n              description: responseFormat.description,\n            }),\n          },\n        },\n      };\n    }\n\n    if (tools && tools.length > 0) {\n      // TODO: support built-in tools\n      const mappedTools = tools\n        .filter((tool) => tool.type === 'function')\n        .map((tool) => ({\n          type: 'function' as const,\n          function: {\n            name: tool.name,\n            description: tool.type,\n            parameters: tool.inputSchema,\n          },\n        }));\n\n      return {\n        ...baseArgs,\n        tools: mappedTools,\n        tool_choice: toolChoice\n          ? getChatCompletionToolChoice(toolChoice)\n          : undefined,\n      };\n    }\n\n    return baseArgs;\n  }\n\n  async doGenerate(options: LanguageModelV2CallOptions): Promise<{\n    content: Array<LanguageModelV2Content>;\n    finishReason: LanguageModelV2FinishReason;\n    usage: LanguageModelV2Usage;\n    warnings: Array<LanguageModelV2CallWarning>;\n    providerMetadata?: {\n      openrouter: {\n        provider: string;\n        usage: OpenRouterUsageAccounting;\n      };\n    };\n    request?: { body?: unknown };\n    response?: LanguageModelV2ResponseMetadata & {\n      headers?: SharedV2Headers;\n      body?: unknown;\n    };\n  }> {\n    const providerOptions = options.providerOptions || {};\n    const openrouterOptions = providerOptions.openrouter || {};\n\n    const args = {\n      ...this.getArgs(options),\n      ...openrouterOptions,\n    };\n\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        OpenRouterNonStreamChatCompletionResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const choice = response.choices[0];\n\n    if (!choice) {\n      throw new Error('No choice in response');\n    }\n\n    // Extract detailed usage information\n    const usageInfo: LanguageModelV2Usage = response.usage\n      ? {\n          inputTokens: response.usage.prompt_tokens ?? 0,\n          outputTokens: response.usage.completion_tokens ?? 0,\n          totalTokens:\n            (response.usage.prompt_tokens ?? 0) +\n            (response.usage.completion_tokens ?? 0),\n          reasoningTokens:\n            response.usage.completion_tokens_details?.reasoning_tokens ?? 0,\n          cachedInputTokens:\n            response.usage.prompt_tokens_details?.cached_tokens ?? 0,\n        }\n      : {\n          inputTokens: 0,\n          outputTokens: 0,\n          totalTokens: 0,\n          reasoningTokens: 0,\n          cachedInputTokens: 0,\n        };\n\n    const reasoningDetails = choice.message.reasoning_details ?? [];\n\n    const reasoning: Array<LanguageModelV2Content> =\n      reasoningDetails.length > 0\n        ? reasoningDetails\n            .map((detail) => {\n              switch (detail.type) {\n                case ReasoningDetailType.Text: {\n                  if (detail.text) {\n                    return {\n                      type: 'reasoning' as const,\n                      text: detail.text,\n                    };\n                  }\n                  break;\n                }\n                case ReasoningDetailType.Summary: {\n                  if (detail.summary) {\n                    return {\n                      type: 'reasoning' as const,\n                      text: detail.summary,\n                    };\n                  }\n                  break;\n                }\n                case ReasoningDetailType.Encrypted: {\n                  // For encrypted reasoning, we include a redacted placeholder\n                  if (detail.data) {\n                    return {\n                      type: 'reasoning' as const,\n                      text: '[REDACTED]',\n                    };\n                  }\n                  break;\n                }\n                default: {\n                  detail satisfies never;\n                }\n              }\n              return null;\n            })\n            .filter((p) => p !== null)\n        : choice.message.reasoning\n          ? [\n              {\n                type: 'reasoning' as const,\n                text: choice.message.reasoning,\n              },\n            ]\n          : [];\n\n    const content: Array<LanguageModelV2Content> = [];\n\n    // Add reasoning content first\n    content.push(...reasoning);\n\n    if (choice.message.content) {\n      content.push({\n        type: 'text' as const,\n        text: choice.message.content,\n      });\n    }\n\n    if (choice.message.tool_calls) {\n      for (const toolCall of choice.message.tool_calls) {\n        content.push({\n          type: 'tool-call' as const,\n          toolCallId: toolCall.id ?? generateId(),\n          toolName: toolCall.function.name,\n          input: toolCall.function.arguments,\n        });\n      }\n    }\n\n    if (choice.message.annotations) {\n      for (const annotation of choice.message.annotations) {\n        if (annotation.type === 'url_citation') {\n          content.push({\n            type: 'source' as const,\n            sourceType: 'url' as const,\n            id: annotation.url_citation.url,\n            url: annotation.url_citation.url,\n            title: annotation.url_citation.title,\n            providerMetadata: {\n              openrouter: {\n                content: annotation.url_citation.content || '',\n              },\n            },\n          });\n        }\n      }\n    }\n\n    return {\n      content,\n      finishReason: mapOpenRouterFinishReason(choice.finish_reason),\n      usage: usageInfo,\n      warnings: [],\n      providerMetadata: {\n        openrouter: {\n          provider: response.provider ?? '',\n          usage: {\n            promptTokens: usageInfo.inputTokens ?? 0,\n            completionTokens: usageInfo.outputTokens ?? 0,\n            totalTokens: usageInfo.totalTokens ?? 0,\n            cost: response.usage?.cost,\n            promptTokensDetails: {\n              cachedTokens:\n                response.usage?.prompt_tokens_details?.cached_tokens ?? 0,\n            },\n            completionTokensDetails: {\n              reasoningTokens:\n                response.usage?.completion_tokens_details?.reasoning_tokens ??\n                0,\n            },\n            costDetails: {\n              upstreamInferenceCost:\n                response.usage?.cost_details?.upstream_inference_cost ?? 0,\n            },\n          },\n        },\n      },\n      request: { body: args },\n      response: {\n        id: response.id,\n        modelId: response.model,\n        headers: responseHeaders,\n      },\n    };\n  }\n\n  async doStream(options: LanguageModelV2CallOptions): Promise<{\n    stream: ReadableStream<LanguageModelV2StreamPart>;\n    warnings: Array<LanguageModelV2CallWarning>;\n    request?: { body?: unknown };\n    response?: LanguageModelV2ResponseMetadata & {\n      headers?: SharedV2Headers;\n      body?: unknown;\n    };\n  }> {\n    const providerOptions = options.providerOptions || {};\n    const openrouterOptions = providerOptions.openrouter || {};\n\n    const args = {\n      ...this.getArgs(options),\n      ...openrouterOptions,\n    };\n\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        stream: true,\n\n        // only include stream_options when in strict compatibility mode:\n        stream_options:\n          this.config.compatibility === 'strict'\n            ? {\n                include_usage: true,\n                // If user has requested usage accounting, make sure we get it in the stream\n                ...(this.settings.usage?.include\n                  ? { include_usage: true }\n                  : {}),\n              }\n            : undefined,\n      },\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        OpenRouterStreamChatCompletionChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const toolCalls: Array<{\n      id: string;\n      type: 'function';\n      function: {\n        name: string;\n        arguments: string;\n      };\n      inputStarted: boolean;\n      sent: boolean;\n    }> = [];\n\n    let finishReason: FinishReason = 'other';\n    const usage: LanguageModelV2Usage = {\n      inputTokens: Number.NaN,\n      outputTokens: Number.NaN,\n      totalTokens: Number.NaN,\n      reasoningTokens: Number.NaN,\n      cachedInputTokens: Number.NaN,\n    };\n\n    // Track provider-specific usage information\n    const openrouterUsage: Partial<OpenRouterUsageAccounting> = {};\n\n    let textStarted = false;\n    let reasoningStarted = false;\n    let textId: string | undefined;\n    let reasoningId: string | undefined;\n    let openrouterResponseId: string | undefined;\n    let provider: string | undefined;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<\n            z.infer<typeof OpenRouterStreamChatCompletionChunkSchema>\n          >,\n          LanguageModelV2StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if ('error' in value) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: value.error });\n              return;\n            }\n\n            if (value.provider) {\n              provider = value.provider;\n            }\n\n            if (value.id) {\n              openrouterResponseId = value.id;\n              controller.enqueue({\n                type: 'response-metadata',\n                id: value.id,\n              });\n            }\n\n            if (value.model) {\n              controller.enqueue({\n                type: 'response-metadata',\n                modelId: value.model,\n              });\n            }\n\n            if (value.usage != null) {\n              usage.inputTokens = value.usage.prompt_tokens;\n              usage.outputTokens = value.usage.completion_tokens;\n              usage.totalTokens =\n                value.usage.prompt_tokens + value.usage.completion_tokens;\n\n              // Collect OpenRouter specific usage information\n              openrouterUsage.promptTokens = value.usage.prompt_tokens;\n\n              if (value.usage.prompt_tokens_details) {\n                const cachedInputTokens =\n                  value.usage.prompt_tokens_details.cached_tokens ?? 0;\n\n                usage.cachedInputTokens = cachedInputTokens;\n                openrouterUsage.promptTokensDetails = {\n                  cachedTokens: cachedInputTokens,\n                };\n              }\n\n              openrouterUsage.completionTokens = value.usage.completion_tokens;\n              if (value.usage.completion_tokens_details) {\n                const reasoningTokens =\n                  value.usage.completion_tokens_details.reasoning_tokens ?? 0;\n\n                usage.reasoningTokens = reasoningTokens;\n                openrouterUsage.completionTokensDetails = {\n                  reasoningTokens,\n                };\n              }\n\n              openrouterUsage.cost = value.usage.cost;\n              openrouterUsage.totalTokens = value.usage.total_tokens;\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenRouterFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.delta == null) {\n              return;\n            }\n\n            const delta = choice.delta;\n\n            const emitReasoningChunk = (chunkText: string) => {\n              if (!reasoningStarted) {\n                reasoningId = openrouterResponseId || generateId();\n                controller.enqueue({\n                  type: 'reasoning-start',\n                  id: reasoningId,\n                });\n                reasoningStarted = true;\n              }\n              controller.enqueue({\n                type: 'reasoning-delta',\n                delta: chunkText,\n                id: reasoningId || generateId(),\n              });\n            };\n\n            if (delta.reasoning_details && delta.reasoning_details.length > 0) {\n              for (const detail of delta.reasoning_details) {\n                switch (detail.type) {\n                  case ReasoningDetailType.Text: {\n                    if (detail.text) {\n                      emitReasoningChunk(detail.text);\n                    }\n                    break;\n                  }\n                  case ReasoningDetailType.Encrypted: {\n                    if (detail.data) {\n                      emitReasoningChunk('[REDACTED]');\n                    }\n                    break;\n                  }\n                  case ReasoningDetailType.Summary: {\n                    if (detail.summary) {\n                      emitReasoningChunk(detail.summary);\n                    }\n                    break;\n                  }\n                  default: {\n                    detail satisfies never;\n                    break;\n                  }\n                }\n              }\n            } else if (delta.reasoning) {\n              emitReasoningChunk(delta.reasoning);\n            }\n\n            if (delta.content) {\n              // If reasoning was previously active and now we're starting text content,\n              // we should end the reasoning first to maintain proper order\n              if (reasoningStarted && !textStarted) {\n                controller.enqueue({\n                  type: 'reasoning-end',\n                  id: reasoningId || generateId(),\n                });\n                reasoningStarted = false; // Mark as ended so we don't end it again in flush\n              }\n\n              if (!textStarted) {\n                textId = openrouterResponseId || generateId();\n                controller.enqueue({\n                  type: 'text-start',\n                  id: textId,\n                });\n                textStarted = true;\n              }\n              controller.enqueue({\n                type: 'text-delta',\n                delta: delta.content,\n                id: textId || generateId(),\n              });\n            }\n\n            if (delta.annotations) {\n              for (const annotation of delta.annotations) {\n                if (annotation.type === 'url_citation') {\n                  controller.enqueue({\n                    type: 'source',\n                    sourceType: 'url' as const,\n                    id: annotation.url_citation.url,\n                    url: annotation.url_citation.url,\n                    title: annotation.url_citation.title,\n                    providerMetadata: {\n                      openrouter: {\n                        content: annotation.url_citation.content || '',\n                      },\n                    },\n                  });\n                }\n              }\n            }\n\n            if (delta.tool_calls != null) {\n              for (const toolCallDelta of delta.tool_calls) {\n                const index = toolCallDelta.index ?? toolCalls.length - 1;\n\n                // Tool call start. OpenRouter returns all information except the arguments in the first chunk.\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== 'function') {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`,\n                    });\n                  }\n\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`,\n                    });\n                  }\n\n                  if (toolCallDelta.function?.name == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`,\n                    });\n                  }\n\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: 'function',\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: toolCallDelta.function.arguments ?? '',\n                    },\n                    inputStarted: false,\n                    sent: false,\n                  };\n\n                  const toolCall = toolCalls[index];\n\n                  if (toolCall == null) {\n                    throw new Error('Tool call is missing');\n                  }\n\n                  // check if tool call is complete (some providers send the full tool call in one chunk)\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null &&\n                    isParsableJson(toolCall.function.arguments)\n                  ) {\n                    toolCall.inputStarted = true;\n\n                    controller.enqueue({\n                      type: 'tool-input-start',\n                      id: toolCall.id,\n                      toolName: toolCall.function.name,\n                    });\n\n                    // send delta\n                    controller.enqueue({\n                      type: 'tool-input-delta',\n                      id: toolCall.id,\n                      delta: toolCall.function.arguments,\n                    });\n\n                    controller.enqueue({\n                      type: 'tool-input-end',\n                      id: toolCall.id,\n                    });\n\n                    // send tool call\n                    controller.enqueue({\n                      type: 'tool-call',\n                      toolCallId: toolCall.id,\n                      toolName: toolCall.function.name,\n                      input: toolCall.function.arguments,\n                    });\n\n                    toolCall.sent = true;\n                  }\n\n                  continue;\n                }\n\n                // existing tool call, merge\n                const toolCall = toolCalls[index];\n\n                if (toolCall == null) {\n                  throw new Error('Tool call is missing');\n                }\n\n                if (!toolCall.inputStarted) {\n                  toolCall.inputStarted = true;\n                  controller.enqueue({\n                    type: 'tool-input-start',\n                    id: toolCall.id,\n                    toolName: toolCall.function.name,\n                  });\n                }\n\n                if (toolCallDelta.function?.arguments != null) {\n                  toolCall.function.arguments +=\n                    toolCallDelta.function?.arguments ?? '';\n                }\n\n                // send delta\n                controller.enqueue({\n                  type: 'tool-input-delta',\n                  id: toolCall.id,\n                  delta: toolCallDelta.function.arguments ?? '',\n                });\n\n                // check if tool call is complete\n                if (\n                  toolCall.function?.name != null &&\n                  toolCall.function?.arguments != null &&\n                  isParsableJson(toolCall.function.arguments)\n                ) {\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallId: toolCall.id ?? generateId(),\n                    toolName: toolCall.function.name,\n                    input: toolCall.function.arguments,\n                  });\n\n                  toolCall.sent = true;\n                }\n              }\n            }\n          },\n\n          flush(controller) {\n            // Forward any unsent tool calls if finish reason is 'tool-calls'\n            if (finishReason === 'tool-calls') {\n              for (const toolCall of toolCalls) {\n                if (toolCall && !toolCall.sent) {\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallId: toolCall.id ?? generateId(),\n                    toolName: toolCall.function.name,\n                    // Coerce invalid arguments to an empty JSON object\n                    input: isParsableJson(toolCall.function.arguments)\n                      ? toolCall.function.arguments\n                      : '{}',\n                  });\n                  toolCall.sent = true;\n                }\n              }\n            }\n\n            // End reasoning first if it was started, to maintain proper order\n            if (reasoningStarted) {\n              controller.enqueue({\n                type: 'reasoning-end',\n                id: reasoningId || generateId(),\n              });\n            }\n            if (textStarted) {\n              controller.enqueue({\n                type: 'text-end',\n                id: textId || generateId(),\n              });\n            }\n\n            const openrouterMetadata: {\n              usage: Partial<OpenRouterUsageAccounting>;\n              provider?: string;\n            } = {\n              usage: openrouterUsage,\n            };\n            \n            // Only include provider if it's actually set\n            if (provider !== undefined) {\n              openrouterMetadata.provider = provider;\n            }\n            \n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage,\n              providerMetadata: {\n                openrouter: openrouterMetadata,\n              },\n            });\n          },\n        }),\n      ),\n      warnings: [],\n      request: { body: args },\n      response: { headers: responseHeaders },\n    };\n  }\n}\n", "import type {\n  LanguageModelV2FilePart,\n  LanguageModelV2Prompt,\n  LanguageModelV2ReasoningPart,\n  LanguageModelV2TextPart,\n  LanguageModelV2ToolCallPart,\n  LanguageModelV2ToolResultPart,\n} from '@ai-sdk/provider';\n\nimport {\n  InvalidPromptError,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\n\nexport function convertToOpenRouterCompletionPrompt({\n  prompt,\n  inputFormat,\n  user = 'user',\n  assistant = 'assistant',\n}: {\n  prompt: LanguageModelV2Prompt;\n  inputFormat: 'prompt' | 'messages';\n  user?: string;\n  assistant?: string;\n}): {\n  prompt: string;\n} {\n  // When the user supplied a prompt input, we don't transform it:\n  if (\n    inputFormat === 'prompt' &&\n    prompt.length === 1 &&\n    prompt[0] &&\n    prompt[0].role === 'user' &&\n    prompt[0].content.length === 1 &&\n    prompt[0].content[0] &&\n    prompt[0].content[0].type === 'text'\n  ) {\n    return { prompt: prompt[0].content[0].text };\n  }\n\n  // otherwise transform to a chat message format:\n  let text = '';\n\n  // if first message is a system message, add it to the text:\n  if (prompt[0] && prompt[0].role === 'system') {\n    text += `${prompt[0].content}\\n\\n`;\n    prompt = prompt.slice(1);\n  }\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        throw new InvalidPromptError({\n          message: `Unexpected system message in prompt: ${content}`,\n          prompt,\n        });\n      }\n\n      case 'user': {\n        const userMessage = content\n          .map((part: LanguageModelV2TextPart | LanguageModelV2FilePart) => {\n            switch (part.type) {\n              case 'text': {\n                return part.text;\n              }\n\n              case 'file': {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'file attachments',\n                });\n              }\n              default: {\n                return '';\n              }\n            }\n          })\n          .join('');\n\n        text += `${user}:\\n${userMessage}\\n\\n`;\n        break;\n      }\n\n      case 'assistant': {\n        const assistantMessage = content\n          .map(\n            (\n              part:\n                | LanguageModelV2TextPart\n                | LanguageModelV2FilePart\n                | LanguageModelV2ReasoningPart\n                | LanguageModelV2ToolCallPart\n                | LanguageModelV2ToolResultPart,\n            ) => {\n              switch (part.type) {\n                case 'text': {\n                  return part.text;\n                }\n                case 'tool-call': {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'tool-call messages',\n                  });\n                }\n                case 'tool-result': {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'tool-result messages',\n                  });\n                }\n                case 'reasoning': {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'reasoning messages',\n                  });\n                }\n\n                case 'file': {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'file attachments',\n                  });\n                }\n\n                default: {\n                  return '';\n                }\n              }\n            },\n          )\n          .join('');\n\n        text += `${assistant}:\\n${assistantMessage}\\n\\n`;\n        break;\n      }\n\n      case 'tool': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'tool messages',\n        });\n      }\n\n      default: {\n        break;\n      }\n    }\n  }\n\n  // Assistant message prefix:\n  text += `${assistant}:\\n`;\n\n  return {\n    prompt: text,\n  };\n}\n", "import { z } from 'zod/v4';\nimport { OpenRouterErrorResponseSchema } from '../schemas/error-response';\nimport { ReasoningDetailArraySchema } from '../schemas/reasoning-details';\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nexport const OpenRouterCompletionChunkSchema = z.union([\n  z.object({\n    id: z.string().optional(),\n    model: z.string().optional(),\n    choices: z.array(\n      z.object({\n        text: z.string(),\n        reasoning: z.string().nullish().optional(),\n        reasoning_details: ReasoningDetailArraySchema.nullish(),\n\n        finish_reason: z.string().nullish(),\n        index: z.number().nullish(),\n        logprobs: z\n          .object({\n            tokens: z.array(z.string()),\n            token_logprobs: z.array(z.number()),\n            top_logprobs: z.array(z.record(z.string(), z.number())).nullable(),\n          })\n          .nullable()\n          .optional(),\n      }),\n    ),\n    usage: z\n      .object({\n        prompt_tokens: z.number(),\n        prompt_tokens_details: z\n          .object({\n            cached_tokens: z.number(),\n          })\n          .nullish(),\n        completion_tokens: z.number(),\n        completion_tokens_details: z\n          .object({\n            reasoning_tokens: z.number(),\n          })\n          .nullish(),\n        total_tokens: z.number(),\n        cost: z.number().optional(),\n      })\n      .nullish(),\n  }),\n  OpenRouterErrorResponseSchema,\n]);\n", "import type {\n  LanguageModelV2,\n  LanguageModelV2CallOptions,\n  LanguageModelV2StreamPart,\n  LanguageModelV2Usage,\n} from '@ai-sdk/provider';\nimport type { ParseResult } from '@ai-sdk/provider-utils';\nimport type { FinishReason } from 'ai';\nimport type { z } from 'zod/v4';\nimport type { OpenRouterUsageAccounting } from '../types';\nimport type {\n  OpenRouterCompletionModelId,\n  OpenRouterCompletionSettings,\n} from '../types/openrouter-completion-settings';\n\nimport { UnsupportedFunctionalityError } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { openrouterFailedResponseHandler } from '../schemas/error-response';\nimport { mapOpenRouterFinishReason } from '../utils/map-finish-reason';\nimport { convertToOpenRouterCompletionPrompt } from './convert-to-openrouter-completion-prompt';\nimport { OpenRouterCompletionChunkSchema } from './schemas';\n\ntype OpenRouterCompletionConfig = {\n  provider: string;\n  compatibility: 'strict' | 'compatible';\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: typeof fetch;\n  extraBody?: Record<string, unknown>;\n};\n\nexport class OpenRouterCompletionLanguageModel implements LanguageModelV2 {\n  readonly specificationVersion = 'v2' as const;\n  readonly provider = 'openrouter';\n  readonly modelId: OpenRouterCompletionModelId;\n  readonly supportedUrls: Record<string, RegExp[]> = {\n    'image/*': [\n      /^data:image\\/[a-zA-Z]+;base64,/,\n      /^https?:\\/\\/.+\\.(jpg|jpeg|png|gif|webp)$/i,\n    ],\n    'text/*': [/^data:text\\//, /^https?:\\/\\/.+$/],\n    'application/*': [/^data:application\\//, /^https?:\\/\\/.+$/],\n  };\n  readonly defaultObjectGenerationMode = undefined;\n  readonly settings: OpenRouterCompletionSettings;\n\n  private readonly config: OpenRouterCompletionConfig;\n\n  constructor(\n    modelId: OpenRouterCompletionModelId,\n    settings: OpenRouterCompletionSettings,\n    config: OpenRouterCompletionConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  private getArgs({\n    prompt,\n    maxOutputTokens,\n    temperature,\n    topP,\n    frequencyPenalty,\n    presencePenalty,\n    seed,\n    responseFormat,\n    topK,\n    stopSequences,\n    tools,\n    toolChoice,\n  }: LanguageModelV2CallOptions) {\n    const { prompt: completionPrompt } = convertToOpenRouterCompletionPrompt({\n      prompt,\n      inputFormat: 'prompt',\n    });\n\n    if (tools?.length) {\n      throw new UnsupportedFunctionalityError({\n        functionality: 'tools',\n      });\n    }\n\n    if (toolChoice) {\n      throw new UnsupportedFunctionalityError({\n        functionality: 'toolChoice',\n      });\n    }\n\n    return {\n      // model id:\n      model: this.modelId,\n      models: this.settings.models,\n\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        typeof this.settings.logprobs === 'number'\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === 'boolean'\n            ? this.settings.logprobs\n              ? 0\n              : undefined\n            : undefined,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n\n      // standardized settings:\n      max_tokens: maxOutputTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n\n      stop: stopSequences,\n      response_format: responseFormat,\n      top_k: topK,\n\n      // prompt:\n      prompt: completionPrompt,\n\n      // OpenRouter specific settings:\n      include_reasoning: this.settings.includeReasoning,\n      reasoning: this.settings.reasoning,\n\n      // extra body:\n      ...this.config.extraBody,\n      ...this.settings.extraBody,\n    };\n  }\n\n  async doGenerate(\n    options: LanguageModelV2CallOptions,\n  ): Promise<Awaited<ReturnType<LanguageModelV2['doGenerate']>>> {\n    const providerOptions = options.providerOptions || {};\n    const openrouterOptions = providerOptions.openrouter || {};\n\n    const args = {\n      ...this.getArgs(options),\n      ...openrouterOptions,\n    };\n\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: this.config.url({\n        path: '/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        OpenRouterCompletionChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    if ('error' in response) {\n      throw new Error(`${response.error.message}`);\n    }\n\n    const choice = response.choices[0];\n\n    if (!choice) {\n      throw new Error('No choice in OpenRouter completion response');\n    }\n\n    return {\n      content: [\n        {\n          type: 'text',\n          text: choice.text ?? '',\n        },\n      ],\n      finishReason: mapOpenRouterFinishReason(choice.finish_reason),\n      usage: {\n        inputTokens: response.usage?.prompt_tokens ?? 0,\n        outputTokens: response.usage?.completion_tokens ?? 0,\n        totalTokens:\n          (response.usage?.prompt_tokens ?? 0) +\n          (response.usage?.completion_tokens ?? 0),\n        reasoningTokens:\n          response.usage?.completion_tokens_details?.reasoning_tokens ?? 0,\n        cachedInputTokens:\n          response.usage?.prompt_tokens_details?.cached_tokens ?? 0,\n      },\n      warnings: [],\n      response: {\n        headers: responseHeaders,\n      },\n    };\n  }\n\n  async doStream(\n    options: LanguageModelV2CallOptions,\n  ): Promise<Awaited<ReturnType<LanguageModelV2['doStream']>>> {\n    const providerOptions = options.providerOptions || {};\n    const openrouterOptions = providerOptions.openrouter || {};\n\n    const args = {\n      ...this.getArgs(options),\n      ...openrouterOptions,\n    };\n\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: this.config.url({\n        path: '/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        stream: true,\n\n        // only include stream_options when in strict compatibility mode:\n        stream_options:\n          this.config.compatibility === 'strict'\n            ? { include_usage: true }\n            : undefined,\n      },\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        OpenRouterCompletionChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    let finishReason: FinishReason = 'other';\n    const usage: LanguageModelV2Usage = {\n      inputTokens: Number.NaN,\n      outputTokens: Number.NaN,\n      totalTokens: Number.NaN,\n      reasoningTokens: Number.NaN,\n      cachedInputTokens: Number.NaN,\n    };\n\n    const openrouterUsage: Partial<OpenRouterUsageAccounting> = {};\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof OpenRouterCompletionChunkSchema>>,\n          LanguageModelV2StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if ('error' in value) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: value.error });\n              return;\n            }\n\n            if (value.usage != null) {\n              usage.inputTokens = value.usage.prompt_tokens;\n              usage.outputTokens = value.usage.completion_tokens;\n              usage.totalTokens =\n                value.usage.prompt_tokens + value.usage.completion_tokens;\n\n              // Collect OpenRouter specific usage information\n              openrouterUsage.promptTokens = value.usage.prompt_tokens;\n\n              if (value.usage.prompt_tokens_details) {\n                const cachedInputTokens =\n                  value.usage.prompt_tokens_details.cached_tokens ?? 0;\n\n                usage.cachedInputTokens = cachedInputTokens;\n                openrouterUsage.promptTokensDetails = {\n                  cachedTokens: cachedInputTokens,\n                };\n              }\n\n              openrouterUsage.completionTokens = value.usage.completion_tokens;\n              if (value.usage.completion_tokens_details) {\n                const reasoningTokens =\n                  value.usage.completion_tokens_details.reasoning_tokens ?? 0;\n\n                usage.reasoningTokens = reasoningTokens;\n                openrouterUsage.completionTokensDetails = {\n                  reasoningTokens,\n                };\n              }\n\n              openrouterUsage.cost = value.usage.cost;\n              openrouterUsage.totalTokens = value.usage.total_tokens;\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenRouterFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.text != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                delta: choice.text,\n                id: generateId(),\n              });\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage,\n              providerMetadata: {\n                openrouter: {\n                  usage: openrouterUsage,\n                },\n              },\n            });\n          },\n        }),\n      ),\n      response: {\n        headers: responseHeaders,\n      },\n    };\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACIA,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,IAAI,MAAM;AALhC,IAAA;AAWO,IAAM,cAAN,MAAMA,qBAAmB,MAAM;;;;;;;;;EAgBpC,YAAY;IACV,MAAAC;IACA;IACA;EACF,GAIG;AACD,UAAM,OAAO;AAxBf,SAAkB,EAAA,IAAU;AA0B1B,SAAK,OAAOA;AACZ,SAAK,QAAQ;EACf;;;;;;EAOA,OAAO,WAAW,OAAqC;AACrD,WAAOD,aAAW,UAAU,OAAO,MAAM;EAC3C;EAEA,OAAiB,UAAU,OAAgBE,UAAyB;AAClE,UAAM,eAAe,OAAO,IAAIA,QAAM;AACtC,WACE,SAAS,QACT,OAAO,UAAU,YACjB,gBAAgB,SAChB,OAAO,MAAM,YAAY,MAAM,aAC/B,MAAM,YAAY,MAAM;EAE5B;AACF;AAjDoB,KAAA;AADb,IAAM,aAAN;ACTP,IAAM,OAAO;AACb,IAAMA,UAAS,mBAAmB,IAAI;AACtC,IAAMC,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,eAAN,cAA2B,WAAW;EAa3C,YAAY;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc,cAAc,SACzB,eAAe;IACd,eAAe;IACf,eAAe;IACf,cAAc;;IAClB;EACF,GAUG;AACD,UAAM,EAAE,MAAM,SAAS,MAAM,CAAC;AArChC,SAAkBA,GAAAA,IAAU;AAuC1B,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAuC;AACvD,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AAnDoBE,MAAAD;ACLpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,yBAAN,cAAqC,WAAW;;EAGrD,YAAY,EAAE,UAAU,sBAAsB,IAA0B,CAAC,GAAG;AAC1E,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAHzB,SAAkBG,GAAAA,IAAU;EAI5B;EAEA,OAAO,WAAW,OAAiD;AACjE,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AAToBE,MAAAD;ACPb,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;ACZA,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AASO,IAAM,uBAAN,cAAmC,WAAW;EAKnD,YAAY;IACV;IACA;IACA;EACF,GAIG;AACD,UAAM,EAAE,MAAAH,OAAM,SAAS,MAAM,CAAC;AAbhC,SAAkBG,GAAAA,IAAU;AAe1B,SAAK,WAAW;EAClB;EAEA,OAAO,WAAW,OAA+C;AAC/D,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AArBoBE,MAAAD;ACRpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,qBAAN,cAAiC,WAAW;EAKjD,YAAY;IACV;IACA;IACA;EACF,GAIG;AACD,UAAM,EAAE,MAAAH,OAAM,SAAS,mBAAmB,OAAO,IAAI,MAAM,CAAC;AAb9D,SAAkBG,GAAAA,IAAU;AAe1B,SAAK,SAAS;EAChB;EAEA,OAAO,WAAW,OAA6C;AAC7D,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AArBoBE,MAAAD;ACTpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,2BAAN,cAAuC,WAAW;EAKvD,YAAY;IACV;IACA,UAAU,0BAA0B,KAAK,UAAU,IAAI,CAAC;EAC1D,GAGG;AACD,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAXzB,SAAkBG,GAAAA,IAAU;AAa1B,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAmD;AACnE,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AAnBoBE,MAAAD;ACRpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AALhC,IAAAE;AAQO,IAAM,iBAAN,cAA6B,WAAW;EAK7C,YAAY,EAAE,MAAM,MAAM,GAAqC;AAC7D,UAAM;MACJ,MAAAH;MACA,SACE,8BACS,IAAI;iBACK,gBAAgB,KAAK,CAAC;MAC1C;IACF,CAAC;AAZH,SAAkBG,GAAAA,IAAU;AAc1B,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAyC;AACzD,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AApBoBE,MAAAD;ACPpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAOoBC,MAAAC;ACLpB,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAMD,UAAS,OAAO,IAAIE,OAAM;AAJhC,IAAAH;AAOoBI,MAAAC;ACLpB,IAAMC,QAAO;AACb,IAAMC,WAAS,mBAAmBD,KAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAUoBI,OAAAC;ACRpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAOoBI,OAAAC;ACLpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAOoBI,OAAAC;ACJpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AALhC,IAAAH;AAOO,IAAM,uBAAN,MAAMI,8BAA4B,WAAW;EAKlD,YAAY,EAAE,OAAO,MAAM,GAAuC;AAChE,UAAM;MACJ,MAAAF;MACA,SACE,kCACU,KAAK,UAAU,KAAK,CAAC;iBACb,gBAAgB,KAAK,CAAC;MAC1C;IACF,CAAC;AAZH,SAAkBF,IAAAA,IAAU;AAc1B,SAAK,QAAQ;EACf;EAEA,OAAO,WAAW,OAA8C;AAC9D,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;;;;;;;;;;;EAYA,OAAO,KAAK;IACV;IACA;EACF,GAGwB;AACtB,WAAOC,sBAAoB,WAAW,KAAK,KAAK,MAAM,UAAU,QAC5D,QACA,IAAIA,sBAAoB,EAAE,OAAO,MAAM,CAAC;EAC9C;AACF;AA1CoBJ,OAAAC;AADb,IAAM,sBAAN;ACLP,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAMO,IAAM,gCAAN,cAA4C,WAAW;EAK5D,YAAY;IACV;IACA,UAAU,IAAI,aAAa;EAC7B,GAGG;AACD,UAAM,EAAE,MAAAE,QAAM,QAAQ,CAAC;AAXzB,SAAkBF,IAAAA,IAAU;AAY1B,SAAK,gBAAgB;EACvB;EAEA,OAAO,WAAW,OAAwD;AACxE,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;AACF;AAlBoBH,OAAAC;;;AEIb,IAAM,aAAN,cAAyB,MAAM;EAqBpC,YACE,SACA,SACA;AACA,UAAM,OAAO,GACb,KAAK,OAAO,cACZ,KAAK,OAAO,QAAQ,MACpB,KAAK,QAAQ,QAAQ,OACrB,KAAK,QAAQ,QAAQ,OACrB,KAAK,OAAO,QAAQ;EAAA;AAExB;ACnCA,SAAS,KAAK,MAAe;AAE7B;AAcO,SAAS,aAAa,WAA+C;AAC1E,MAAI,OAAO,aAAc;AACvB,UAAM,IAAI;MACR;IACF;AAGI,QAAA,EAAC,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,UAAA,IAAa;AAEpE,MAAI,iBAAiB,IAEjB,eAAe,MACf,IACA,OAAO,IACP,YAAY;AAEhB,WAAS,KAAK,UAAkB;AAE9B,UAAM,QAAQ,eAAe,SAAS,QAAQ,iBAAiB,EAAE,IAAI,UAI/D,CAAC,UAAU,UAAU,IAAI,WAAW,GAAG,cAAc,GAAG,KAAK,EAAE;AAErE,eAAW,QAAQ;AACjB,gBAAU,IAAI;AAGhB,qBAAiB,YACjB,eAAe;EAAA;AAGjB,WAAS,UAAU,MAAc;AAE/B,QAAI,SAAS,IAAI;AACD,oBAAA;AACd;IAAA;AAIE,QAAA,KAAK,WAAW,GAAG,GAAG;AACpB,mBACF,UAAU,KAAK,MAAM,KAAK,WAAW,IAAI,IAAI,IAAI,CAAC,CAAC;AAErD;IAAA;AAII,UAAA,sBAAsB,KAAK,QAAQ,GAAG;AAC5C,QAAI,wBAAwB,IAAI;AAG9B,YAAM,QAAQ,KAAK,MAAM,GAAG,mBAAmB,GAKzC,SAAS,KAAK,sBAAsB,CAAC,MAAM,MAAM,IAAI,GACrD,QAAQ,KAAK,MAAM,sBAAsB,MAAM;AAExC,mBAAA,OAAO,OAAO,IAAI;AAC/B;IAAA;AAOW,iBAAA,MAAM,IAAI,IAAI;EAAA;AAGpB,WAAA,aAAa,OAAe,OAAe,MAAc;AAEhE,YAAQ,OAAO;MACb,KAAK;AAES,oBAAA;AACZ;MACF,KAAK;AAGI,eAAA,GAAG,IAAI,GAAG,KAAK;;AACtB;MACF,KAAK;AAGH,aAAK,MAAM,SAAS,IAAI,IAAI,SAAY;AACxC;MACF,KAAK;AAIC,gBAAQ,KAAK,KAAK,IACpB,QAAQ,SAAS,OAAO,EAAE,CAAC,IAE3B;UACE,IAAI,WAAW,6BAA6B,KAAK,KAAK;YACpD,MAAM;YACN;YACA;UACD,CAAA;QACH;AAEF;MACF;AAEE;UACE,IAAI;YACF,kBAAkB,MAAM,SAAS,KAAK,GAAG,MAAM,MAAM,GAAG,EAAE,CAAC,WAAM,KAAK;YACtE,EAAC,MAAM,iBAAiB,OAAO,OAAO,KAAI;UAAA;QAE9C;AACA;IAAA;EACJ;AAGF,WAAS,gBAAgB;AACA,SAAK,SAAS,KAEnC,QAAQ;MACN;MACA,OAAO,aAAa;;;MAGpB,MAAM,KAAK,SAAS;CAAI,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI;IAAA,CACjD,GAIH,KAAK,QACL,OAAO,IACP,YAAY;EAAA;AAGL,WAAA,MAAM,UAA+B,CAAA,GAAI;AAC5C,sBAAkB,QAAQ,WAC5B,UAAU,cAAc,GAG1B,eAAe,MACf,KAAK,QACL,OAAO,IACP,YAAY,IACZ,iBAAiB;EAAA;AAGZ,SAAA,EAAC,MAAM,MAAK;AACrB;AASA,SAAS,WAAW,OAA8D;AAOhF,QAAM,QAAuB,CAAC;AAC1B,MAAA,iBAAiB,IACjB,cAAc;AAEX,SAAA,cAAc,MAAM,UAAQ;AAE3B,UAAA,UAAU,MAAM,QAAQ,MAAM,WAAW,GACzC,UAAU,MAAM,QAAQ;GAAM,WAAW;AAG/C,QAAI,UAAU;AAWd,QAVI,YAAY,MAAM,YAAY,KAEhC,UAAU,KAAK,IAAI,SAAS,OAAO,IAC1B,YAAY,KACrB,UAAU,UACD,YAAY,OACrB,UAAU,UAIR,YAAY,IAAI;AAED,uBAAA,MAAM,MAAM,WAAW;AACxC;IAAA,OACK;AACL,YAAM,OAAO,MAAM,MAAM,aAAa,OAAO;AAC7C,YAAM,KAAK,IAAI,GAGf,cAAc,UAAU,GACpB,MAAM,cAAc,CAAC,MAAM,QAAQ,MAAM,WAAW,MAAM;KAC5D;IAAA;EAEJ;AAGK,SAAA,CAAC,OAAO,cAAc;AAC/B;;;ACzKO,IAAM,0BAAN,cAAsC,gBAA4C;EACvF,YAAY,EAAC,SAAS,SAAS,UAAS,IAAmB,CAAA,GAAI;AACzD,QAAA;AAEE,UAAA;MACJ,MAAM,YAAY;AAChB,iBAAS,aAAa;UACpB,SAAS,CAAC,UAAU;AAClB,uBAAW,QAAQ,KAAK;UAC1B;UACA,QAAQ,OAAO;AACT,wBAAY,cACd,WAAW,MAAM,KAAK,IACb,OAAO,WAAY,cAC5B,QAAQ,KAAK;UAIjB;UACA;UACA;QAAA,CACD;MACH;MACA,UAAU,OAAO;AACf,eAAO,KAAK,KAAK;MAAA;IACnB,CACD;EAAA;AAEL;;;A0BlFA,SAAoB;;;AKFb,IAAM,iBAAiB,OAAO,mDAAmD;;;ACAxF,IAAAI,cAAsC;;;ACAtC,iBAAsC;;;ACAtC,IAAAC,cAAuC;;;ACsMvC,IAAM,gBAAgB,IAAI,IAAI,8DAA8D;;;AlCtMrF,SAAS,kBACX,SACiC;AACpC,SAAO,QAAQ;IACb,CAAC,iBAAiB,mBAAoB,kCACjC,kBACC,kBAAA,OAAA,iBAAkB,CAAC;IAEzB,CAAC;EACH;AACF;AGJO,SAAS,uBAAuB,UAAoB;AACzD,SAAO,OAAO,YAAoB,CAAC,GAAG,SAAS,OAAO,CAAC;AACzD;ACIO,IAAM,oBAAoB,CAAC;EAChC;EACA,OAAO;EACP,WAAW;EACX,YAAY;AACd,IAKI,CAAC,MAAmB;AACtB,QAAM,YAAY,MAAM;AACtB,UAAM,iBAAiB,SAAS;AAChC,UAAM,QAAQ,IAAI,MAAM,IAAI;AAC5B,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,YAAM,CAAC,IAAI,SAAU,KAAK,OAAO,IAAI,iBAAkB,CAAC;IAC1D;AACA,WAAO,MAAM,KAAK,EAAE;EACtB;AAEA,MAAI,UAAU,MAAM;AAClB,WAAO;EACT;AAGA,MAAI,SAAS,SAAS,SAAS,GAAG;AAChC,UAAM,IAAI,qBAAqB;MAC7B,UAAU;MACV,SAAS,kBAAkB,SAAS,uCAAuC,QAAQ;IACrF,CAAC;EACH;AAEA,SAAO,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC;AAClD;AAWO,IAAM,aAAa,kBAAkB;AIxDrC,SAAS,aAAa,OAAgC;AAC3D,UACG,iBAAiB,SAAS,iBAAiB,kBAC3C,MAAM,SAAS,gBACd,MAAM,SAAS;EACf,MAAM,SAAS;AAErB;ADJA,IAAM,8BAA8B,CAAC,gBAAgB,iBAAiB;AAE/D,SAAS,iBAAiB;EAC/B;EACA;EACA;AACF,GAIG;AACD,MAAI,aAAa,KAAK,GAAG;AACvB,WAAO;EACT;AAGA,MACE,iBAAiB,aACjB,4BAA4B,SAAS,MAAM,QAAQ,YAAY,CAAC,GAChE;AACA,UAAM,QAAS,MAAc;AAE7B,QAAI,SAAS,MAAM;AAEjB,aAAO,IAAI,aAAa;QACtB,SAAS,0BAA0B,MAAM,OAAO;QAChD;QACA;QACA;QACA,aAAa;;MACf,CAAC;IACH;EACF;AAEA,SAAO;AACT;AEjCO,SAAS,uBACd,QACmB;AACnB,SAAO,OAAO;IACZ,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,SAAS,IAAI;EAChE;AACF;AMYA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAE7B,SAAS,OAAO,MAAc;AAE5B,QAAM,MAAM,KAAK,MAAM,IAAI;AAG3B,MAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;AAC3C,WAAO;EACT;AAEA,MACE,eAAe,KAAK,IAAI,MAAM,SAC9B,qBAAqB,KAAK,IAAI,MAAM,OACpC;AACA,WAAO;EACT;AAGA,SAAO,OAAO,GAAG;AACnB;AAEA,SAAS,OAAO,KAAU;AACxB,MAAI,OAAO,CAAC,GAAG;AAEf,SAAO,KAAK,QAAQ;AAClB,UAAM,QAAQ;AACd,WAAO,CAAC;AAER,eAAW,QAAQ,OAAO;AACxB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,GAAG;AAC3D,cAAM,IAAI,YAAY,8CAA8C;MACtE;AAEA,UACE,OAAO,UAAU,eAAe,KAAK,MAAM,aAAa,KACxD,OAAO,UAAU,eAAe,KAAK,KAAK,aAAa,WAAW,GAClE;AACA,cAAM,IAAI,YAAY,8CAA8C;MACtE;AAEA,iBAAW,OAAO,MAAM;AACtB,cAAM,QAAQ,KAAK,GAAG;AACtB,YAAI,SAAS,OAAO,UAAU,UAAU;AACtC,eAAK,KAAK,KAAK;QACjB;MACF;IACF;EACF;AACA,SAAO;AACT;AAEO,SAAS,gBAAgB,MAAc;AAE5C,QAAM,EAAE,gBAAgB,IAAI;AAC5B,QAAM,kBAAkB;AACxB,MAAI;AACF,WAAO,OAAO,IAAI;EACpB,UAAA;AACE,UAAM,kBAAkB;EAC1B;AACF;AE/EO,IAAM,kBAAkB,OAAO,IAAI,qBAAqB;AA0BxD,SAAS,UACd,UAKmB;AACnB,SAAO,EAAE,CAAC,eAAe,GAAG,MAAM,SAAS;AAC7C;AAEO,SAAS,YAAY,OAAoC;AAC9D,SACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,MAAM,eAAe,MAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,OACmB;AACnB,SAAO,YAAY,KAAK,IAAI,QAAQ,wBAAwB,KAAK;AACnE;AAEO,SAAS,wBACd,gBACmB;AACnB,SAAO,UAAU,OAAM,UAAS;AAC9B,UAAM,SAAS,MAAM,eAAe,WAAW,EAAE,SAAS,KAAK;AAE/D,WAAO,OAAO,UAAU,OACpB,EAAE,SAAS,MAAM,OAAO,OAAO,MAAM,IACrC;MACE,SAAS;MACT,OAAO,IAAI,oBAAoB;QAC7B;QACA,OAAO,OAAO;MAChB,CAAC;IACH;EACN,CAAC;AACH;AD7DA,eAAsB,cAAsB;EAC1C;EACA;AACF,GAGoB;AAClB,QAAM,SAAS,MAAM,kBAAkB,EAAE,OAAO,OAAO,CAAC;AAExD,MAAI,CAAC,OAAO,SAAS;AACnB,UAAMC,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;EAC/D;AAEA,SAAO,OAAO;AAChB;AAWA,eAAsB,kBAA0B;EAC9C;EACA;AACF,GAcE;AACA,QAAMC,aAAY,YAAY,MAAM;AAEpC,MAAI;AACF,QAAIA,WAAU,YAAY,MAAM;AAC9B,aAAO,EAAE,SAAS,MAAM,OAAwB,UAAU,MAAM;IAClE;AAEA,UAAM,SAAS,MAAMA,WAAU,SAAS,KAAK;AAE7C,QAAI,OAAO,SAAS;AAClB,aAAO,EAAE,SAAS,MAAM,OAAO,OAAO,OAAO,UAAU,MAAM;IAC/D;AAEA,WAAO;MACL,SAAS;MACT,OAAOD,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;MAC9D,UAAU;IACZ;EACF,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAOA,oBAAoB,KAAK,EAAE,OAAO,OAAO,MAAM,CAAC;MACvD,UAAU;IACZ;EACF;AACF;AFhDA,eAAsB,UAAa;EACjC;EACA;AACF,GAGe;AACb,MAAI;AACF,UAAM,QAAQ,gBAAgB,IAAI;AAElC,QAAI,UAAU,MAAM;AAClB,aAAO;IACT;AAEA,WAAO,cAAiB,EAAE,OAAO,OAAO,CAAC;EAC3C,SAAS,OAAO;AACd,QACE,eAAe,WAAW,KAAK,KAC/BA,oBAAoB,WAAW,KAAK,GACpC;AACA,YAAM;IACR;AAEA,UAAM,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;EACjD;AACF;AAgCA,eAAsB,cAAiB;EACrC;EACA;AACF,GAG4B;AAC1B,MAAI;AACF,UAAM,QAAQ,gBAAgB,IAAI;AAElC,QAAI,UAAU,MAAM;AAClB,aAAO,EAAE,SAAS,MAAM,OAAmB,UAAU,MAAM;IAC7D;AAEA,WAAO,MAAM,kBAAqB,EAAE,OAAO,OAAO,CAAC;EACrD,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAO,eAAe,WAAW,KAAK,IAClC,QACA,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;MAC7C,UAAU;IACZ;EACF;AACF;AAEO,SAAS,eAAe,OAAwB;AACrD,MAAI;AACF,oBAAgB,KAAK;AACrB,WAAO;EACT,SAAQ,GAAA;AACN,WAAO;EACT;AACF;AIjHO,SAAS,qBAAwB;EACtC;EACA;AACF,GAGmC;AACjC,SAAO,OACJ,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,IAAI,wBAAwB,CAAC,EACzC;IACC,IAAI,gBAAoD;MACtD,MAAM,UAAU,EAAE,KAAK,GAAG,YAAY;AAEpC,YAAI,SAAS,UAAU;AACrB;QACF;AAEA,mBAAW,QAAQ,MAAM,cAAc,EAAE,MAAM,MAAM,OAAO,CAAC,CAAC;MAChE;IACF,CAAC;EACH;AACJ;AEvBA,IAAME,oBAAmB,MAAM,WAAW;AAEnC,IAAM,gBAAgB,OAAU;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MASE,UAAU;EACR;EACA,SAAS;IACP,gBAAgB;KACb;EAEL,MAAM;IACJ,SAAS,KAAK,UAAU,IAAI;IAC5B,QAAQ;EACV;EACA;EACA;EACA;EACA;AACF,CAAC;AAgCI,IAAM,YAAY,OAAU;EACjC;EACA,UAAU,CAAC;EACX;EACA;EACA;EACA;EACA,QAAQC,kBAAiB;AAC3B,MAWM;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK;MAChC,QAAQ;MACR,SAAS,uBAAuB,OAAO;MACvC,MAAM,KAAK;MACX,QAAQ;IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;UAC7C;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAKC,aAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;QACR;AAEA,cAAM,IAAIA,aAAa;UACrB,SAAS;UACT,OAAO;UACP,YAAY,SAAS;UACrB;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH;AAEA,YAAM,iBAAiB;IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;QACrC;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAKA,aAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;QACR;MACF;AAEA,YAAM,IAAIA,aAAa;QACrB,SAAS;QACT,OAAO;QACP,YAAY,SAAS;QACrB;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH;EACF,SAAS,OAAO;AACd,UAAM,iBAAiB,EAAE,OAAO,KAAK,mBAAmB,KAAK,OAAO,CAAC;EACvE;AACF;AI/IO,IAAM,iCACX,CAAI;EACF;EACA;EACA;AACF,MAKA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,kBAAkB,uBAAuB,QAAQ;AAGvD,MAAI,aAAa,KAAK,MAAM,IAAI;AAC9B,WAAO;MACL;MACA,OAAO,IAAIC,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AAGA,MAAI;AACF,UAAM,cAAc,MAAM,UAAU;MAClC,MAAM;MACN,QAAQ;IACV,CAAC;AAED,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,eAAe,WAAW;QACnC;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,MAAM;QACN,aAAa,eAAA,OAAA,SAAA,YAAc,UAAU,WAAA;MACvC,CAAC;IACH;EACF,SAAS,YAAY;AACnB,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AACF;AAEK,IAAM,mCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;EACrC;AAEA,SAAO;IACL;IACA,OAAO,qBAAqB;MAC1B,QAAQ,SAAS;MACjB,QAAQ;IACV,CAAC;EACH;AACF;AAqCK,IAAM,4BACX,CAAI,mBACJ,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,QAAM,eAAe,MAAM,cAAc;IACvC,MAAM;IACN,QAAQ;EACV,CAAC;AAED,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM,IAAIC,aAAa;MACrB,SAAS;MACT,OAAO,aAAa;MACpB,YAAY,SAAS;MACrB;MACA;MACA;MACA;IACF,CAAC;EACH;AAEA,SAAO;IACL;IACA,OAAO,aAAa;IACpB,UAAU,aAAa;EACzB;AACF;AE7JF,IAAM,eAAe,OAAO,IAAI,kBAAkB;ACNlD,IAAM,EAAE,MAAM,KAAK,IAAI;AAQhB,SAAS,0BAA0B,OAA2B;AACnE,MAAI,eAAe;AAInB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAgB,OAAO,cAAc,MAAM,CAAC,CAAC;EAC/C;AAEA,SAAO,KAAK,YAAY;AAC1B;;;AQrBA,gBAAkB;AAQX,IAAM,+BAA+B,YAAE,OAAO;AAAA,EACnD,MAAM,YAAE,QAAQ,iCAA2B;AAAA,EAC3C,SAAS,YAAE,OAAO;AACpB,CAAC;AAKM,IAAM,iCAAiC,YAAE,OAAO;AAAA,EACrD,MAAM,YAAE,QAAQ,qCAA6B;AAAA,EAC7C,MAAM,YAAE,OAAO;AACjB,CAAC;AAKM,IAAM,4BAA4B,YAAE,OAAO;AAAA,EAChD,MAAM,YAAE,QAAQ,2BAAwB;AAAA,EACxC,MAAM,YAAE,OAAO,EAAE,QAAQ;AAAA,EACzB,WAAW,YAAE,OAAO,EAAE,QAAQ;AAChC,CAAC;AAIM,IAAM,6BAA6B,YAAE,MAAM;AAAA,EAChD;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,oCAAoC,YAAE,MAAM;AAAA,EAChD;AAAA,EACA,YAAE,QAAQ,EAAE,UAAU,MAAM,IAAI;AAClC,CAAC;AAIM,IAAM,6BAA6B,YACvC,MAAM,iCAAiC,EACvC,UAAU,CAAC,MAAM,EAAE,OAAO,CAACC,OAAiC,CAAC,CAACA,EAAC,CAAC;;;AC9CnE,IAAAC,aAAkB;AAEX,IAAM,gCAAgC,aAAE,OAAO;AAAA,EACpD,OAAO,aAAE,OAAO;AAAA,IACd,MAAM,aAAE,MAAM,CAAC,aAAE,OAAO,GAAG,aAAE,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,IAAI;AAAA,IAC1E,SAAS,aAAE,OAAO;AAAA,IAClB,MAAM,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,IAAI;AAAA,IACnD,OAAO,aAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,IAAI;AAAA,EACnD,CAAC;AACH,CAAC;AAIM,IAAM,kCAAkC,+BAA+B;AAAA,EAC5E,aAAa;AAAA,EACb,gBAAgB,CAAC,SAA8B,KAAK,MAAM;AAC5D,CAAC;;;ACfM,SAAS,0BACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;AClBO,SAAS,MAAM;AAAA,EACpB;AAAA,EACA;AACF,GAGY;AACV,MAAI;AACF,UAAM,SAAS,IAAI,IAAI,GAAG;AAE1B,WAAO,UAAU,IAAI,OAAO,QAAwB;AAAA,EACtD,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;;;ACTO,SAAS,WAAW;AAAA,EACzB;AAAA,EACA;AACF,GAGG;AAXH,MAAAC,MAAA;AAYE,MAAI,KAAK,gBAAgB,YAAY;AACnC,UAAM,SAAS,0BAA0B,KAAK,IAAI;AAClD,WAAO,SAAQA,OAAA,KAAK,cAAL,OAAAA,OAAkB,gBAAgB,WAAW,MAAM;AAAA,EACpE;AAEA,QAAM,YAAY,KAAK,KAAK,SAAS;AAErC,MACE,MAAM;AAAA,IACJ,KAAK;AAAA,IACL,WAAW,oBAAI,IAAI,CAAC,SAAS,QAAQ,CAAC;AAAA,EACxC,CAAC,GACD;AACA,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,WAAW,OAAO,IAC/B,YACA,SAAQ,UAAK,cAAL,YAAkB,gBAAgB,WAAW,SAAS;AACpE;;;ACXA,SAAS,gBACP,kBACoC;AAtBtC,MAAAC,MAAA;AAuBE,QAAM,YAAY,qDAAkB;AACpC,QAAM,aAAa,qDAAkB;AAGrC,UAAQ,YAAAA,OAAA,yCAAY,iBAAZ,OAAAA,OACN,yCAAY,kBADN,YAEN,uCAAW,iBAFL,YAGN,uCAAW;AACf;AAEO,SAAS,gCACd,QACgC;AAnClC,MAAAA,MAAA;AAoCE,QAAM,WAA2C,CAAC;AAClD,aAAW,EAAE,MAAM,SAAS,gBAAgB,KAAK,QAAQ;AACvD,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN;AAAA,UACA,eAAe,gBAAgB,eAAe;AAAA,QAChD,CAAC;AACD;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,YAAI,QAAQ,WAAW,OAAKA,OAAA,QAAQ,CAAC,MAAT,gBAAAA,KAAY,UAAS,QAAQ;AACvD,gBAAM,gBACJ,qBAAgB,eAAe,MAA/B,YACA,gBAAgB,QAAQ,CAAC,EAAE,eAAe;AAC5C,gBAAM,0BACJ,eACI;AAAA,YACE;AAAA,cACE,MAAM;AAAA,cACN,MAAM,QAAQ,CAAC,EAAE;AAAA,cACjB,eAAe;AAAA,YACjB;AAAA,UACF,IACA,QAAQ,CAAC,EAAE;AACjB,mBAAS,KAAK;AAAA,YACZ,MAAM;AAAA,YACN,SAAS;AAAA,UACX,CAAC;AACD;AAAA,QACF;AAGA,cAAM,sBAAsB,gBAAgB,eAAe;AAC3D,cAAM,eAA4C,QAAQ;AAAA,UACxD,CAAC,SAA4D;AAzEvE,gBAAAA,MAAAC,KAAAC,KAAA;AA0EY,kBAAM,gBACJF,OAAA,gBAAgB,KAAK,eAAe,MAApC,OAAAA,OAAyC;AAE3C,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK;AACH,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,MAAM,KAAK;AAAA;AAAA,kBAEX,eAAe;AAAA,gBACjB;AAAA,cACF,KAAK,QAAQ;AACX,qBAAIC,MAAA,KAAK,cAAL,gBAAAA,IAAgB,WAAW,WAAW;AACxC,wBAAM,MAAM,WAAW;AAAA,oBACrB;AAAA,oBACA,kBAAkB;AAAA,kBACpB,CAAC;AACD,yBAAO;AAAA,oBACL,MAAM;AAAA,oBACN,WAAW;AAAA,sBACT;AAAA,oBACF;AAAA;AAAA,oBAEA,eAAe;AAAA,kBACjB;AAAA,gBACF;AAEA,sBAAM,WAAW;AAAA,mBACf,kBAAAC,MAAA,KAAK,oBAAL,gBAAAA,IAAsB,eAAtB,mBAAkC,aAAlC,YACE,KAAK,aADP,YAEE;AAAA,gBACJ;AAEA,sBAAM,WAAW,WAAW;AAAA,kBAC1B;AAAA,kBACA,kBAAkB;AAAA,gBACpB,CAAC;AAED,oBACE,MAAM;AAAA,kBACJ,KAAK;AAAA,kBACL,WAAW,oBAAI,IAAI,CAAC,SAAS,QAAQ,CAAC;AAAA,gBACxC,CAAC,GACD;AACA,yBAAO;AAAA,oBACL,MAAM;AAAA,oBACN,MAAM;AAAA,sBACJ,UAAU;AAAA,sBACV,WAAW;AAAA,oBACb;AAAA,kBACF;AAAA,gBACF;AAEA,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,MAAM;AAAA,oBACJ,UAAU;AAAA,oBACV,WAAW;AAAA,kBACb;AAAA,kBACA,eAAe;AAAA,gBACjB;AAAA,cACF;AAAA,cACA,SAAS;AACP,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,eAAe;AAAA,gBACjB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGA,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAED;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,YAAI,OAAO;AACX,YAAI,YAAY;AAChB,cAAM,mBAA2C,CAAC;AAClD,cAAM,YAID,CAAC;AAEN,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,sBAAQ,KAAK;AACb;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,wBAAU,KAAK;AAAA,gBACb,IAAI,KAAK;AAAA,gBACT,MAAM;AAAA,gBACN,UAAU;AAAA,kBACR,MAAM,KAAK;AAAA,kBACX,WAAW,KAAK,UAAU,KAAK,KAAK;AAAA,gBACtC;AAAA,cACF,CAAC;AACD;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,2BAAa,KAAK;AAClB,+BAAiB,KAAK;AAAA,gBACpB;AAAA,gBACA,MAAM,KAAK;AAAA,cACb,CAAC;AAED;AAAA,YACF;AAAA,YAEA,KAAK;AACH;AAAA,YACF,SAAS;AACP;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,YAAY,UAAU,SAAS,IAAI,YAAY;AAAA,UAC/C,WAAW,aAAa;AAAA,UACxB,mBACE,iBAAiB,SAAS,IAAI,mBAAmB;AAAA,UACnD,eAAe,gBAAgB,eAAe;AAAA,QAChD,CAAC;AAED;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,mBAAW,gBAAgB,SAAS;AAClC,gBAAMC,WAAU,qBAAqB,YAAY;AAEjD,mBAAS,KAAK;AAAA,YACZ,MAAM;AAAA,YACN,cAAc,aAAa;AAAA,YAC3B,SAAAA;AAAA,YACA,gBACE,qBAAgB,eAAe,MAA/B,YACA,gBAAgB,aAAa,eAAe;AAAA,UAChD,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MAEA,SAAS;AACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,qBAAqB,OAA8C;AAC1E,SAAO,MAAM,OAAO,SAAS,SACzB,MAAM,OAAO,QACb,KAAK,UAAU,MAAM,OAAO,KAAK;AACvC;;;ACjPA,IAAAC,aAAkB;AAElB,IAAM,iCAAiC,aAAE,MAAM;AAAA,EAC7C,aAAE,QAAQ,MAAM;AAAA,EAChB,aAAE,QAAQ,MAAM;AAAA,EAChB,aAAE,QAAQ,UAAU;AAAA,EACpB,aAAE,OAAO;AAAA,IACP,MAAM,aAAE,QAAQ,UAAU;AAAA,IAC1B,UAAU,aAAE,OAAO;AAAA,MACjB,MAAM,aAAE,OAAO;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AAIM,SAAS,4BACd,YAC0B;AAC1B,UAAQ,WAAW,MAAM;AAAA,IACvB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,WAAW;AAAA,IACpB,KAAK,QAAQ;AACX,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU,EAAE,MAAM,WAAW,SAAS;AAAA,MACxC;AAAA,IACF;AAAA,IACA,SAAS;AACP;AACA,YAAM,IAAI,MAAM,6BAA6B,UAAU,EAAE;AAAA,IAC3D;AAAA,EACF;AACF;;;ACrCA,IAAAC,aAAkB;AAIlB,IAAM,6CAA6C,aAAE,OAAO;AAAA,EAC1D,IAAI,aAAE,OAAO,EAAE,SAAS;AAAA,EACxB,OAAO,aAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,UAAU,aAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,OAAO,aACJ,OAAO;AAAA,IACN,eAAe,aAAE,OAAO;AAAA,IACxB,uBAAuB,aACpB,OAAO;AAAA,MACN,eAAe,aAAE,OAAO;AAAA,IAC1B,CAAC,EACA,QAAQ;AAAA,IACX,mBAAmB,aAAE,OAAO;AAAA,IAC5B,2BAA2B,aACxB,OAAO;AAAA,MACN,kBAAkB,aAAE,OAAO;AAAA,IAC7B,CAAC,EACA,QAAQ;AAAA,IACX,cAAc,aAAE,OAAO;AAAA,IACvB,MAAM,aAAE,OAAO,EAAE,SAAS;AAAA,IAC1B,cAAc,aACX,OAAO;AAAA,MACN,yBAAyB,aAAE,OAAO,EAAE,QAAQ;AAAA,IAC9C,CAAC,EACA,QAAQ;AAAA,EACb,CAAC,EACA,QAAQ;AACb,CAAC;AAGM,IAAM,kDACX,2CAA2C,OAAO;AAAA,EAChD,SAAS,aAAE;AAAA,IACT,aAAE,OAAO;AAAA,MACP,SAAS,aAAE,OAAO;AAAA,QAChB,MAAM,aAAE,QAAQ,WAAW;AAAA,QAC3B,SAAS,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,QACxC,WAAW,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,QAC1C,mBAAmB,2BAA2B,QAAQ;AAAA,QAEtD,YAAY,aACT;AAAA,UACC,aAAE,OAAO;AAAA,YACP,IAAI,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,YACnC,MAAM,aAAE,QAAQ,UAAU;AAAA,YAC1B,UAAU,aAAE,OAAO;AAAA,cACjB,MAAM,aAAE,OAAO;AAAA,cACf,WAAW,aAAE,OAAO;AAAA,YACtB,CAAC;AAAA,UACH,CAAC;AAAA,QACH,EACC,SAAS;AAAA,QAEZ,aAAa,aACV;AAAA,UACC,aAAE,OAAO;AAAA,YACP,MAAM,aAAE,KAAK,CAAC,cAAc,CAAC;AAAA,YAC7B,cAAc,aAAE,OAAO;AAAA,cACrB,WAAW,aAAE,OAAO;AAAA,cACpB,aAAa,aAAE,OAAO;AAAA,cACtB,OAAO,aAAE,OAAO;AAAA,cAChB,KAAK,aAAE,OAAO;AAAA,cACd,SAAS,aAAE,OAAO,EAAE,SAAS;AAAA,YAC/B,CAAC;AAAA,UACH,CAAC;AAAA,QACH,EACC,QAAQ;AAAA,MACb,CAAC;AAAA,MACD,OAAO,aAAE,OAAO,EAAE,QAAQ;AAAA,MAC1B,UAAU,aACP,OAAO;AAAA,QACN,SAAS,aACN;AAAA,UACC,aAAE,OAAO;AAAA,YACP,OAAO,aAAE,OAAO;AAAA,YAChB,SAAS,aAAE,OAAO;AAAA,YAClB,cAAc,aAAE;AAAA,cACd,aAAE,OAAO;AAAA,gBACP,OAAO,aAAE,OAAO;AAAA,gBAChB,SAAS,aAAE,OAAO;AAAA,cACpB,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH,EACC,SAAS;AAAA,MACd,CAAC,EACA,SAAS,EACT,SAAS;AAAA,MACZ,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,IAChD,CAAC;AAAA,EACH;AACF,CAAC;AAGI,IAAM,4CAA4C,aAAE,MAAM;AAAA,EAC/D,2CAA2C,OAAO;AAAA,IAChD,SAAS,aAAE;AAAA,MACT,aAAE,OAAO;AAAA,QACP,OAAO,aACJ,OAAO;AAAA,UACN,MAAM,aAAE,KAAK,CAAC,WAAW,CAAC,EAAE,SAAS;AAAA,UACrC,SAAS,aAAE,OAAO,EAAE,QAAQ;AAAA,UAC5B,WAAW,aAAE,OAAO,EAAE,QAAQ,EAAE,SAAS;AAAA,UACzC,mBAAmB,2BAA2B,QAAQ;AAAA,UACtD,YAAY,aACT;AAAA,YACC,aAAE,OAAO;AAAA,cACP,OAAO,aAAE,OAAO,EAAE,QAAQ;AAAA,cAC1B,IAAI,aAAE,OAAO,EAAE,QAAQ;AAAA,cACvB,MAAM,aAAE,QAAQ,UAAU,EAAE,SAAS;AAAA,cACrC,UAAU,aAAE,OAAO;AAAA,gBACjB,MAAM,aAAE,OAAO,EAAE,QAAQ;AAAA,gBACzB,WAAW,aAAE,OAAO,EAAE,QAAQ;AAAA,cAChC,CAAC;AAAA,YACH,CAAC;AAAA,UACH,EACC,QAAQ;AAAA,UAEX,aAAa,aACV;AAAA,YACC,aAAE,OAAO;AAAA,cACP,MAAM,aAAE,KAAK,CAAC,cAAc,CAAC;AAAA,cAC7B,cAAc,aAAE,OAAO;AAAA,gBACrB,WAAW,aAAE,OAAO;AAAA,gBACpB,aAAa,aAAE,OAAO;AAAA,gBACtB,OAAO,aAAE,OAAO;AAAA,gBAChB,KAAK,aAAE,OAAO;AAAA,gBACd,SAAS,aAAE,OAAO,EAAE,SAAS;AAAA,cAC/B,CAAC;AAAA,YACH,CAAC;AAAA,UACH,EACC,QAAQ;AAAA,QACb,CAAC,EACA,QAAQ;AAAA,QACX,UAAU,aACP,OAAO;AAAA,UACN,SAAS,aACN;AAAA,YACC,aAAE,OAAO;AAAA,cACP,OAAO,aAAE,OAAO;AAAA,cAChB,SAAS,aAAE,OAAO;AAAA,cAClB,cAAc,aAAE;AAAA,gBACd,aAAE,OAAO;AAAA,kBACP,OAAO,aAAE,OAAO;AAAA,kBAChB,SAAS,aAAE,OAAO;AAAA,gBACpB,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH,EACC,SAAS;AAAA,QACd,CAAC,EACA,QAAQ;AAAA,QACX,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,QAC9C,OAAO,aAAE,OAAO,EAAE,QAAQ;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAAA,EACD;AACF,CAAC;;;AClHM,IAAM,8BAAN,MAA6D;AAAA,EAkBlE,YACE,SACA,UACA,QACA;AArBF,SAAS,uBAAuB;AAChC,SAAS,WAAW;AACpB,SAAS,8BAA8B;AAGvC,SAAS,gBAA0C;AAAA,MACjD,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,iBAAiB,CAAC,uBAAuB,iBAAiB;AAAA,IAC5D;AAUE,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAEQ,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAA+B;AAzFjC,QAAAC;AA0FI,UAAM,WAAW;AAAA;AAAA,MAEf,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK,SAAS;AAAA;AAAA,MAGtB,YAAY,KAAK,SAAS;AAAA,MAC1B,UACE,KAAK,SAAS,aAAa,QAC3B,OAAO,KAAK,SAAS,aAAa,WAC9B,OACA;AAAA,MACN,cACE,OAAO,KAAK,SAAS,aAAa,WAC9B,KAAK,SAAS,WACd,OAAO,KAAK,SAAS,aAAa,YAChC,KAAK,SAAS,WACZ,IACA,SACF;AAAA,MACR,MAAM,KAAK,SAAS;AAAA,MACpB,qBAAqB,KAAK,SAAS;AAAA;AAAA,MAGnC,YAAY;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB;AAAA,MAEA,MAAM;AAAA,MACN,iBAAiB;AAAA,MACjB,OAAO;AAAA;AAAA,MAGP,UAAU,gCAAgC,MAAM;AAAA;AAAA,MAGhD,mBAAmB,KAAK,SAAS;AAAA,MACjC,WAAW,KAAK,SAAS;AAAA,MACzB,OAAO,KAAK,SAAS;AAAA;AAAA,MAGrB,SAAS,KAAK,SAAS;AAAA,MACvB,oBAAoB,KAAK,SAAS;AAAA;AAAA,MAElC,UAAU,KAAK,SAAS;AAAA,OAGrB,KAAK,OAAO,YACZ,KAAK,SAAS;AAGnB,SAAI,iDAAgB,UAAS,UAAU,eAAe,UAAU,MAAM;AACpE,aAAO,iCACF,WADE;AAAA,QAEL,iBAAiB;AAAA,UACf,MAAM;AAAA,UACN,aAAa;AAAA,YACX,QAAQ,eAAe;AAAA,YACvB,QAAQ;AAAA,YACR,OAAMA,OAAA,eAAe,SAAf,OAAAA,OAAuB;AAAA,aACzB,eAAe,eAAe;AAAA,YAChC,aAAa,eAAe;AAAA,UAC9B;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,MAAM,SAAS,GAAG;AAE7B,YAAM,cAAc,MACjB,OAAO,CAAC,SAAS,KAAK,SAAS,UAAU,EACzC,IAAI,CAAC,UAAU;AAAA,QACd,MAAM;AAAA,QACN,UAAU;AAAA,UACR,MAAM,KAAK;AAAA,UACX,aAAa,KAAK;AAAA,UAClB,YAAY,KAAK;AAAA,QACnB;AAAA,MACF,EAAE;AAEJ,aAAO,iCACF,WADE;AAAA,QAEL,OAAO;AAAA,QACP,aAAa,aACT,4BAA4B,UAAU,IACtC;AAAA,MACN;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,WAAW,SAgBd;AA1ML,QAAAA,MAAA;AA2MI,UAAM,kBAAkB,QAAQ,mBAAmB,CAAC;AACpD,UAAM,oBAAoB,gBAAgB,cAAc,CAAC;AAEzD,UAAM,OAAO,kCACR,KAAK,QAAQ,OAAO,IACpB;AAGL,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,KAAK,OAAO,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,2BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,SAAS,SAAS,QAAQ,CAAC;AAEjC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAGA,UAAM,YAAkC,SAAS,QAC7C;AAAA,MACE,cAAaA,OAAA,SAAS,MAAM,kBAAf,OAAAA,OAAgC;AAAA,MAC7C,eAAc,cAAS,MAAM,sBAAf,YAAoC;AAAA,MAClD,eACG,cAAS,MAAM,kBAAf,YAAgC,OAChC,cAAS,MAAM,sBAAf,YAAoC;AAAA,MACvC,kBACE,oBAAS,MAAM,8BAAf,mBAA0C,qBAA1C,YAA8D;AAAA,MAChE,oBACE,oBAAS,MAAM,0BAAf,mBAAsC,kBAAtC,YAAuD;AAAA,IAC3D,IACA;AAAA,MACE,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,mBAAmB;AAAA,IACrB;AAEJ,UAAM,oBAAmB,YAAO,QAAQ,sBAAf,YAAoC,CAAC;AAE9D,UAAM,YACJ,iBAAiB,SAAS,IACtB,iBACG,IAAI,CAAC,WAAW;AACf,cAAQ,OAAO,MAAM;AAAA,QACnB,kCAA+B;AAC7B,cAAI,OAAO,MAAM;AACf,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,MAAM,OAAO;AAAA,YACf;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,wCAAkC;AAChC,cAAI,OAAO,SAAS;AAClB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,MAAM,OAAO;AAAA,YACf;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,4CAAoC;AAElC,cAAI,OAAO,MAAM;AACf,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC,EACA,OAAO,CAAC,MAAM,MAAM,IAAI,IAC3B,OAAO,QAAQ,YACb;AAAA,MACE;AAAA,QACE,MAAM;AAAA,QACN,MAAM,OAAO,QAAQ;AAAA,MACvB;AAAA,IACF,IACA,CAAC;AAET,UAAM,UAAyC,CAAC;AAGhD,YAAQ,KAAK,GAAG,SAAS;AAEzB,QAAI,OAAO,QAAQ,SAAS;AAC1B,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,MAAM,OAAO,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,QAAI,OAAO,QAAQ,YAAY;AAC7B,iBAAW,YAAY,OAAO,QAAQ,YAAY;AAChD,gBAAQ,KAAK;AAAA,UACX,MAAM;AAAA,UACN,aAAY,cAAS,OAAT,YAAe,WAAW;AAAA,UACtC,UAAU,SAAS,SAAS;AAAA,UAC5B,OAAO,SAAS,SAAS;AAAA,QAC3B,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,OAAO,QAAQ,aAAa;AAC9B,iBAAW,cAAc,OAAO,QAAQ,aAAa;AACnD,YAAI,WAAW,SAAS,gBAAgB;AACtC,kBAAQ,KAAK;AAAA,YACX,MAAM;AAAA,YACN,YAAY;AAAA,YACZ,IAAI,WAAW,aAAa;AAAA,YAC5B,KAAK,WAAW,aAAa;AAAA,YAC7B,OAAO,WAAW,aAAa;AAAA,YAC/B,kBAAkB;AAAA,cAChB,YAAY;AAAA,gBACV,SAAS,WAAW,aAAa,WAAW;AAAA,cAC9C;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL;AAAA,MACA,cAAc,0BAA0B,OAAO,aAAa;AAAA,MAC5D,OAAO;AAAA,MACP,UAAU,CAAC;AAAA,MACX,kBAAkB;AAAA,QAChB,YAAY;AAAA,UACV,WAAU,cAAS,aAAT,YAAqB;AAAA,UAC/B,OAAO;AAAA,YACL,eAAc,eAAU,gBAAV,YAAyB;AAAA,YACvC,mBAAkB,eAAU,iBAAV,YAA0B;AAAA,YAC5C,cAAa,eAAU,gBAAV,YAAyB;AAAA,YACtC,OAAM,cAAS,UAAT,mBAAgB;AAAA,YACtB,qBAAqB;AAAA,cACnB,eACE,0BAAS,UAAT,mBAAgB,0BAAhB,mBAAuC,kBAAvC,YAAwD;AAAA,YAC5D;AAAA,YACA,yBAAyB;AAAA,cACvB,kBACE,0BAAS,UAAT,mBAAgB,8BAAhB,mBAA2C,qBAA3C,YACA;AAAA,YACJ;AAAA,YACA,aAAa;AAAA,cACX,wBACE,0BAAS,UAAT,mBAAgB,iBAAhB,mBAA8B,4BAA9B,YAAyD;AAAA,YAC7D;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS,EAAE,MAAM,KAAK;AAAA,MACtB,UAAU;AAAA,QACR,IAAI,SAAS;AAAA,QACb,SAAS,SAAS;AAAA,QAClB,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SAAS,SAQZ;AAxYL,QAAAA;AAyYI,UAAM,kBAAkB,QAAQ,mBAAmB,CAAC;AACpD,UAAM,oBAAoB,gBAAgB,cAAc,CAAC;AAEzD,UAAM,OAAO,kCACR,KAAK,QAAQ,OAAO,IACpB;AAGL,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,KAAK,OAAO,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM,iCACD,OADC;AAAA,QAEJ,QAAQ;AAAA;AAAA,QAGR,gBACE,KAAK,OAAO,kBAAkB,WAC1B;AAAA,UACE,eAAe;AAAA,aAEXA,OAAA,KAAK,SAAS,UAAd,gBAAAA,KAAqB,WACrB,EAAE,eAAe,KAAK,IACtB,CAAC,KAEP;AAAA,MACR;AAAA,MACA,uBAAuB;AAAA,MACvB,2BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,YASD,CAAC;AAEN,QAAI,eAA6B;AACjC,UAAM,QAA8B;AAAA,MAClC,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,MACrB,aAAa,OAAO;AAAA,MACpB,iBAAiB,OAAO;AAAA,MACxB,mBAAmB,OAAO;AAAA,IAC5B;AAGA,UAAM,kBAAsD,CAAC;AAE7D,QAAI,cAAc;AAClB,QAAI,mBAAmB;AACvB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAKF;AAAA,UACA,UAAU,OAAO,YAAY;AArdvC,gBAAAA,MAAA;AAudY,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,WAAW,OAAO;AACpB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,gBAAI,MAAM,UAAU;AAClB,yBAAW,MAAM;AAAA,YACnB;AAEA,gBAAI,MAAM,IAAI;AACZ,qCAAuB,MAAM;AAC7B,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,IAAI,MAAM;AAAA,cACZ,CAAC;AAAA,YACH;AAEA,gBAAI,MAAM,OAAO;AACf,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,SAAS,MAAM;AAAA,cACjB,CAAC;AAAA,YACH;AAEA,gBAAI,MAAM,SAAS,MAAM;AACvB,oBAAM,cAAc,MAAM,MAAM;AAChC,oBAAM,eAAe,MAAM,MAAM;AACjC,oBAAM,cACJ,MAAM,MAAM,gBAAgB,MAAM,MAAM;AAG1C,8BAAgB,eAAe,MAAM,MAAM;AAE3C,kBAAI,MAAM,MAAM,uBAAuB;AACrC,sBAAM,qBACJA,OAAA,MAAM,MAAM,sBAAsB,kBAAlC,OAAAA,OAAmD;AAErD,sBAAM,oBAAoB;AAC1B,gCAAgB,sBAAsB;AAAA,kBACpC,cAAc;AAAA,gBAChB;AAAA,cACF;AAEA,8BAAgB,mBAAmB,MAAM,MAAM;AAC/C,kBAAI,MAAM,MAAM,2BAA2B;AACzC,sBAAM,mBACJ,WAAM,MAAM,0BAA0B,qBAAtC,YAA0D;AAE5D,sBAAM,kBAAkB;AACxB,gCAAgB,0BAA0B;AAAA,kBACxC;AAAA,gBACF;AAAA,cACF;AAEA,8BAAgB,OAAO,MAAM,MAAM;AACnC,8BAAgB,cAAc,MAAM,MAAM;AAAA,YAC5C;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,iBAAI,iCAAQ,kBAAiB,MAAM;AACjC,6BAAe,0BAA0B,OAAO,aAAa;AAAA,YAC/D;AAEA,iBAAI,iCAAQ,UAAS,MAAM;AACzB;AAAA,YACF;AAEA,kBAAM,QAAQ,OAAO;AAErB,kBAAM,qBAAqB,CAAC,cAAsB;AAChD,kBAAI,CAAC,kBAAkB;AACrB,8BAAc,wBAAwB,WAAW;AACjD,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,IAAI;AAAA,gBACN,CAAC;AACD,mCAAmB;AAAA,cACrB;AACA,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,IAAI,eAAe,WAAW;AAAA,cAChC,CAAC;AAAA,YACH;AAEA,gBAAI,MAAM,qBAAqB,MAAM,kBAAkB,SAAS,GAAG;AACjE,yBAAW,UAAU,MAAM,mBAAmB;AAC5C,wBAAQ,OAAO,MAAM;AAAA,kBACnB,kCAA+B;AAC7B,wBAAI,OAAO,MAAM;AACf,yCAAmB,OAAO,IAAI;AAAA,oBAChC;AACA;AAAA,kBACF;AAAA,kBACA,4CAAoC;AAClC,wBAAI,OAAO,MAAM;AACf,yCAAmB,YAAY;AAAA,oBACjC;AACA;AAAA,kBACF;AAAA,kBACA,wCAAkC;AAChC,wBAAI,OAAO,SAAS;AAClB,yCAAmB,OAAO,OAAO;AAAA,oBACnC;AACA;AAAA,kBACF;AAAA,kBACA,SAAS;AACP;AACA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,WAAW,MAAM,WAAW;AAC1B,iCAAmB,MAAM,SAAS;AAAA,YACpC;AAEA,gBAAI,MAAM,SAAS;AAGjB,kBAAI,oBAAoB,CAAC,aAAa;AACpC,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,IAAI,eAAe,WAAW;AAAA,gBAChC,CAAC;AACD,mCAAmB;AAAA,cACrB;AAEA,kBAAI,CAAC,aAAa;AAChB,yBAAS,wBAAwB,WAAW;AAC5C,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,IAAI;AAAA,gBACN,CAAC;AACD,8BAAc;AAAA,cAChB;AACA,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,OAAO,MAAM;AAAA,gBACb,IAAI,UAAU,WAAW;AAAA,cAC3B,CAAC;AAAA,YACH;AAEA,gBAAI,MAAM,aAAa;AACrB,yBAAW,cAAc,MAAM,aAAa;AAC1C,oBAAI,WAAW,SAAS,gBAAgB;AACtC,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,YAAY;AAAA,oBACZ,IAAI,WAAW,aAAa;AAAA,oBAC5B,KAAK,WAAW,aAAa;AAAA,oBAC7B,OAAO,WAAW,aAAa;AAAA,oBAC/B,kBAAkB;AAAA,sBAChB,YAAY;AAAA,wBACV,SAAS,WAAW,aAAa,WAAW;AAAA,sBAC9C;AAAA,oBACF;AAAA,kBACF,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,MAAM,cAAc,MAAM;AAC5B,yBAAW,iBAAiB,MAAM,YAAY;AAC5C,sBAAM,SAAQ,mBAAc,UAAd,YAAuB,UAAU,SAAS;AAGxD,oBAAI,UAAU,KAAK,KAAK,MAAM;AAC5B,sBAAI,cAAc,SAAS,YAAY;AACrC,0BAAM,IAAI,yBAAyB;AAAA,sBACjC,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX,CAAC;AAAA,kBACH;AAEA,sBAAI,cAAc,MAAM,MAAM;AAC5B,0BAAM,IAAI,yBAAyB;AAAA,sBACjC,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX,CAAC;AAAA,kBACH;AAEA,wBAAI,mBAAc,aAAd,mBAAwB,SAAQ,MAAM;AACxC,0BAAM,IAAI,yBAAyB;AAAA,sBACjC,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX,CAAC;AAAA,kBACH;AAEA,4BAAU,KAAK,IAAI;AAAA,oBACjB,IAAI,cAAc;AAAA,oBAClB,MAAM;AAAA,oBACN,UAAU;AAAA,sBACR,MAAM,cAAc,SAAS;AAAA,sBAC7B,YAAW,mBAAc,SAAS,cAAvB,YAAoC;AAAA,oBACjD;AAAA,oBACA,cAAc;AAAA,oBACd,MAAM;AAAA,kBACR;AAEA,wBAAMC,YAAW,UAAU,KAAK;AAEhC,sBAAIA,aAAY,MAAM;AACpB,0BAAM,IAAI,MAAM,sBAAsB;AAAA,kBACxC;AAGA,wBACE,KAAAA,UAAS,aAAT,mBAAmB,SAAQ,UAC3B,KAAAA,UAAS,aAAT,mBAAmB,cAAa,QAChC,eAAeA,UAAS,SAAS,SAAS,GAC1C;AACA,oBAAAA,UAAS,eAAe;AAExB,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,IAAIA,UAAS;AAAA,sBACb,UAAUA,UAAS,SAAS;AAAA,oBAC9B,CAAC;AAGD,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,IAAIA,UAAS;AAAA,sBACb,OAAOA,UAAS,SAAS;AAAA,oBAC3B,CAAC;AAED,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,IAAIA,UAAS;AAAA,oBACf,CAAC;AAGD,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,YAAYA,UAAS;AAAA,sBACrB,UAAUA,UAAS,SAAS;AAAA,sBAC5B,OAAOA,UAAS,SAAS;AAAA,oBAC3B,CAAC;AAED,oBAAAA,UAAS,OAAO;AAAA,kBAClB;AAEA;AAAA,gBACF;AAGA,sBAAM,WAAW,UAAU,KAAK;AAEhC,oBAAI,YAAY,MAAM;AACpB,wBAAM,IAAI,MAAM,sBAAsB;AAAA,gBACxC;AAEA,oBAAI,CAAC,SAAS,cAAc;AAC1B,2BAAS,eAAe;AACxB,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,IAAI,SAAS;AAAA,oBACb,UAAU,SAAS,SAAS;AAAA,kBAC9B,CAAC;AAAA,gBACH;AAEA,sBAAI,mBAAc,aAAd,mBAAwB,cAAa,MAAM;AAC7C,2BAAS,SAAS,cAChB,yBAAc,aAAd,mBAAwB,cAAxB,YAAqC;AAAA,gBACzC;AAGA,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,IAAI,SAAS;AAAA,kBACb,QAAO,mBAAc,SAAS,cAAvB,YAAoC;AAAA,gBAC7C,CAAC;AAGD,sBACE,cAAS,aAAT,mBAAmB,SAAQ,UAC3B,cAAS,aAAT,mBAAmB,cAAa,QAChC,eAAe,SAAS,SAAS,SAAS,GAC1C;AACA,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,aAAY,cAAS,OAAT,YAAe,WAAW;AAAA,oBACtC,UAAU,SAAS,SAAS;AAAA,oBAC5B,OAAO,SAAS,SAAS;AAAA,kBAC3B,CAAC;AAED,2BAAS,OAAO;AAAA,gBAClB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UAEA,MAAM,YAAY;AAtwB5B,gBAAAD;AAwwBY,gBAAI,iBAAiB,cAAc;AACjC,yBAAW,YAAY,WAAW;AAChC,oBAAI,YAAY,CAAC,SAAS,MAAM;AAC9B,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,aAAYA,OAAA,SAAS,OAAT,OAAAA,OAAe,WAAW;AAAA,oBACtC,UAAU,SAAS,SAAS;AAAA;AAAA,oBAE5B,OAAO,eAAe,SAAS,SAAS,SAAS,IAC7C,SAAS,SAAS,YAClB;AAAA,kBACN,CAAC;AACD,2BAAS,OAAO;AAAA,gBAClB;AAAA,cACF;AAAA,YACF;AAGA,gBAAI,kBAAkB;AACpB,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,IAAI,eAAe,WAAW;AAAA,cAChC,CAAC;AAAA,YACH;AACA,gBAAI,aAAa;AACf,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,IAAI,UAAU,WAAW;AAAA,cAC3B,CAAC;AAAA,YACH;AAEA,kBAAM,qBAGF;AAAA,cACF,OAAO;AAAA,YACT;AAGA,gBAAI,aAAa,QAAW;AAC1B,iCAAmB,WAAW;AAAA,YAChC;AAEA,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA,kBAAkB;AAAA,gBAChB,YAAY;AAAA,cACd;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,UAAU,CAAC;AAAA,MACX,SAAS,EAAE,MAAM,KAAK;AAAA,MACtB,UAAU,EAAE,SAAS,gBAAgB;AAAA,IACvC;AAAA,EACF;AACF;;;ACrzBO,SAAS,oCAAoC;AAAA,EAClD;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,YAAY;AACd,GAOE;AAEA,MACE,gBAAgB,YAChB,OAAO,WAAW,KAClB,OAAO,CAAC,KACR,OAAO,CAAC,EAAE,SAAS,UACnB,OAAO,CAAC,EAAE,QAAQ,WAAW,KAC7B,OAAO,CAAC,EAAE,QAAQ,CAAC,KACnB,OAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,QAC9B;AACA,WAAO,EAAE,QAAQ,OAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,KAAK;AAAA,EAC7C;AAGA,MAAI,OAAO;AAGX,MAAI,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE,SAAS,UAAU;AAC5C,YAAQ,GAAG,OAAO,CAAC,EAAE,OAAO;AAAA;AAAA;AAC5B,aAAS,OAAO,MAAM,CAAC;AAAA,EACzB;AAEA,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,cAAM,IAAI,mBAAmB;AAAA,UAC3B,SAAS,wCAAwC,OAAO;AAAA,UACxD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,KAAK,QAAQ;AACX,cAAM,cAAc,QACjB,IAAI,CAAC,SAA4D;AAChE,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,qBAAO,KAAK;AAAA,YACd;AAAA,YAEA,KAAK,QAAQ;AACX,oBAAM,IAAI,8BAA8B;AAAA,gBACtC,eAAe;AAAA,cACjB,CAAC;AAAA,YACH;AAAA,YACA,SAAS;AACP,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC,EACA,KAAK,EAAE;AAEV,gBAAQ,GAAG,IAAI;AAAA,EAAM,WAAW;AAAA;AAAA;AAChC;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,cAAM,mBAAmB,QACtB;AAAA,UACC,CACE,SAMG;AACH,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO,KAAK;AAAA,cACd;AAAA,cACA,KAAK,aAAa;AAChB,sBAAM,IAAI,8BAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,cACA,KAAK,eAAe;AAClB,sBAAM,IAAI,8BAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,cACA,KAAK,aAAa;AAChB,sBAAM,IAAI,8BAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,cAEA,KAAK,QAAQ;AACX,sBAAM,IAAI,8BAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,cAEA,SAAS;AACP,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF,EACC,KAAK,EAAE;AAEV,gBAAQ,GAAG,SAAS;AAAA,EAAM,gBAAgB;AAAA;AAAA;AAC1C;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,cAAM,IAAI,8BAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MAEA,SAAS;AACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,UAAQ,GAAG,SAAS;AAAA;AAEpB,SAAO;AAAA,IACL,QAAQ;AAAA,EACV;AACF;;;ACrJA,IAAAE,aAAkB;AAMX,IAAM,kCAAkC,aAAE,MAAM;AAAA,EACrD,aAAE,OAAO;AAAA,IACP,IAAI,aAAE,OAAO,EAAE,SAAS;AAAA,IACxB,OAAO,aAAE,OAAO,EAAE,SAAS;AAAA,IAC3B,SAAS,aAAE;AAAA,MACT,aAAE,OAAO;AAAA,QACP,MAAM,aAAE,OAAO;AAAA,QACf,WAAW,aAAE,OAAO,EAAE,QAAQ,EAAE,SAAS;AAAA,QACzC,mBAAmB,2BAA2B,QAAQ;AAAA,QAEtD,eAAe,aAAE,OAAO,EAAE,QAAQ;AAAA,QAClC,OAAO,aAAE,OAAO,EAAE,QAAQ;AAAA,QAC1B,UAAU,aACP,OAAO;AAAA,UACN,QAAQ,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,UAC1B,gBAAgB,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,UAClC,cAAc,aAAE,MAAM,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,OAAO,CAAC,CAAC,EAAE,SAAS;AAAA,QACnE,CAAC,EACA,SAAS,EACT,SAAS;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA,OAAO,aACJ,OAAO;AAAA,MACN,eAAe,aAAE,OAAO;AAAA,MACxB,uBAAuB,aACpB,OAAO;AAAA,QACN,eAAe,aAAE,OAAO;AAAA,MAC1B,CAAC,EACA,QAAQ;AAAA,MACX,mBAAmB,aAAE,OAAO;AAAA,MAC5B,2BAA2B,aACxB,OAAO;AAAA,QACN,kBAAkB,aAAE,OAAO;AAAA,MAC7B,CAAC,EACA,QAAQ;AAAA,MACX,cAAc,aAAE,OAAO;AAAA,MACvB,MAAM,aAAE,OAAO,EAAE,SAAS;AAAA,IAC5B,CAAC,EACA,QAAQ;AAAA,EACb,CAAC;AAAA,EACD;AACF,CAAC;;;ACXM,IAAM,oCAAN,MAAmE;AAAA,EAiBxE,YACE,SACA,UACA,QACA;AApBF,SAAS,uBAAuB;AAChC,SAAS,WAAW;AAEpB,SAAS,gBAA0C;AAAA,MACjD,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU,CAAC,gBAAgB,iBAAiB;AAAA,MAC5C,iBAAiB,CAAC,uBAAuB,iBAAiB;AAAA,IAC5D;AACA,SAAS,8BAA8B;AAUrC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAEQ,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAA+B;AAC7B,UAAM,EAAE,QAAQ,iBAAiB,IAAI,oCAAoC;AAAA,MACvE;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAED,QAAI,+BAAO,QAAQ;AACjB,YAAM,IAAI,8BAA8B;AAAA,QACtC,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,QAAI,YAAY;AACd,YAAM,IAAI,8BAA8B;AAAA,QACtC,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,WAAO;AAAA;AAAA,MAEL,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK,SAAS;AAAA;AAAA,MAGtB,YAAY,KAAK,SAAS;AAAA,MAC1B,UACE,OAAO,KAAK,SAAS,aAAa,WAC9B,KAAK,SAAS,WACd,OAAO,KAAK,SAAS,aAAa,YAChC,KAAK,SAAS,WACZ,IACA,SACF;AAAA,MACR,QAAQ,KAAK,SAAS;AAAA,MACtB,MAAM,KAAK,SAAS;AAAA;AAAA,MAGpB,YAAY;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB;AAAA,MAEA,MAAM;AAAA,MACN,iBAAiB;AAAA,MACjB,OAAO;AAAA;AAAA,MAGP,QAAQ;AAAA;AAAA,MAGR,mBAAmB,KAAK,SAAS;AAAA,MACjC,WAAW,KAAK,SAAS;AAAA,OAGtB,KAAK,OAAO,YACZ,KAAK,SAAS;AAAA,EAErB;AAAA,EAEA,MAAM,WACJ,SAC6D;AA5IjE,QAAAC,MAAA;AA6II,UAAM,kBAAkB,QAAQ,mBAAmB,CAAC;AACpD,UAAM,oBAAoB,gBAAgB,cAAc,CAAC;AAEzD,UAAM,OAAO,kCACR,KAAK,QAAQ,OAAO,IACpB;AAGL,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,KAAK,OAAO,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,2BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,QAAI,WAAW,UAAU;AACvB,YAAM,IAAI,MAAM,GAAG,SAAS,MAAM,OAAO,EAAE;AAAA,IAC7C;AAEA,UAAM,SAAS,SAAS,QAAQ,CAAC;AAEjC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,OAAMA,OAAA,OAAO,SAAP,OAAAA,OAAe;AAAA,QACvB;AAAA,MACF;AAAA,MACA,cAAc,0BAA0B,OAAO,aAAa;AAAA,MAC5D,OAAO;AAAA,QACL,cAAa,oBAAS,UAAT,mBAAgB,kBAAhB,YAAiC;AAAA,QAC9C,eAAc,oBAAS,UAAT,mBAAgB,sBAAhB,YAAqC;AAAA,QACnD,eACG,oBAAS,UAAT,mBAAgB,kBAAhB,YAAiC,OACjC,oBAAS,UAAT,mBAAgB,sBAAhB,YAAqC;AAAA,QACxC,kBACE,0BAAS,UAAT,mBAAgB,8BAAhB,mBAA2C,qBAA3C,YAA+D;AAAA,QACjE,oBACE,0BAAS,UAAT,mBAAgB,0BAAhB,mBAAuC,kBAAvC,YAAwD;AAAA,MAC5D;AAAA,MACA,UAAU,CAAC;AAAA,MACX,UAAU;AAAA,QACR,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,kBAAkB,QAAQ,mBAAmB,CAAC;AACpD,UAAM,oBAAoB,gBAAgB,cAAc,CAAC;AAEzD,UAAM,OAAO,kCACR,KAAK,QAAQ,OAAO,IACpB;AAGL,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,KAAK,OAAO,IAAI;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM,iCACD,OADC;AAAA,QAEJ,QAAQ;AAAA;AAAA,QAGR,gBACE,KAAK,OAAO,kBAAkB,WAC1B,EAAE,eAAe,KAAK,IACtB;AAAA,MACR;AAAA,MACA,uBAAuB;AAAA,MACvB,2BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,QAAI,eAA6B;AACjC,UAAM,QAA8B;AAAA,MAClC,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,MACrB,aAAa,OAAO;AAAA,MACpB,iBAAiB,OAAO;AAAA,MACxB,mBAAmB,OAAO;AAAA,IAC5B;AAEA,UAAM,kBAAsD,CAAC;AAC7D,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AA3PvC,gBAAAA,MAAA;AA6PY,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,WAAW,OAAO;AACpB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,gBAAI,MAAM,SAAS,MAAM;AACvB,oBAAM,cAAc,MAAM,MAAM;AAChC,oBAAM,eAAe,MAAM,MAAM;AACjC,oBAAM,cACJ,MAAM,MAAM,gBAAgB,MAAM,MAAM;AAG1C,8BAAgB,eAAe,MAAM,MAAM;AAE3C,kBAAI,MAAM,MAAM,uBAAuB;AACrC,sBAAM,qBACJA,OAAA,MAAM,MAAM,sBAAsB,kBAAlC,OAAAA,OAAmD;AAErD,sBAAM,oBAAoB;AAC1B,gCAAgB,sBAAsB;AAAA,kBACpC,cAAc;AAAA,gBAChB;AAAA,cACF;AAEA,8BAAgB,mBAAmB,MAAM,MAAM;AAC/C,kBAAI,MAAM,MAAM,2BAA2B;AACzC,sBAAM,mBACJ,WAAM,MAAM,0BAA0B,qBAAtC,YAA0D;AAE5D,sBAAM,kBAAkB;AACxB,gCAAgB,0BAA0B;AAAA,kBACxC;AAAA,gBACF;AAAA,cACF;AAEA,8BAAgB,OAAO,MAAM,MAAM;AACnC,8BAAgB,cAAc,MAAM,MAAM;AAAA,YAC5C;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,iBAAI,iCAAQ,kBAAiB,MAAM;AACjC,6BAAe,0BAA0B,OAAO,aAAa;AAAA,YAC/D;AAEA,iBAAI,iCAAQ,SAAQ,MAAM;AACxB,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,OAAO,OAAO;AAAA,gBACd,IAAI,WAAW;AAAA,cACjB,CAAC;AAAA,YACH;AAAA,UACF;AAAA,UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA,kBAAkB;AAAA,gBAChB,YAAY;AAAA,kBACV,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;", "names": ["_AISDKError", "name", "marker", "symbol", "_a", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_TypeValidationError", "import_zod", "import_zod", "TypeValidationError", "validator", "getOriginalFetch", "getOriginalFetch", "APICallError", "APICallError", "APICallError", "d", "import_v4", "_a", "_a", "_b", "_c", "content", "import_v4", "import_v4", "_a", "toolCall", "import_v4", "_a"]}