hoistPattern:
  - '*'
hoistedDependencies:
  '@ai-sdk/gateway@1.0.3(zod@4.0.15)':
    '@ai-sdk/gateway': private
  '@ai-sdk/provider-utils@3.0.1(zod@4.0.15)':
    '@ai-sdk/provider-utils': private
  '@ai-sdk/provider@2.0.0':
    '@ai-sdk/provider': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  ai@5.0.6(zod@4.0.15):
    ai: private
  eventsource-parser@3.0.3:
    eventsource-parser: private
  json-schema@0.4.0:
    json-schema: private
  zod-to-json-schema@3.24.6(zod@4.0.15):
    zod-to-json-schema: private
  zod@4.0.15:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.4.1
pendingBuilds: []
prunedAt: Thu, 07 Aug 2025 13:17:01 GMT
publicHoistPattern: []
registries:
  default: http://ued.zuoyebang.cc/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
