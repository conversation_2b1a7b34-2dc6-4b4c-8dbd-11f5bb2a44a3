lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@openrouter/ai-sdk-provider':
        specifier: ^1.1.0
        version: 1.1.0(ai@5.0.6(zod@4.0.15))(zod@4.0.15)

packages:

  '@ai-sdk/gateway@1.0.3':
    resolution: {integrity: sha512-QRGz2vH1WR9NvCv8gWocoebAKiXcuqj22mug6i8COeVsp33x5K5cK2DT4TwiQx5SfYbqJbVoBT+UqnHF7A3PHA==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.25.76 || ^4

  '@ai-sdk/provider-utils@3.0.1':
    resolution: {integrity: sha512-/iP1sKc6UdJgGH98OCly7sWJKv+J9G47PnTjIj40IJMUQKwDrUMyf7zOOfRtPwSuNifYhSoJQ4s1WltI65gJ/g==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.25.76 || ^4

  '@ai-sdk/provider@2.0.0':
    resolution: {integrity: sha512-6o7Y2SeO9vFKB8lArHXehNuusnpddKPk7xqL7T2/b+OvXMRIXUO1rR4wcv1hAFUAT9avGZshty3Wlua/XA7TvA==}
    engines: {node: '>=18'}

  '@openrouter/ai-sdk-provider@1.1.0':
    resolution: {integrity: sha512-e5cW/KbgGakHOOsDhnHI6a0IDul9ER5J4QGM4yN9EfQ8XHfOFgwGGpLOopoRwkqaX5UdyQrpzei+1DPzg95i0A==}
    engines: {node: '>=18'}
    peerDependencies:
      ai: ^5.0.0
      zod: ^3.24.1 || ^v4

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==, tarball: http://ued.zuoyebang.cc:80/@opentelemetry%2fapi/-/api-1.9.0.tgz}
    engines: {node: '>=8.0.0'}

  '@standard-schema/spec@1.0.0':
    resolution: {integrity: sha512-m2bOd0f2RT9k8QJx1JN85cZYyH1RqFBdlwtkSlf4tBDYLCiiZnv1fIIwacK6cqwXavOydf0NPToMQgpKq+dVlA==}

  ai@5.0.6:
    resolution: {integrity: sha512-h643LlDEoUImDuu0v9oN52dHG5ck9sigVk/gjhYF+2x+TJtzTrMwQEUBKUOQjQ+Sb1ZFRKA3IxEYm5nUig0KzQ==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.25.76 || ^4

  eventsource-parser@3.0.3:
    resolution: {integrity: sha512-nVpZkTMM9rF6AQ9gPJpFsNAMt48wIzB5TQgiTLdHiuO8XEDhUgZEhqKlZWXbIzo9VmJ/HvysHqEaVeD5v9TPvA==}
    engines: {node: '>=20.0.0'}

  json-schema@0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=}

  zod-to-json-schema@3.24.6:
    resolution: {integrity: sha512-h/z3PKvcTcTetyjl1fkj79MHNEjm+HpD6NXheWjzOekY7kV+lwDYnHw+ivHkijnCSMz1yJaWBD9vu/Fcmk+vEg==}
    peerDependencies:
      zod: ^3.24.1

  zod@4.0.15:
    resolution: {integrity: sha512-2IVHb9h4Mt6+UXkyMs0XbfICUh1eUrlJJAOupBHUhLRnKkruawyDddYRCs0Eizt900ntIMk9/4RksYl+FgSpcQ==}

snapshots:

  '@ai-sdk/gateway@1.0.3(zod@4.0.15)':
    dependencies:
      '@ai-sdk/provider': 2.0.0
      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
      zod: 4.0.15

  '@ai-sdk/provider-utils@3.0.1(zod@4.0.15)':
    dependencies:
      '@ai-sdk/provider': 2.0.0
      '@standard-schema/spec': 1.0.0
      eventsource-parser: 3.0.3
      zod: 4.0.15
      zod-to-json-schema: 3.24.6(zod@4.0.15)

  '@ai-sdk/provider@2.0.0':
    dependencies:
      json-schema: 0.4.0

  '@openrouter/ai-sdk-provider@1.1.0(ai@5.0.6(zod@4.0.15))(zod@4.0.15)':
    dependencies:
      ai: 5.0.6(zod@4.0.15)
      zod: 4.0.15

  '@opentelemetry/api@1.9.0': {}

  '@standard-schema/spec@1.0.0': {}

  ai@5.0.6(zod@4.0.15):
    dependencies:
      '@ai-sdk/gateway': 1.0.3(zod@4.0.15)
      '@ai-sdk/provider': 2.0.0
      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
      '@opentelemetry/api': 1.9.0
      zod: 4.0.15

  eventsource-parser@3.0.3: {}

  json-schema@0.4.0: {}

  zod-to-json-schema@3.24.6(zod@4.0.15):
    dependencies:
      zod: 4.0.15

  zod@4.0.15: {}
