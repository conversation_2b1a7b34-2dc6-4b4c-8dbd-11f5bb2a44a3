<script setup lang="ts">
import { ref, computed } from 'vue'
import { generateRequirement, generateDesign, checkAIHealth } from './api/ai'

// 响应式数据
const userInput = ref('')
const isLoading = ref(false)
const currentStep = ref<'input' | 'requirement' | 'design'>('input')
const requirementDescription = ref('')
const designAndTasks = ref('')
const error = ref('')
const aiHealthy = ref(false)

// 计算属性
const canSubmit = computed(() => userInput.value.trim().length > 0 && !isLoading.value)
const canGenerateDesign = computed(() => requirementDescription.value.trim().length > 0 && !isLoading.value)

// 检查AI服务健康状态
const checkHealth = async () => {
  try {
    const response = await checkAIHealth()
    aiHealthy.value = response.success
  } catch (err) {
    aiHealthy.value = false
  }
}

// 生成需求描述
const handleGenerateRequirement = async () => {
  if (!canSubmit.value) return

  isLoading.value = true
  error.value = ''

  try {
    const response = await generateRequirement(userInput.value.trim())

    if (response.success && response.data) {
      requirementDescription.value = response.data.requirementDescription
      currentStep.value = 'requirement'
    } else {
      error.value = response.error || '生成需求描述失败'
    }
  } catch (err) {
    error.value = '网络错误，请检查服务器连接'
    console.error('Error generating requirement:', err)
  } finally {
    isLoading.value = false
  }
}

// 生成设计和任务
const handleGenerateDesign = async () => {
  if (!canGenerateDesign.value) return

  isLoading.value = true
  error.value = ''

  try {
    const response = await generateDesign(requirementDescription.value)

    if (response.success && response.data) {
      designAndTasks.value = response.data.designAndTasks
      currentStep.value = 'design'
    } else {
      error.value = response.error || '生成设计和任务失败'
    }
  } catch (err) {
    error.value = '网络错误，请检查服务器连接'
    console.error('Error generating design:', err)
  } finally {
    isLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  userInput.value = ''
  requirementDescription.value = ''
  designAndTasks.value = ''
  error.value = ''
  currentStep.value = 'input'
}

// 页面加载时检查健康状态
checkHealth()
</script>

<template>
  <div class="app-container">
    <header class="header">
      <h1>AI 需求分析与任务生成器</h1>
      <div class="health-status" :class="{ healthy: aiHealthy, unhealthy: !aiHealthy }">
        <span class="status-dot"></span>
        {{ aiHealthy ? 'AI服务正常' : 'AI服务异常' }}
      </div>
    </header>

    <main class="main-content">
      <!-- 步骤指示器 -->
      <div class="steps">
        <div class="step" :class="{ active: currentStep === 'input', completed: currentStep !== 'input' }">
          <span class="step-number">1</span>
          <span class="step-title">输入需求</span>
        </div>
        <div class="step" :class="{ active: currentStep === 'requirement', completed: currentStep === 'design' }">
          <span class="step-number">2</span>
          <span class="step-title">需求分析</span>
        </div>
        <div class="step" :class="{ active: currentStep === 'design' }">
          <span class="step-number">3</span>
          <span class="step-title">设计与任务</span>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="error-message">
        {{ error }}
      </div>

      <!-- 第一步：输入需求 -->
      <div v-if="currentStep === 'input'" class="step-content">
        <div class="input-section">
          <h2>请描述您的需求</h2>
          <textarea
            v-model="userInput"
            placeholder="请用自然语言描述您想要实现的功能，例如：我想要一个用户管理系统，可以添加、编辑、删除用户信息..."
            rows="6"
            :disabled="isLoading"
          ></textarea>
          <div class="button-group">
            <button
              @click="handleGenerateRequirement"
              :disabled="!canSubmit"
              class="primary-button"
            >
              {{ isLoading ? '生成中...' : '生成需求描述' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 第二步：需求描述 -->
      <div v-if="currentStep === 'requirement'" class="step-content">
        <div class="result-section">
          <h2>需求分析结果</h2>
          <div class="content-display">
            <pre>{{ requirementDescription }}</pre>
          </div>
          <div class="button-group">
            <button @click="resetForm" class="secondary-button">重新开始</button>
            <button
              @click="handleGenerateDesign"
              :disabled="!canGenerateDesign"
              class="primary-button"
            >
              {{ isLoading ? '生成中...' : '生成设计和任务' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 第三步：设计和任务 -->
      <div v-if="currentStep === 'design'" class="step-content">
        <div class="result-section">
          <h2>设计方案与任务列表</h2>
          <div class="content-display">
            <pre>{{ designAndTasks }}</pre>
          </div>
          <div class="button-group">
            <button @click="resetForm" class="secondary-button">重新开始</button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.header h1 {
  color: #1f2937;
  margin-bottom: 10px;
}

.health-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.health-status.healthy {
  background-color: #dcfce7;
  color: #166534;
}

.health-status.unhealthy {
  background-color: #fef2f2;
  color: #dc2626;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

.steps {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  gap: 40px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.step.active,
.step.completed {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s;
}

.step.active .step-number {
  background-color: #3b82f6;
  color: white;
}

.step.completed .step-number {
  background-color: #10b981;
  color: white;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.step.active .step-title,
.step.completed .step-title {
  color: #1f2937;
}

.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.step-content {
  background-color: #f9fafb;
  border-radius: 12px;
  padding: 30px;
}

.input-section h2,
.result-section h2 {
  color: #1f2937;
  margin-bottom: 20px;
  text-align: center;
}

.input-section textarea {
  width: 100%;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s;
}

.input-section textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.input-section textarea:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.content-display {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.content-display pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #374151;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
}

.primary-button,
.secondary-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.primary-button {
  background-color: #3b82f6;
  color: white;
}

.primary-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.primary-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.secondary-button {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.secondary-button:hover {
  background-color: #e5e7eb;
}

@media (max-width: 768px) {
  .app-container {
    padding: 16px;
  }

  .steps {
    gap: 20px;
  }

  .step-content {
    padding: 20px;
  }

  .button-group {
    flex-direction: column;
  }
}
</style>
