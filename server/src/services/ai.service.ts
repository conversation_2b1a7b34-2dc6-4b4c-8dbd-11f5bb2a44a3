import { generateText } from 'ai';
import { logger as pinoLogger } from '../utils/logger.js';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
const logger = pinoLogger();

// 配置OpenRouter的DeepSeek R1 Free模型
const openrouter = createOpenRouter({
  apiKey:
    '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',
});
const model = openrouter('deepseek/deepseek-chat-v3-0324:free');

/**
 * 将用户的自然语言输入转换为详细的需求描述
 * @param userInput 用户的自然语言输入
 * @returns 详细的需求描述
 */
export async function generateRequirementDescription(userInput: string): Promise<string> {
  try {
    logger.info({ userInput }, 'Processing user input for requirement generation');

    const prompt = `你是一个专业的前端产品需求分析师，专门为Vue3 + TypeScript + Composition API项目生成详细的开发需求。请将用户的简单自然语言描述转换为详细、具体、可执行的前端开发需求。

用户输入: "${userInput}"

请按照以下格式输出详细的前端开发需求描述：

## 功能概述
[简要描述功能的核心目标和用户价值]

## 页面/组件设计
### 页面结构
- [主要页面列表]
- [页面间的导航关系]
- [页面层级结构]

### 组件拆分
- [主要组件列表]
- [组件职责说明]
- [组件间的数据传递关系]

## 用户界面需求
### 布局设计
- [页面布局方式（如：响应式、固定宽度等）]
- [主要区域划分]
- [移动端适配要求]

### 交互设计
- [用户操作流程]
- [表单交互方式]
- [反馈机制（loading、成功、错误状态）]
- [动画效果要求]

### 视觉设计
- [UI风格要求（如：现代简约、卡片式等）]
- [色彩方案建议]
- [字体和间距规范]

## 技术实现需求
### Vue3技术栈
- [使用的Vue3特性（Composition API、响应式等）]
- [状态管理方案（Pinia/Vuex）]
- [路由配置需求]

### 数据处理
- [API接口需求]
- [数据结构定义]
- [数据验证规则]
- [缓存策略]

### 第三方依赖
- [UI组件库选择建议]
- [工具库需求]
- [插件需求]

## 开发任务拆解
### 核心功能模块
1. [模块1名称] - [具体实现要求]
2. [模块2名称] - [具体实现要求]
3. [模块3名称] - [具体实现要求]

### 开发优先级
- P0（核心功能）：[必须实现的功能]
- P1（重要功能）：[重要但可延后的功能]
- P2（优化功能）：[体验优化功能]

## 用户故事
作为 [用户角色]，我希望能够 [具体操作]，以便 [达成目标]。

## 验收标准
### 功能验收
1. [功能点1的验收条件]
2. [功能点2的验收条件]
3. [功能点3的验收条件]

### 性能验收
- [页面加载时间要求]
- [交互响应时间要求]
- [内存使用要求]

### 兼容性验收
- [浏览器兼容性要求]
- [设备兼容性要求]

## 开发注意事项
### 代码规范
- [Vue3 Composition API最佳实践]
- [TypeScript类型定义要求]
- [组件命名和文件组织规范]

### 性能优化
- [懒加载策略]
- [代码分割建议]
- [缓存优化方案]

### 可维护性
- [组件复用性考虑]
- [代码可读性要求]
- [测试覆盖要求]

请确保需求描述：
1. 具体明确，可直接指导Vue3开发
2. 考虑前端开发的技术特点和限制
3. 包含完整的用户体验设计
4. 提供清晰的开发任务拆解
5. 考虑性能和可维护性

现在请基于用户输入生成详细的前端开发需求描述：`;

    const result = await generateText({
      model: model,
      prompt,
      temperature: 0.7,
    });

    logger.info({ 
      inputLength: userInput.length, 
      outputLength: result.text.length 
    }, 'Successfully generated requirement description');

    return result.text;
  } catch (error) {
    logger.error({ error, userInput }, 'Failed to generate requirement description');
    throw new Error('Failed to generate requirement description');
  }
}

/**
 * 基于需求描述生成设计方案和具体任务
 * @param requirementDescription 详细的需求描述
 * @returns 设计方案和任务列表
 */
export async function generateDesignAndTasks(requirementDescription: string): Promise<string> {
  try {
    logger.info({ requirementLength: requirementDescription.length }, 'Processing requirement for design and task generation');

    const prompt = `你是一个资深的前端架构师和项目经理，专门负责将需求描述转换为具体的设计方案和开发任务。请基于提供的需求描述，生成详细的设计方案和可执行的开发任务列表。

需求描述:
${requirementDescription}

请按照以下格式输出设计方案和任务拆分：

## 技术架构设计

### 项目结构设计
\`\`\`
src/
├── components/          # 通用组件
│   ├── ui/             # 基础UI组件
│   └── business/       # 业务组件
├── views/              # 页面组件
├── composables/        # 组合式函数
├── stores/             # 状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── api/                # API接口
└── assets/             # 静态资源
\`\`\`

### 核心技术选型
- **框架**: Vue 3 + TypeScript + Vite
- **状态管理**: [推荐的状态管理方案]
- **UI组件库**: [推荐的UI库]
- **路由**: Vue Router 4
- **HTTP客户端**: [推荐的HTTP库]
- **样式方案**: [推荐的样式解决方案]

### 数据流设计
- [描述数据在应用中的流动方式]
- [状态管理策略]
- [API调用策略]

## 详细设计方案

### 页面设计
#### [页面1名称]
- **路由**: \`/[路由路径]\`
- **组件结构**: [描述页面的组件层次]
- **状态管理**: [页面级状态和全局状态]
- **API接口**: [需要调用的接口]

#### [页面2名称]
- **路由**: \`/[路由路径]\`
- **组件结构**: [描述页面的组件层次]
- **状态管理**: [页面级状态和全局状态]
- **API接口**: [需要调用的接口]

### 组件设计
#### [组件1名称]
- **功能**: [组件的主要功能]
- **Props**: [输入属性定义]
- **Events**: [输出事件定义]
- **插槽**: [插槽设计]

#### [组件2名称]
- **功能**: [组件的主要功能]
- **Props**: [输入属性定义]
- **Events**: [输出事件定义]
- **插槽**: [插槽设计]

### API接口设计
#### [接口1]
- **方法**: GET/POST/PUT/DELETE
- **路径**: \`/api/[路径]\`
- **参数**: [请求参数]
- **响应**: [响应数据结构]

#### [接口2]
- **方法**: GET/POST/PUT/DELETE
- **路径**: \`/api/[路径]\`
- **参数**: [请求参数]
- **响应**: [响应数据结构]

## 开发任务拆分

### 阶段一：项目基础搭建 (预计1-2天)
- [ ] **任务1.1**: 项目初始化和依赖安装
  - 创建Vue3项目结构
  - 安装必要依赖包
  - 配置TypeScript和ESLint
  - **预计时间**: 2小时
  - **负责人**: [开发者]
  - **验收标准**: 项目能正常启动，无编译错误

- [ ] **任务1.2**: 基础配置和工具设置
  - 配置路由结构
  - 设置状态管理
  - 配置API客户端
  - **预计时间**: 3小时
  - **负责人**: [开发者]
  - **验收标准**: 基础架构搭建完成

### 阶段二：核心功能开发 (预计3-5天)
- [ ] **任务2.1**: [具体功能模块1]
  - 实现[具体功能描述]
  - 创建相关组件
  - 实现API调用
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

- [ ] **任务2.2**: [具体功能模块2]
  - 实现[具体功能描述]
  - 创建相关组件
  - 实现API调用
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

### 阶段三：UI/UX优化 (预计2-3天)
- [ ] **任务3.1**: 界面美化和交互优化
  - 实现响应式设计
  - 添加动画效果
  - 优化用户体验
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

### 阶段四：测试和优化 (预计1-2天)
- [ ] **任务4.1**: 功能测试和bug修复
  - 单元测试编写
  - 集成测试
  - 性能优化
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

## 关键里程碑
1. **MVP版本** (第3天): 核心功能可用
2. **Beta版本** (第6天): 功能完整，待优化
3. **正式版本** (第8天): 完成测试，可上线

## 风险评估和应对
### 技术风险
- **风险**: [具体技术风险]
- **影响**: [风险影响评估]
- **应对**: [应对措施]

### 进度风险
- **风险**: [具体进度风险]
- **影响**: [风险影响评估]
- **应对**: [应对措施]

## 开发规范
### 代码规范
- 使用Vue3 Composition API
- 严格的TypeScript类型检查
- 统一的组件命名规范
- 清晰的文件组织结构

### Git工作流
- 功能分支开发
- 代码审查流程
- 提交信息规范

请确保设计方案：
1. 技术架构清晰合理
2. 任务拆分具体可执行
3. 时间估算相对准确
4. 考虑了技术风险和应对措施
5. 符合Vue3最佳实践

现在请基于需求描述生成详细的设计方案和任务拆分：`;

    const result = await generateText({
      model: model,
      prompt,
      temperature: 0.7,
    });

    logger.info({
      requirementLength: requirementDescription.length,
      outputLength: result.text.length
    }, 'Successfully generated design and tasks');

    return result.text;
  } catch (error) {
    logger.error({ error, requirementDescription }, 'Failed to generate design and tasks');
    throw new Error('Failed to generate design and tasks');
  }
}

/**
 * 基于设计方案生成具体的代码和依赖安装任务
 * @param designAndTasks 设计方案和任务描述
 * @returns 具体的代码生成和依赖安装指令
 */
export async function generateCodeAndDependencies(designAndTasks: string): Promise<string> {
  try {
    logger.info({ designLength: designAndTasks.length }, 'Processing design for code generation');

    const prompt = `你是一个资深的Vue3全栈开发工程师，专门负责将设计方案转换为具体的代码实现和依赖安装指令。请基于提供的设计方案，生成详细的代码文件和安装命令。

设计方案:
${designAndTasks}

请按照以下格式输出具体的实现方案：

## 依赖安装指令

### 生产依赖
\`\`\`bash
# 安装UI组件库和核心依赖
pnpm add element-plus @element-plus/icons-vue
pnpm add marked highlight.js dayjs
pnpm add pinia-plugin-persistedstate
pnpm add axios
\`\`\`

### 开发依赖
\`\`\`bash
# 安装开发工具和类型定义
pnpm add -D @types/marked
pnpm add -D unplugin-vue-components unplugin-auto-import
\`\`\`

## 项目配置文件

### vite.config.ts
\`\`\`typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': '/src',
    },
  },
})
\`\`\`

## 核心代码文件

### src/types/index.ts
\`\`\`typescript
export interface Article {
  id: string
  title: string
  content: string
  excerpt?: string
  createdAt: string
  updatedAt: string
  tags: string[]
  category?: string
  published: boolean
}

export interface User {
  id: string
  username: string
  email: string
  avatar?: string
}

export interface Comment {
  id: string
  articleId: string
  content: string
  author: string
  createdAt: string
  replies?: Comment[]
}
\`\`\`

### src/stores/auth.ts
\`\`\`typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string>('')

  const isLoggedIn = computed(() => !!user.value && !!token.value)

  const login = async (credentials: { username: string; password: string }) => {
    // TODO: 实现登录逻辑
    console.log('Login:', credentials)
  }

  const logout = () => {
    user.value = null
    token.value = ''
  }

  return {
    user,
    token,
    isLoggedIn,
    login,
    logout,
  }
}, {
  persist: true,
})
\`\`\`

### src/stores/articles.ts
\`\`\`typescript
import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Article } from '@/types'

export const useArticlesStore = defineStore('articles', () => {
  const articles = ref<Article[]>([])
  const currentArticle = ref<Article | null>(null)
  const loading = ref(false)

  const fetchArticles = async () => {
    loading.value = true
    try {
      // TODO: 实现API调用
      console.log('Fetching articles...')
    } finally {
      loading.value = false
    }
  }

  const fetchArticle = async (id: string) => {
    loading.value = true
    try {
      // TODO: 实现API调用
      console.log('Fetching article:', id)
    } finally {
      loading.value = false
    }
  }

  const createArticle = async (article: Omit<Article, 'id' | 'createdAt' | 'updatedAt'>) => {
    // TODO: 实现创建文章
    console.log('Creating article:', article)
  }

  return {
    articles,
    currentArticle,
    loading,
    fetchArticles,
    fetchArticle,
    createArticle,
  }
})
\`\`\`

### src/api/articles.ts
\`\`\`typescript
import axios from 'axios'
import type { Article } from '@/types'

const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
})

export const articlesApi = {
  // 获取文章列表
  getArticles: (params?: { page?: number; limit?: number; category?: string }) =>
    api.get<{ data: Article[]; total: number }>('/articles', { params }),

  // 获取文章详情
  getArticle: (id: string) =>
    api.get<Article>(\`/articles/\${id}\`),

  // 创建文章
  createArticle: (article: Omit<Article, 'id' | 'createdAt' | 'updatedAt'>) =>
    api.post<Article>('/articles', article),

  // 更新文章
  updateArticle: (id: string, article: Partial<Article>) =>
    api.put<Article>(\`/articles/\${id}\`, article),

  // 删除文章
  deleteArticle: (id: string) =>
    api.delete(\`/articles/\${id}\`),
}
\`\`\`

### src/components/ArticleCard.vue
\`\`\`vue
<template>
  <el-card class="article-card" shadow="hover" @click="handleClick">
    <div class="article-header">
      <h3 class="article-title">{{ article.title }}</h3>
      <div class="article-meta">
        <el-tag v-for="tag in article.tags" :key="tag" size="small">
          {{ tag }}
        </el-tag>
        <span class="article-date">{{ formatDate(article.createdAt) }}</span>
      </div>
    </div>
    <div class="article-excerpt">
      {{ article.excerpt || article.content.substring(0, 150) + '...' }}
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import type { Article } from '@/types'

interface Props {
  article: Article
}

const props = defineProps<Props>()
const router = useRouter()

const formatDate = (date: string) => dayjs(date).format('YYYY-MM-DD')

const handleClick = () => {
  router.push(\`/article/\${props.article.id}\`)
}
</script>

<style scoped>
.article-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.2s;
}

.article-card:hover {
  transform: translateY(-2px);
}

.article-title {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.article-date {
  color: #666;
  font-size: 14px;
}

.article-excerpt {
  color: #666;
  line-height: 1.6;
}
</style>
\`\`\`

### src/components/MarkdownEditor.vue
\`\`\`vue
<template>
  <div class="markdown-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button @click="togglePreview">
          {{ showPreview ? '编辑' : '预览' }}
        </el-button>
        <el-button @click="saveDraft">保存草稿</el-button>
      </el-button-group>
    </div>

    <div class="editor-content" :class="{ 'split-view': showPreview }">
      <div v-if="!showPreview || showPreview" class="editor-pane">
        <el-input
          v-model="content"
          type="textarea"
          :rows="20"
          placeholder="请输入Markdown内容..."
          @input="handleInput"
        />
      </div>

      <div v-if="showPreview" class="preview-pane">
        <div class="markdown-content" v-html="renderedContent"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'

interface Props {
  modelValue: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'save-draft'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const content = ref(props.modelValue)
const showPreview = ref(false)

// 配置marked
marked.setOptions({
  highlight: (code, lang) => {
    if (lang && hljs.getLanguage(lang)) {
      return hljs.highlight(code, { language: lang }).value
    }
    return hljs.highlightAuto(code).value
  },
})

const renderedContent = computed(() => marked(content.value))

const handleInput = () => {
  emit('update:modelValue', content.value)
}

const togglePreview = () => {
  showPreview.value = !showPreview.value
}

const saveDraft = () => {
  emit('save-draft')
}

watch(() => props.modelValue, (newValue) => {
  content.value = newValue
})
</script>

<style scoped>
.markdown-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.editor-toolbar {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.editor-content {
  display: flex;
}

.split-view .editor-pane,
.split-view .preview-pane {
  flex: 1;
}

.preview-pane {
  border-left: 1px solid #dcdfe6;
  padding: 20px;
  background-color: #fff;
}

.markdown-content {
  line-height: 1.6;
}
</style>
\`\`\`

## 路由配置

### src/router/index.ts
\`\`\`typescript
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
    },
    {
      path: '/article/:id',
      name: 'ArticleDetail',
      component: () => import('@/views/ArticleDetail.vue'),
    },
    {
      path: '/editor',
      name: 'ArticleEditor',
      component: () => import('@/views/ArticleEditor.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/editor/:id',
      name: 'ArticleEdit',
      component: () => import('@/views/ArticleEditor.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
    },
  ],
})

// 路由守卫
router.beforeEach((to) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    return '/login'
  }
})

export default router
\`\`\`

## 主要页面组件

### src/views/Home.vue
\`\`\`vue
<template>
  <div class="home">
    <div class="hero-section">
      <h1>我的博客</h1>
      <p>分享技术，记录生活</p>
    </div>

    <div class="articles-section">
      <div class="section-header">
        <h2>最新文章</h2>
        <el-button v-if="authStore.isLoggedIn" type="primary" @click="createArticle">
          写文章
        </el-button>
      </div>

      <div v-loading="articlesStore.loading" class="articles-list">
        <ArticleCard
          v-for="article in articlesStore.articles"
          :key="article.id"
          :article="article"
        />
      </div>

      <el-pagination
        v-if="total > pageSize"
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="prev, pager, next"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useArticlesStore } from '@/stores/articles'
import ArticleCard from '@/components/ArticleCard.vue'

const router = useRouter()
const authStore = useAuthStore()
const articlesStore = useArticlesStore()

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const createArticle = () => {
  router.push('/editor')
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadArticles()
}

const loadArticles = async () => {
  await articlesStore.fetchArticles()
}

onMounted(() => {
  loadArticles()
})
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.hero-section {
  text-align: center;
  padding: 60px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
  margin-bottom: 40px;
}

.hero-section h1 {
  font-size: 3em;
  margin-bottom: 10px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.articles-list {
  min-height: 400px;
}
</style>
\`\`\`

## 开发步骤总结

1. **环境准备**：执行依赖安装命令
2. **配置文件**：更新vite.config.ts和路由配置
3. **类型定义**：创建TypeScript接口
4. **状态管理**：实现Pinia stores
5. **API层**：创建API调用函数
6. **组件开发**：按优先级实现组件
7. **页面开发**：实现主要页面
8. **样式优化**：完善UI和响应式设计
9. **功能测试**：测试核心功能流程

请确保代码实现：
1. 严格遵循Vue3 Composition API最佳实践
2. 完整的TypeScript类型定义
3. 响应式设计适配
4. 性能优化考虑
5. 代码可维护性

现在请基于设计方案开始具体的代码实现：`;

    const result = await generateText({
      model: model,
      prompt,
      temperature: 0.3,
    });

    logger.info({
      designLength: designAndTasks.length,
      outputLength: result.text.length
    }, 'Successfully generated code and dependencies');

    return result.text;
  } catch (error) {
    logger.error({ error, designAndTasks }, 'Failed to generate code and dependencies');
    throw new Error('Failed to generate code and dependencies');
  }
}

/**
 * 健康检查AI服务连接
 */
export async function checkAIServiceHealth(): Promise<boolean> {
  try {
    const result = await generateText({
      model: model,
      prompt: 'Hello, please respond with "OK" if you can receive this message.',
    });

    logger.info({ response: result.text }, 'AI service health check completed');
    return result.text.toLowerCase().includes('ok');
  } catch (error) {
    logger.error({ error }, 'AI service health check failed');
    return false;
  }
}
