import { generateText } from 'ai';
import { logger as pinoLogger } from '../utils/logger.js';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
const logger = pinoLogger();

// 配置OpenRouter的DeepSeek R1 Free模型
const openrouter = createOpenRouter({
  apiKey:
    '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',
});
const model = openrouter('deepseek/deepseek-chat-v3-0324:free');

/**
 * 将用户的自然语言输入转换为详细的需求描述
 * @param userInput 用户的自然语言输入
 * @returns 详细的需求描述
 */
export async function generateRequirementDescription(userInput: string): Promise<string> {
  try {
    logger.info({ userInput }, 'Processing user input for requirement generation');

    const prompt = `你是一个专业的产品需求分析师。请将用户的简单自然语言描述转换为详细、具体、可执行的需求描述。

用户输入: "${userInput}"

请按照以下格式输出详细的需求描述：

## 功能概述
[简要描述功能的核心目标和价值]

## 详细需求
### 功能需求
1. [具体功能点1]
2. [具体功能点2]
3. [具体功能点3]
...

### 用户界面需求
- [界面布局要求]
- [交互方式要求]
- [视觉设计要求]

### 技术需求
- [技术实现要求]
- [性能要求]
- [兼容性要求]

### 数据需求
- [数据结构要求]
- [数据存储要求]
- [数据处理要求]

## 用户故事
作为 [用户角色]，我希望 [具体功能]，以便 [实现的价值]。

## 验收标准
1. [验收条件1]
2. [验收条件2]
3. [验收条件3]
...

## 注意事项
- [特殊考虑事项]
- [潜在风险点]
- [优先级说明]

请确保需求描述：
1. 具体明确，避免模糊表述
2. 可测试和验证
3. 技术可行
4. 用户友好
5. 考虑边界情况

现在请基于用户输入生成详细的需求描述：`;

    const result = await generateText({
      model: model,
      prompt,
      temperature: 0.7,
    });

    logger.info({ 
      inputLength: userInput.length, 
      outputLength: result.text.length 
    }, 'Successfully generated requirement description');

    return result.text;
  } catch (error) {
    logger.error({ error, userInput }, 'Failed to generate requirement description');
    throw new Error('Failed to generate requirement description');
  }
}

/**
 * 健康检查AI服务连接
 */
export async function checkAIServiceHealth(): Promise<boolean> {
  try {
    const result = await generateText({
      model: model,
      prompt: 'Hello, please respond with "OK" if you can receive this message.',
    });
    
    logger.info({ response: result.text }, 'AI service health check completed');
    return result.text.toLowerCase().includes('ok');
  } catch (error) {
    logger.error({ error }, 'AI service health check failed');
    return false;
  }
}
