import { generateText } from 'ai';
import { logger as pinoLogger } from '../utils/logger.js';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
const logger = pinoLogger();

// 配置OpenRouter的DeepSeek R1 Free模型
const openrouter = createOpenRouter({
  apiKey:
    '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',
});
const model = openrouter('deepseek/deepseek-chat-v3-0324:free');

/**
 * 将用户的自然语言输入转换为详细的需求描述
 * @param userInput 用户的自然语言输入
 * @returns 详细的需求描述
 */
export async function generateRequirementDescription(userInput: string): Promise<string> {
  try {
    logger.info({ userInput }, 'Processing user input for requirement generation');

    const prompt = `你是一个专业的前端产品需求分析师，专门为Vue3 + TypeScript + Composition API项目生成详细的开发需求。请将用户的简单自然语言描述转换为详细、具体、可执行的前端开发需求。

用户输入: "${userInput}"

请按照以下格式输出详细的前端开发需求描述：

## 功能概述
[简要描述功能的核心目标和用户价值]

## 页面/组件设计
### 页面结构
- [主要页面列表]
- [页面间的导航关系]
- [页面层级结构]

### 组件拆分
- [主要组件列表]
- [组件职责说明]
- [组件间的数据传递关系]

## 用户界面需求
### 布局设计
- [页面布局方式（如：响应式、固定宽度等）]
- [主要区域划分]
- [移动端适配要求]

### 交互设计
- [用户操作流程]
- [表单交互方式]
- [反馈机制（loading、成功、错误状态）]
- [动画效果要求]

### 视觉设计
- [UI风格要求（如：现代简约、卡片式等）]
- [色彩方案建议]
- [字体和间距规范]

## 技术实现需求
### Vue3技术栈
- [使用的Vue3特性（Composition API、响应式等）]
- [状态管理方案（Pinia/Vuex）]
- [路由配置需求]

### 数据处理
- [API接口需求]
- [数据结构定义]
- [数据验证规则]
- [缓存策略]

### 第三方依赖
- [UI组件库选择建议]
- [工具库需求]
- [插件需求]

## 开发任务拆解
### 核心功能模块
1. [模块1名称] - [具体实现要求]
2. [模块2名称] - [具体实现要求]
3. [模块3名称] - [具体实现要求]

### 开发优先级
- P0（核心功能）：[必须实现的功能]
- P1（重要功能）：[重要但可延后的功能]
- P2（优化功能）：[体验优化功能]

## 用户故事
作为 [用户角色]，我希望能够 [具体操作]，以便 [达成目标]。

## 验收标准
### 功能验收
1. [功能点1的验收条件]
2. [功能点2的验收条件]
3. [功能点3的验收条件]

### 性能验收
- [页面加载时间要求]
- [交互响应时间要求]
- [内存使用要求]

### 兼容性验收
- [浏览器兼容性要求]
- [设备兼容性要求]

## 开发注意事项
### 代码规范
- [Vue3 Composition API最佳实践]
- [TypeScript类型定义要求]
- [组件命名和文件组织规范]

### 性能优化
- [懒加载策略]
- [代码分割建议]
- [缓存优化方案]

### 可维护性
- [组件复用性考虑]
- [代码可读性要求]
- [测试覆盖要求]

请确保需求描述：
1. 具体明确，可直接指导Vue3开发
2. 考虑前端开发的技术特点和限制
3. 包含完整的用户体验设计
4. 提供清晰的开发任务拆解
5. 考虑性能和可维护性

现在请基于用户输入生成详细的前端开发需求描述：`;

    const result = await generateText({
      model: model,
      prompt,
      temperature: 0.7,
    });

    logger.info({ 
      inputLength: userInput.length, 
      outputLength: result.text.length 
    }, 'Successfully generated requirement description');

    return result.text;
  } catch (error) {
    logger.error({ error, userInput }, 'Failed to generate requirement description');
    throw new Error('Failed to generate requirement description');
  }
}

/**
 * 健康检查AI服务连接
 */
export async function checkAIServiceHealth(): Promise<boolean> {
  try {
    const result = await generateText({
      model: model,
      prompt: 'Hello, please respond with "OK" if you can receive this message.',
    });
    
    logger.info({ response: result.text }, 'AI service health check completed');
    return result.text.toLowerCase().includes('ok');
  } catch (error) {
    logger.error({ error }, 'AI service health check failed');
    return false;
  }
}
