import { nanoid } from 'nanoid';
import { logger } from '../utils/logger.js';

export type TaskStatus = 'pending' | 'running' | 'succeeded' | 'failed';

export interface TaskResult {
  zipPath?: string;
}

export interface TaskRecord<TSpec = unknown> {
  id: string;
  status: TaskStatus;
  message?: string;
  progress?: number;
  spec?: TSpec;
  result?: TaskResult;
  createdAt: number;
  updatedAt: number;
}

const tasks = new Map<string, TaskRecord>();

export function createTask<TSpec = unknown>(spec?: TSpec): TaskRecord<TSpec> {
  const id = nanoid();
  const now = Date.now();
  const task: TaskRecord<TSpec> = {
    id,
    status: 'pending',
    progress: 0,
    spec,
    createdAt: now,
    updatedAt: now,
  };
  tasks.set(id, task as TaskRecord);
  logger().info({ id }, 'Task created');
  return task;
}

export function getTask<TSpec = unknown>(id: string): TaskRecord<TSpec> | undefined {
  return tasks.get(id) as TaskRecord<TSpec> | undefined;
}

function patchTask(id: string, patch: Partial<TaskRecord>): TaskRecord | undefined {
  const t = tasks.get(id);
  if (!t) return undefined;
  const next = { ...t, ...patch, updatedAt: Date.now() };
  tasks.set(id, next);
  return next;
}

export function setTaskRunning(id: string) {
  const t = patchTask(id, { status: 'running' });
  if (t) logger().info({ id }, 'Task running');
}

export function setTaskSucceeded(id: string, result: TaskResult) {
  const t = patchTask(id, { status: 'succeeded', progress: 100, result });
  if (t) logger().info({ id, result }, 'Task succeeded');
}

export function setTaskFailed(id: string, message: string) {
  const t = patchTask(id, { status: 'failed', message });
  if (t) logger().error({ id, message }, 'Task failed');
}

export function setTaskProgress(id: string, progress: number, message?: string) {
  patchTask(id, { progress, message });
}