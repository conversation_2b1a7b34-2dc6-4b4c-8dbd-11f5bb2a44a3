import { mkdirSync, writeFileSync, createWriteStream, existsSync } from 'node:fs';
import { join } from 'node:path';
import archiver from 'archiver';
import { logger } from '../utils/logger.js';

type RouteItem = {
  path: string;
  name: string;
  title?: string;
};

export type ProjectSpecMvp = {
  name: string;
  preset: 'vue3-default';
  routes: RouteItem[];
};

/**
 * 运行生成器：基于最小 MVP 将 Vue3 工程必要文件输出到临时目录，并打包为 zip。
 * 返回 zip 文件绝对路径。
 */
export async function runGeneratorToZip(taskId: string, spec: ProjectSpecMvp): Promise<string> {
  const baseTmp = join(process.cwd(), '.tmp');
  const taskDir = join(baseTmp, `spec-${taskId}`);
  const outDir = join(taskDir, 'web', 'src');

  ensureDir(outDir);
  ensureDir(join(taskDir, 'web', 'src', 'router'));
  ensureDir(join(taskDir, 'web', 'src', 'views'));
  ensureDir(join(taskDir, 'web', 'src', 'api'));

  // http.ts
  const httpTs = `
// 基础 fetch 封装（可后续替换为 axios）
export interface HttpResponse<T> { data: T }

export async function httpGet<T>(url: string): Promise<HttpResponse<T>> {
  const res = await fetch(url);
  if (!res.ok) throw new Error(\`Request failed: \${res.status}\`);
  const data = (await res.json()) as T;
  return { data };
}
`.trimStart();
  writeFileSync(join(outDir, 'api', 'http.ts'), httpTs, 'utf8');

  // routes.ts（懒加载）
  const routesTs = `
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  ${spec.routes
    .map(
      (r) => `{
    path: '${r.path}',
    name: '${r.name}',
    component: () => import('../views/${r.name}.vue'),
    meta: { title: '${r.title ?? r.name}' }
  }`,
    )
    .join(',\n  ')}
];

export const router = createRouter({
  history: createWebHistory(),
  routes
});
`.trimStart();
  writeFileSync(join(outDir, 'router', 'index.ts'), routesTs, 'utf8');

  // 简单页面组件：为每个路由生成一个 Hello 页面
  for (const r of spec.routes) {
    const vueContent = `
<script setup lang="ts">
</script>

<template>
  <div class="page">
    <h1>${spec.name}: ${r.title ?? r.name}</h1>
    <p>Generated by SDD agent.</p>
  </div>
</template>

<style scoped>
.page { padding: 16px; }
</style>
`.trimStart();
    writeFileSync(join(outDir, 'views', `${r.name}.vue`), vueContent, 'utf8');
  }

  // 打包为 zip
  const zipPath = join(taskDir, `${taskId}.zip`);
  await zipDirectory(join(taskDir, 'web'), zipPath);
  logger().info({ zipPath }, 'ZIP generated');
  return zipPath;
}

function ensureDir(path: string) {
  if (!existsSync(path)) mkdirSync(path, { recursive: true });
}

async function zipDirectory(srcDir: string, zipPath: string): Promise<void> {
  await new Promise<void>((resolve, reject) => {
    const out = createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    out.on('close', () => resolve());
    archive.on('error', (err) => reject(err));

    archive.pipe(out);
    archive.directory(srcDir, false);
    archive.finalize();
  });
}