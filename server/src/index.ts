import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { logger as pinoLogger } from './utils/logger.js';

const app = new Hono();


// 404
app.notFound((c) => c.json({ error: 'Not Found' }, 404));

// error handling
app.onError((err, c) => {
  pinoLogger().error({ err }, 'Unhandled error');
  return c.json({ error: 'Internal Server Error' }, 500);
});

const port = Number(process.env.PORT || 3000);

serve({
  fetch: app.fetch,
  port,
});

pinoLogger().info({ port }, `Server listening on http://localhost:${port}`);
