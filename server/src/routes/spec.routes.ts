import { Hono } from 'hono';
import { z } from 'zod';
import { logger } from '../utils/logger.js';
import { createTask, getTask, setTaskFailed, setTaskRunning, setTaskSucceeded } from '../services/task.service.js';
import { runGeneratorToZip } from '../services/generator_orchestrator.js';

export const specRoutes = new Hono();

// 基础的 MVP 规格（可后续扩展）
const RouteSpec = z.object({
  path: z.string().min(1),
  name: z.string().min(1),
  title: z.string().optional(),
});

const ProjectSpec = z.object({
  name: z.string().min(1),
  preset: z.enum(['vue3-default']).default('vue3-default'),
  routes: z.array(RouteSpec).nonempty('至少需要一个路由'),
});

type TProjectSpec = z.infer<typeof ProjectSpec>;

specRoutes.post('/specs', async (c) => {
  try {
    const body = await c.req.json();
    const parsed = ProjectSpec.parse(body?.spec ?? body); // 兼容 {spec: {...}}
    const task = createTask(parsed);
    // 异步执行生成（不阻塞请求）
    queueMicrotask(async () => {
      try {
        setTaskRunning(task.id);
        const zipPath = await runGeneratorToZip(task.id, parsed);
        setTaskSucceeded(task.id, { zipPath });
      } catch (err) {
        logger().error({ err }, 'generator failed');
        setTaskFailed(task.id, err instanceof Error ? err.message : String(err));
      }
    });
    return c.json({ taskId: task.id });
  } catch (err) {
    return c.json({ error: err instanceof Error ? err.message : String(err) }, 400);
  }
});

specRoutes.get('/specs/:taskId', (c) => {
  const taskId = c.req.param('taskId');
  const task = getTask(taskId);
  if (!task) return c.json({ error: 'Task not found' }, 404);

  const resp: any = { status: task.status, message: task.message, progress: task.progress ?? 0 };
  if (task.status === 'succeeded' && task.result?.zipPath) {
    resp.downloadUrl = `/api/specs/${taskId}/download`;
  }
  return c.json(resp);
});

specRoutes.get('/specs/:taskId/download', async (c) => {
  const taskId = c.req.param('taskId');
  const task = getTask(taskId);
  if (!task) return c.json({ error: 'Task not found' }, 404);
  if (task.status !== 'succeeded' || !task.result?.zipPath) {
    return c.json({ error: 'Task not ready' }, 400);
  }

  const zipPath = task.result.zipPath;
  const fs = await import('node:fs');
  if (!fs.existsSync(zipPath)) {
    return c.json({ error: 'File not found' }, 404);
  }
  const stream = fs.createReadStream(zipPath);
  return new Response(stream as any, {
    headers: {
      'Content-Type': 'application/zip',
      'Content-Disposition': `attachment; filename="${taskId}.zip"`,
    },
  });
});