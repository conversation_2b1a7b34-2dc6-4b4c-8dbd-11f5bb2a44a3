import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { generateRequirementDescription, generateDesignAndTasks, checkAIServiceHealth } from '../services/ai.service.js';
import { logger as pinoLogger } from '../utils/logger.js';

const logger = pinoLogger();
const aiRoutes = new Hono();

// 请求体验证schema
const generateRequirementSchema = z.object({
  input: z.string().min(1, 'User input cannot be empty').max(1000, 'User input too long'),
});

const generateDesignSchema = z.object({
  requirementDescription: z.string().min(1, 'Requirement description cannot be empty').max(10000, 'Requirement description too long'),
});

/**
 * POST /ai/generate-requirement
 * 将用户的自然语言输入转换为详细的需求描述
 */
aiRoutes.post(
  '/generate-requirement',
  zValidator('json', generateRequirementSchema),
  async (c) => {
    try {
      const { input } = c.req.valid('json');
      
      logger.info({ input }, 'Received requirement generation request');

      const requirementDescription = await generateRequirementDescription(input);

      return c.json({
        success: true,
        data: {
          input,
          requirementDescription,
          generatedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error({ error }, 'Failed to generate requirement description');
      
      return c.json({
        success: false,
        error: 'Failed to generate requirement description',
        message: error instanceof Error ? error.message : 'Unknown error',
      }, 500);
    }
  }
);

/**
 * POST /ai/generate-design
 * 基于需求描述生成设计方案和具体任务
 */
aiRoutes.post(
  '/generate-design',
  zValidator('json', generateDesignSchema),
  async (c) => {
    try {
      const { requirementDescription } = c.req.valid('json');

      logger.info({ requirementLength: requirementDescription.length }, 'Received design generation request');

      const designAndTasks = await generateDesignAndTasks(requirementDescription);

      return c.json({
        success: true,
        data: {
          requirementDescription,
          designAndTasks,
          generatedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error({ error }, 'Failed to generate design and tasks');

      return c.json({
        success: false,
        error: 'Failed to generate design and tasks',
        message: error instanceof Error ? error.message : 'Unknown error',
      }, 500);
    }
  }
);

/**
 * GET /ai/health
 * 检查AI服务健康状态
 */
aiRoutes.get('/health', async (c) => {
  try {
    const isHealthy = await checkAIServiceHealth();
    
    return c.json({
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error({ error }, 'AI health check failed');
    
    return c.json({
      success: false,
      error: 'Health check failed',
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
      },
    }, 500);
  }
});

/**
 * GET /ai/info
 * 获取AI服务信息
 */
aiRoutes.get('/info', (c) => {
  return c.json({
    success: true,
    data: {
      model: 'deepseek/deepseek-r1',
      provider: 'OpenRouter',
      description: 'AI service for generating detailed requirement descriptions from natural language input',
      endpoints: [
        {
          method: 'POST',
          path: '/ai/generate-requirement',
          description: 'Generate detailed requirement description from user input',
        },
        {
          method: 'POST',
          path: '/ai/generate-design',
          description: 'Generate design and task breakdown from requirement description',
        },
        {
          method: 'GET',
          path: '/ai/health',
          description: 'Check AI service health status',
        },
        {
          method: 'GET',
          path: '/ai/info',
          description: 'Get AI service information',
        },
      ],
    },
  });
});

export { aiRoutes };
